import React, { useState, useEffect, useRef } from 'react';
import '../../gx-phone-react.css';

/**
 * CountrySelector - Component for selecting a country from a dropdown
 * 
 * @param {Object} props - Component props
 * @param {Array} props.countries - Array of country objects
 * @param {Object} props.selectedCountry - Currently selected country
 * @param {Function} props.onSelect - Callback when a country is selected
 * @param {boolean} props.disabled - Whether the selector is disabled
 * @param {boolean} props.isLoading - Whether countries are loading
 * @param {boolean} props.isOpen - Whether the dropdown is open
 * @param {Function} props.onToggle - Callback when dropdown is toggled
 * @returns {JSX.Element}
 */
const CountrySelector = ({
  countries = [],
  selectedCountry = null,
  onSelect,
  disabled = false,
  isLoading = false,
  isOpen = false,
  onToggle,
}) => {
  const [countriesFilter, setCountriesFilter] = useState('');
  const [filteredCountries, setFilteredCountries] = useState(countries);
  const searchInputRef = useRef(null);
  
  // Filter countries when search term changes or countries array updates
  useEffect(() => {
    if (countries.length > 0) {
      if (!countriesFilter) {
        setFilteredCountries(countries);
      } else {
        const filter = countriesFilter.toLowerCase();
        const filtered = countries.filter(
          country => 
            country.en.toLowerCase().includes(filter) || 
            String(country.code).includes(filter) ||
            (country.ru && country.ru.toLowerCase().includes(filter))
        );
        setFilteredCountries(filtered);
      }
    }
  }, [countries, countriesFilter]);
  
  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current.focus();
      }, 50);
    }
  }, [isOpen]);
  
  // Reset filter when dropdown closes
  useEffect(() => {
    if (!isOpen) {
      setCountriesFilter('');
    }
  }, [isOpen]);
  
  // Handle search input change
  const handleSearchChange = (e) => {
    setCountriesFilter(e.target.value);
  };
  
  // Handle search input keyboard navigation
  const handleSearchKeyDown = (e) => {
    if (e.key === 'Escape') {
      onToggle(false);
      setCountriesFilter('');
    } else if (e.key === 'ArrowDown' && filteredCountries.length > 0) {
      // Focus on the first country item
      const firstCountryItem = document.querySelector('.gx-country-dropdown-item');
      if (firstCountryItem) {
        firstCountryItem.focus();
      }
    }
  };
  
  // Handle country selection
  const handleCountrySelect = (country) => {
    onSelect(country);
    onToggle(false);
    setCountriesFilter('');
  };
  
  // Handle country item keyboard navigation
  const handleCountryKeyDown = (e, index, country) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleCountrySelect(country);
    } else if (e.key === 'Escape') {
      onToggle(false);
      setCountriesFilter('');
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (index === 0) {
        // If first item, focus back on search input
        searchInputRef.current?.focus();
      } else {
        // Focus on previous item
        const prevItem = document.querySelectorAll('.gx-country-dropdown-item')[index - 1];
        if (prevItem) {
          prevItem.focus();
        }
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      // Focus on next item
      const nextItem = document.querySelectorAll('.gx-country-dropdown-item')[index + 1];
      if (nextItem) {
        nextItem.focus();
      }
    }
  };
  
  return (
    <>
      <button 
        type="button"
        className="gx-country-selector-button" 
        onClick={() => onToggle(!isOpen)}
        disabled={disabled || isLoading}
      >
        {selectedCountry ? (
          <>
            <img 
              src={selectedCountry.icon} 
              alt={selectedCountry.en} 
              className="gx-country-flag"
            />
            <span className="gx-country-code">+{selectedCountry.code}</span>
          </>
        ) : (
          <span className="gx-country-code">+</span>
        )}
      </button>
      
      {isOpen && (
        <div className="gx-country-dropdown">
          <div className="gx-country-search-container">
            <input
              ref={searchInputRef}
              type="text"
              className="gx-country-search-input"
              placeholder="Поиск страны..."
              value={countriesFilter}
              onChange={handleSearchChange}
              onKeyDown={handleSearchKeyDown}
            />
          </div>
          
          <div className="gx-country-list">
            {filteredCountries.length > 0 ? (
              filteredCountries.map((country, index) => (
                <button
                  key={country.iso}
                  className={`gx-country-dropdown-item ${selectedCountry?.iso === country.iso ? 'selected' : ''}`}
                  onClick={() => handleCountrySelect(country)}
                  onKeyDown={(e) => handleCountryKeyDown(e, index, country)}
                  tabIndex={0}
                >
                  <img 
                    src={country.icon} 
                    alt={country.en} 
                    className="gx-country-flag"
                  />
                  <span className="gx-country-name">
                    {country.ru || country.en} (+{country.code})
                  </span>
                </button>
              ))
            ) : (
              <div className="gx-country-empty">Страны не найдены</div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default CountrySelector;