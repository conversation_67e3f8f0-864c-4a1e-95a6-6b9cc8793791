import { useState, useEffect } from 'react';

export interface Mask {
  e164: string;
  international: string;
  national: string;
  local: string;
}

export interface Country {
  en: string;
  ru: string;
  iso: string;
  icon: string;
  code: number;
  mask: Mask;
}

/**
 * Hook to fetch and manage country data for phone input
 * 
 * @param {string} apiUrl - API URL for the countries endpoint
 * @returns {{countries: Country[], defaultCountry: Country | null, isLoading: boolean, error: string | null}} - Countries data and loading state
 */
export const useCountries = (apiUrl = 'https://phone.gexarus.com/api') => {
  const [countries, setCountries] = useState<Country[]>([]);
  const [defaultCountry, setDefaultCountry] = useState<Country | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`${apiUrl}/Country/list`);
        const data = await response.json();
        
        if (data.success) {
          setCountries(data.countries);
          setDefaultCountry(data.default);
        } else {
          throw new Error(data.err || 'Failed to fetch countries');
        }
      } catch (error) {
        console.error('[useCountries] Error fetching countries:', error);
        setError(error.message);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchCountries();
  }, [apiUrl]);
  
  return { 
    countries, 
    defaultCountry, 
    isLoading, 
    error 
  };
};

export default useCountries;
