// src/pages/FavoritesPage.tsx
// NOTE: Temporary location. Ideally, this would be in src/pages/favorites-page/ui/FavoritesPage.tsx
import React, { useEffect } from 'react'; // Added React for React.FC

import { FavDetail } from '@components/Favorites/FavDetail'; // Path to be updated later
// import { FavSidebar } from '@components/Favorites/FavSidebar'; // Path to be updated later
import ErrorEmpty from '@/shared/ui/ErrorEmpty'; // Updated path from helpers/Errors/ErrorEmpty.jsx
import { useSelector } from 'react-redux';

import { useAuthContext } from '@/entities/user/model/AuthContext';
import { useFavorites } from '@/features/favorite';
import { scrollToTop } from '@/shared/lib/scrollToTop';
import { Breadcrumbs } from '@/widgets/breadcrumbs';
import { CategorySwitcher, useCategorySwitcher } from '@/widgets/category-switcher';

import type { RootState } from '@/app/providers/store';

const FavoritesPage: React.FC = () => { // Renamed from Favorites and typed as React.FC
  const { isAuthenticated } = useAuthContext();
  const {
    favorites: allFavorites,
    isLoading,
    isError: favoritesIsError,
    refreshFromServer,
  } = useFavorites();

  const { categories } = useSelector(
    (state: RootState) => state.favorite
  );

  const {
    selectedCategory,
    filteredProducts: filteredFavorites,
    handleCategoryChange,
    isUpdating
  } = useCategorySwitcher(categories, allFavorites);
  
  const isPageLoading = isLoading || isUpdating;

  useEffect(() => {
    scrollToTop();
  }, []);

  useEffect(() => {
    // console.log('[FavoritesPage.tsx] useEffect for refreshFromServer triggered. isAuthenticated:', isAuthenticated); // Log kept for now
    if (isAuthenticated) {
      void refreshFromServer();
    }
  }, [isAuthenticated, refreshFromServer]);

  if (favoritesIsError) {
    console.error('Error loading favorites page data.');
  }

  return (
    <div className="content pb-6">
      <Breadcrumbs />
      <h1 className="block text-2xl md:text-[40px] font-semibold text-colBlack pb-5">
        Избранное
      </h1>
      {isPageLoading ? (
        <p>Loading categories...</p>
      ) : !isPageLoading && categories?.length > 0 ? (
        <CategorySwitcher
          categories={categories}
          selectedCategory={selectedCategory}
          onCategoryChange={handleCategoryChange}
        />
      ) : null}
      {isPageLoading ? (
        <p>Loading favorites...</p>
      ) : !isPageLoading &&
        isAuthenticated &&
        filteredFavorites &&
        filteredFavorites.length > 0 ? (
        <div className="md:flex">
          {/* <FavSidebar
            favorite={serverDataForCategories} // This variable seems undefined, might be a remnant
            selectedFilter={{ type: 'category', filter: selectedCategory }} // selectedFilter also seems undefined
            setSelectedFilter={handleCategoryChange} // setSelectedFilter also seems undefined/misused
          /> */}
          <FavDetail favorite={filteredFavorites} user={isAuthenticated} />
        </div>
      ) : !isAuthenticated &&
        filteredFavorites &&
        filteredFavorites.length > 0 ? (
        <div className="md:flex">
          <FavDetail favorite={filteredFavorites} user={isAuthenticated} />
        </div>
      ) : !isPageLoading &&
        !favoritesIsError &&
        (!filteredFavorites || filteredFavorites.length === 0) ? (
        <ErrorEmpty
          title="Еще не готовы к покупке?"
          desc="Добавляйте понравившийся товар в избранное, чтобы не потерять его."
          height="420px"
        />
      ) : null}
    </div>
  );
};

export default FavoritesPage; // Renamed export
