import { useCallback, useEffect, useRef, useState } from 'react';

import { useGetFiltersNewMutation } from '@/entities/filter';
import { useGetVariantsTestMutation } from '@/entities/product';
import { getFiltersFromUrlUpdate } from '@/shared/lib/getFiltersFromUrl';
import { ProductFilter as IProductFilter } from '@/types/Filters/ProductFilter';

import {
  buildQueryParams,
  buildServerFilter
} from '@/shared/lib/catalog-filter/catalog-filter.utils';
import { modalFilterItemObserver } from '@/shared/lib/observer';
import { bodyScrollLockSingleObserver } from '@/shared/lib/observer/body.observer';
import { updateFilterState } from '@/shared/lib/queryUtils';
import { OrderByType, SortOrderType } from '@/types/Filters/Sort';
import { useLocation, useNavigate } from 'react-router-dom';
import { ProductFilterURLInput } from '../CatalogRoot';
import { useSort } from './useSort';

export interface CategoryChain {
  id: number;
  image: false | { name: string; tiny: string }[];
  name: string;
  slug: string;
}

interface Category {
  count: number;
  chain: CategoryChain[];
}

export const useSearchTest = () => {
  const { handleSortChange, currentSort, sortBy } = useSort();

  const [filters, setFilters] = useState<IProductFilter[]>([]);
  const [filtersLoading, setFiltersLoading] = useState(false);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [products, setProducts] = useState<any[]>([]);
  const [productsPage, setProductsPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);

  const [categories, setCategories] = useState<Category[]>([]);

  const [fetchFiltersProducts] = useGetVariantsTestMutation();
  const [fetchFilters] = useGetFiltersNewMutation();

  const observer = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  const cardView = localStorage.getItem('cardView');
  const [cardType, setTypeCard] = useState(cardView ? cardView : 'tile');

  const location = useLocation();
  const navigate = useNavigate();
  const prevQ = useRef<string | null>(null);

  const getQParam = (param = 'q') => {
    const params = new URLSearchParams(location.search);
    return params.get(param);
  };

  const firstFetchFilters = () => {
    const { filters } = getFiltersFromUrlUpdate();

    const { search, orderBy, sortOrder, category_id, ...filtersUpdate } = {
      search: '',
      orderBy: '',
      sortOrder: '',
      category_id: '',
      ...filters
    };

    const res = async () => {
      setFiltersLoading(true);
      setIsLoadingProducts(true);
      const r = await fetchFilters({
        category_id,
        filters: filtersUpdate,
        search,
        source: search
      });
      const p = await fetchFiltersProducts({
        search,
        filters: filtersUpdate,
        category_id,
        limit: 20,
        page: 1,
        orderBy: currentSort.orderBy,
        sortOrder: currentSort.sortOrder,
        source: search
      });
      if ('data' in p) {
        setProducts(p.data.data);
        setProductsPage(2);
      }
      if ('data' in r) {
        setFilters(r.data.data);
        if ('categories' in r.data) {
          setCategories(r.data.categories);
        }
        setFiltersLoading(false);
        setIsLoadingProducts(false);
      }
    };
    res();
  };

  const onSortChange = async (value: any) => {
    handleSortChange(value);
    setProductsPage(1);
    const [orderBy, sortOrder] = value.split('-') as [OrderByType, SortOrderType];

    const selectedSort = sortBy.find(
      (sort) => sort.orderBy === orderBy && sort.sortOrder === sortOrder
    );

    if (selectedSort) {
      setIsLoadingProducts(true);

      const { filters } = getFiltersFromUrlUpdate();
      const {
        search,
        orderBy: ob,
        sortOrder: so,
        category_id,
        ...filtersUpdate
      } = {
        search: '',
        orderBy: '',
        sortOrder: '',
        category_id: '',
        ...filters
      };

      try {
        const resultProducts = await fetchFiltersProducts({
          filters: filtersUpdate,
          search,
          category_id,
          limit: 20,
          page: 1,
          orderBy: selectedSort.orderBy,
          sortOrder: selectedSort.sortOrder,
          source: search
        });
        if ('data' in resultProducts) {
          setProducts(resultProducts.data.data);
          setIsLoadingProducts(false);
        }
      } catch (error) {
        console.log(error);
      }
    }
  };

  const onChangeFilter = async (input: ProductFilterURLInput) => {
    setFiltersLoading(true);
    setHasMore(true);
    setIsLoadingProducts(true);
    setProductsPage(1);

    const filtersArray = 'alone' in input ? [input.alone] : input.multiple;

    let { filterParams: queryParamsToFilterData } = getFiltersFromUrlUpdate();
    const {
      q: search,
      orderBy,
      sortOrder,
      category_id,
      ...other
    } = {
      q: '',
      orderBy: '',
      sortOrder: '',
      category_id: '',
      ...queryParamsToFilterData
    };

    let otherFilters = other;

    filtersArray.forEach((filter) => {
      otherFilters = updateFilterState(other, filter);
    });

    const dataToFilterData = otherFilters;

    const queryParams = buildQueryParams({ q: search, category_id, ...dataToFilterData });

    const filterDataToServerData = buildServerFilter(dataToFilterData);

    const res = async () => {
      const r = await fetchFilters({
        category_id,
        filters: filterDataToServerData,
        search,
        source: search
      });
      if ('data' in r) {
        setFilters(r.data.data);
        return r.data.data;
      }
    };
    res();
    const p = await fetchFiltersProducts({
      filters: filterDataToServerData,
      category_id,
      search,
      limit: 20,
      page: 1,
      orderBy,
      sortOrder,
      source: search
    });
    if ('data' in p) {
      setProducts(p.data.data);
      setIsLoadingProducts(false);
      setProductsPage((prev) => prev + 1);
    }
    navigate(`?${queryParams.join('&')}`, { replace: true });

    setFiltersLoading(false);
  };

  const onResetFilters = () => {
    const { filters } = getFiltersFromUrlUpdate();
    const { search, orderBy, sortOrder, category_id } = {
      search: '',
      orderBy: '',
      sortOrder: '',
      category_id: '',
      ...filters
    };

    const res = async () => {
      setFiltersLoading(true);
      setIsLoadingProducts(true);
      const r = await fetchFilters({
        category_id,
        filters: {},
        search
      });
      const p = await fetchFiltersProducts({
        search,
        filters: {},
        category_id,
        limit: 20,
        page: 1,
        orderBy,
        sortOrder
      });
      if ('data' in p) {
        setProducts(p.data.data);
        setProductsPage(2);
      }
      if ('data' in r) {
        setFilters([]);
        setFilters(r.data.data);
        setCategories(r.data.categories);
        setFiltersLoading(false);
        setIsLoadingProducts(false);
      }
    };
    res();
  };
  const handleOpenModalAllFilters = () => {
    bodyScrollLockSingleObserver.setValue(true);
    const modalId = modalFilterItemObserver.addObserver({
      type: 'search',
      data: filters,
      onAccept: async (arr, params) => {
        const { filterParams: filterData } = getFiltersFromUrlUpdate();
        const {
          q: search,
          orderBy,
          sortOrder,
          category_id,
          ...other
        } = {
          q: '',
          orderBy: '',
          sortOrder: '',
          category_id: '',
          ...filterData
        };
        setFilters(arr);
        const updateParams = buildQueryParams({ q: search, category_id, ...params });
        navigate(`?${updateParams.join('&')}`, { replace: true });
        bodyScrollLockSingleObserver.setValue(false);
        const filterDataToServerData = buildServerFilter(other);
        const p = await fetchFiltersProducts({
          search,
          filters: filterDataToServerData,
          category_id,
          limit: 20,
          page: 1,
          orderBy,
          sortOrder,
          source: search
        });
        if ('data' in p) {
          setProducts(p.data.data);
          setIsLoadingProducts(false);
        }

        modalFilterItemObserver.dissmissObserver(modalId);
      },
      onCancel: () => {
        modalFilterItemObserver.dissmissObserver(modalId);
        setTimeout(() => {
          bodyScrollLockSingleObserver.setValue(false);
        }, 300);
      }
    });
  };

  const onCategory = async (str: string | null) => {
    const { filterParams } = getFiltersFromUrlUpdate();
    if (str !== null) {
      filterParams.category_id = str;
    } else {
      delete filterParams.category_id;
    }

    const build = buildQueryParams(filterParams);
    navigate(`?${build.join('&')}`, { replace: true });
    firstFetchFilters();
  };

  const loadProducts = useCallback(async () => {
    if (isLoadingProducts || !hasMore) return;

    setIsLoadingProducts(true);

    try {
      const { filters } = getFiltersFromUrlUpdate();

      const { search, category_id, ...other } = { search: '', category_id: '', ...filters };

      const p = await fetchFiltersProducts({
        search,
        filters: other,
        category_id,
        limit: 20,
        page: productsPage,
        orderBy: currentSort.orderBy,
        sortOrder: currentSort.sortOrder
      });
      if ('data' in p) {
        setProducts((prev) => [...prev, ...p.data.data]);
        setIsLoadingProducts(false);
        setProductsPage(productsPage + 1);
        setHasMore(p.data.data.length > 0);
      }
    } catch (error) {
      console.error(error);
      setIsLoadingProducts(false);
    }
  }, [isLoadingProducts, productsPage, hasMore]);

  const [isVisible, setIsVisible] = useState(false);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const sentinelRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(!entry.isIntersecting);
      },
      { threshold: 0 }
    );

    if (sentinelRef.current) {
      observerRef.current.observe(sentinelRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [sentinelRef.current]);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'instant'
    });
  };

  useEffect(() => {
    const currentQ = getQParam();
    if (prevQ.current !== currentQ) {
      prevQ.current = currentQ;
      firstFetchFilters();
    }
  }, [location.search]);

  useEffect(() => {
    observer.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoadingProducts) {
          loadProducts();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observer.current.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current && observer.current) {
        observer.current.unobserve(loadMoreRef.current);
      }
    };
  }, [loadProducts, hasMore, isLoadingProducts]);

  return {
    filters,
    filtersLoading,
    isLoadingProducts,
    products,
    productsPage,
    onChangeFilter,
    onResetFilters,
    handleOpenModalAllFilters,
    currentSort,
    handleSortChange: onSortChange,
    sortBy,
    cardType,
    setTypeCard,
    categories,
    onCategory,
    prevQ,
    hasMore,
    loadMoreRef,
    sentinelRef,
    isVisible,
    scrollToTop
  };
};
