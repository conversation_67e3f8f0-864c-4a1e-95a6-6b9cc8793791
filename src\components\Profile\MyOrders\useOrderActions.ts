import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

import {
  useCancelOrderMutation,
  useRepeatOrderMutation,
  useGetOrderPDFMutation,
} from '@/entities/order';
import { useModal } from '@/features/modals';

export const useOrderActions = (orderNumber: string) => {
  const navigate = useNavigate();
  const [cancelOrder] = useCancelOrderMutation();
  const [repeatOrder] = useRepeatOrderMutation();
  const [getOrderPDF] = useGetOrderPDFMutation();
  const { showModal } = useModal();
  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

  const handleCancelClick = async () => {
    try {
      await cancelOrder({
        order_number: orderNumber,
        reason: 'User requested cancellation',
      }).unwrap();
    } catch (err: unknown) {
      console.error('Error canceling order:', err);
    }
  };

  const handleRepeatClick = async () => {
    try {
      const result = await repeatOrder({
        order_number: orderNumber,
      }).unwrap();
      if (result.success === 'ok') {
        navigate('/profile/orders/');
        toast.success('Заказ успешно повторен');
      }
    } catch (err: unknown) {
      console.error('Error repeating order:', err);
      showModal({
        type: 'confirmation',
        title: 'Ошибка',
        text: 'Не удалось повторить заказ. Пожалуйста, попробуйте позже.',
        action: () => {},
      });
    }
  };

  const handleDownloadClick = async () => {
    try {
      const result = await getOrderPDF({
        order_number: orderNumber,
      }).unwrap();
      if (result.success === 'ok' && result.file) {
        const fullUrl = new URL(result.file.url, API_BASE_URL).toString();
        window.open(fullUrl, '_blank');
        // const response = await fetch(fullUrl);
        // const blob = await response.blob();
        // const url = window.URL.createObjectURL(blob);

        // const link = document.createElement('a');
        // link.href = url;
        // link.download = `order-${orderNumber}.pdf`;
        // document.body.appendChild(link);
        // link.click();
        // document.body.removeChild(link);
        // window.URL.revokeObjectURL(url);
      }
    } catch (err: unknown) {
      console.error('Error downloading PDF:', err);
      // showModal({
      //   type: 'confirmation',
      //   title: 'Ошибка',
      //   text: 'Не удалось скачать PDF. Пожалуйста, попробуйте позже.',
      //   action: () => {},
      // });
    }
  };

  return {
    handleCancelClick,
    handleRepeatClick,
    handleDownloadClick,
  };
};
