# Feature: Cart Share

## Purpose

This feature allows users to generate a unique link representing their currently selected cart items, which can then be shared with others. Visiting this link allows another user (or the same user in a different session) to view the shared items and potentially add them to their own cart.

## Key Functionality

*   **Code Generation:** Generates a unique code representing the selected items in the current user's cart (`getCartShareCode` mutation).
*   **Fetching Shared Cart:** Retrieves the list of products associated with a specific share code (`getCartShareItemsByCode` mutation).
*   **Adding Shared Cart:** Adds the items from a shared cart (identified by code) to the current user's active cart (`addSharedCart` mutation).
*   **UI:** Provides modals for initiating the sharing process (`ShareCartModal`) and displaying a received shared cart (`ShowSharedCartModal`).

## Components (`ui/`)

*   **Modals (`ui/modals/`):**
    *   `ShareCartModal.tsx`: Triggered by the user to share their cart. Fetches the share code, displays the selected items, provides the shareable URL, and includes social sharing buttons (using `react-share`).
    *   `ShowSharedCartModal.tsx`: Triggered automatically when a user visits a URL containing a `?cart=<code>` query parameter. Fetches and displays the items from the shared cart and provides a button to add them to the current user's cart.

## State & Logic

*   This feature primarily relies on API interactions and UI state within its modals.
*   It interacts with the `cart` feature's state (`useCartSelection`) to determine which items to share.
*   It uses the `modals` feature (`useModal`) to display its UI.

## API Integration (`api/`)

*   **`cartShareApi.ts`:** Defines RTK Query endpoints:
    *   `getCartShareCode`: Mutation (called by `ShareCartModal`) to request a share code from the backend based on the currently selected cart items (selection likely happens server-side based on the user's session/token).
    *   `getCartShareItemsByCode`: Mutation (used like a query by `ShowSharedCartModal`) to fetch the product details associated with a given share code.
    *   `addSharedCart`: Mutation (called by `ShowSharedCartModal`) to add the items from a shared code to the current user's cart. Invalidates `Cart` and `User` data tags.

## Usage

1.  **Sharing:** The user clicks a "Share Cart" button (likely in the main `CartDetail` component). This triggers the `ShareCartModal` via `useModal`. The modal calls `getCartShareCode` and `getCartShareItemsByCode`, then displays the URL and sharing options. Requires items to be selected first.
2.  **Receiving:** A user navigates to a URL like `/shopping-cart?cart=<code>`. The main `Cart` component (`src/features/cart/ui/Cart.tsx`) detects the `cart` query parameter and triggers the `ShowSharedCartModal` via `useModal`. The modal fetches the items using the code and allows the user to add them via `addSharedCart`.

## Related Features/Entities

*   `features/cart`: Provides the selected items to be shared and is the target for adding shared items.
*   `features/modals`: Manages the display of the sharing and viewing modals.
*   `entities/product`: Shared carts contain product data.
