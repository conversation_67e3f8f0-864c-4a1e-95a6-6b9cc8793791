import { ProductFilterURLInput } from '@/components/Catalog/CatalogRoot';
import { FilterCheckbox } from '@/shared/ui';
import { ProductFilterVisual } from '@/types/Filters/ProductFilter';
import { useRef } from 'react';

interface FilterOptionVisualProps {
  data: ProductFilterVisual;
  onChange: (data: ProductFilterURLInput) => void;
  disabled?: boolean;
}

export const FilterOptionVisual: React.FC<FilterOptionVisualProps> = ({
  data,
  onChange,
  disabled = false
}) => {
  const { current: uuid } = useRef(
    Date.now().toString(36) + Math.random().toString(36).substring(2)
  );

  const handleChange = (data: ProductFilterVisual, itemId: number, bool: boolean, text: string) => {
    if (!data.is_active) return;
    onChange({
      alone: {
        parentId: data.id,
        id: itemId,
        type: 'multiple',
        value: bool,
        title: data.name,
        text
      }
    });
  };

  return (
    <div className='flex flex-col gap-[8px] mt-2'>
      {data.values.map((item) => (
        <FilterCheckbox
          {...item}
          onChange={(bool) => handleChange(data, item.id, bool, item.text)}
          disabled={disabled || !item.is_active}
          id={`${uuid}-${item.id}`}
          key={`${uuid}-${item.id}`}
          is_show_checkbox={false}
          className={`group/visual font-semibold`}
        >
          <span
            className={`size-10 border-[2px] p-[2px] rounded-[4px] ${disabled || !item.is_active ? 'opacity-[0.5]' : ''} ${item.is_selected ? 'group-hover/visual:border-colGreen border-colGreen' : 'border-[transparent]'} bg-[#E6F0EE] group-hover/visual:border-[#59A892] group-has-[:checked]/visual:border-colGreen`}
          >
            <img src={item.image.compact} alt='' className='w-full h-full object-contain' />
          </span>
          {item.text}
        </FilterCheckbox>
      ))}
    </div>
  );
};
