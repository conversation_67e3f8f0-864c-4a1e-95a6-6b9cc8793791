import { ProductFilterURLInput } from '@/components/Catalog/CatalogRoot';
import { SquareCheckbox } from '@/shared/ui';
import { ProductFilterColor } from '@/types/Filters/ProductFilter';
import { useRef } from 'react';

interface FilterOptionColorProps {
  data: ProductFilterColor;
  onChange: (data: ProductFilterURLInput) => void;
  disabled?: boolean;
}
export const FilterOptionColorSquare: React.FC<FilterOptionColorProps> = ({
  data,
  onChange,
  disabled = false
}) => {
  const { current: uuid } = useRef(
    Date.now().toString(36) + Math.random().toString(36).substring(2)
  );

  const handleChange = (data: ProductFilterColor, itemId: number, bool: boolean, text: string) => {
    onChange({
      alone: {
        parentId: data.id,
        id: itemId,
        type: 'multiple',
        value: bool,
        title: data.name,
        text
      }
    });
  };

  return (
    <div className='flex flex-wrap gap-[6px] justify-between'>
      {data.values.map((item) => (
        <SquareCheckbox
          checked={item.is_selected}
          colors={{ color: item.color, second_color: item.second_color }}
          id={`${item.id}-${uuid}`}
          onChange={(bool) => handleChange(data, item.id, bool, item.text)}
          disabled={disabled}
          key={`${item.id}-${uuid}`}
          text={item.text}
        />
      ))}
    </div>
  );
};
