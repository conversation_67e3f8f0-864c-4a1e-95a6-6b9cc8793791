import { ProductFilter } from '@/types/Filters/ProductFilter';
import { useEffect, useRef, useState } from 'react';
import { useProductFilterContext } from './useProductFIlterContext';

const MAX_VISIBLE_HEIGHT = 240;

export const useProductFilterOption = () => {
  const { data, isOpen, onChange, disabled } = useProductFilterContext();

  const [bodyHeight, setBodyHeight] = useState<number>(0);
  const bodyRef = useRef<HTMLDivElement | null>(null);
  const bodyItemsRef = useRef<HTMLDivElement | null>(null);
  const [visible, setVisible] = useState(false);

  const [filtered, setFiltered] = useState<ProductFilter>(data);
  const [isMore, setIsMore] = useState(false);
  const [showSearch, setShowSearch] = useState(false);

  const handleFilter = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (data.input_type !== 'multiple') return;

    const value = event.target.value;
    if (!value) return setFiltered(data);

    if (data.type === 'color') {
      const result = data.values.filter(({ text }) => text.toLowerCase().includes(value));
      const updateData = { ...data, values: result };
      setFiltered(updateData);
    }
    if (data.type === 'text') {
      const result = data.values.filter(({ text }) => text.toLowerCase().includes(value));
      const updateData = { ...data, values: result };
      setFiltered(updateData);
    }
  };

  // при инициализации
  useEffect(() => {
    if (!data) return;

    if (data.input_type === 'multiple') {
      if (data.values.length > 6) setIsMore(true);
      if (data.values.length > 10) setShowSearch(true);
    }

    // Установим исходный список
    setFiltered(data);
  }, [data]);

  // расчёт высоты
  useEffect(() => {
    const el = bodyRef.current;
    const itemsEl = bodyItemsRef.current;
    if (!el && !itemsEl) return;

    const contentHeight = el.scrollHeight;
    const contentItemsHeight =
      itemsEl.scrollHeight > itemsEl.clientHeight ? itemsEl.scrollHeight : 0;
    const height = contentHeight + contentItemsHeight;
    const finalHeight = height > MAX_VISIBLE_HEIGHT ? MAX_VISIBLE_HEIGHT : height;

    el.style.height = isOpen ? `${finalHeight}px` : '0px';

    requestAnimationFrame(() => {
      setBodyHeight(finalHeight);
    });
  }, [isOpen, filtered]);

  // видимость контента с задержкой
  useEffect(() => {
    let timeout: NodeJS.Timeout;

    if (isOpen) {
      timeout = setTimeout(() => setVisible(true), 200);
    } else {
      setVisible(false);
    }

    return () => clearTimeout(timeout);
  }, [isOpen]);

  return {
    bodyRef,
    bodyHeight,
    isOpen,
    visible,
    data,
    filtered,
    handleFilter,
    showSearch,
    isMore,
    bodyItemsRef,
    onChange,
    disabled
  };
};
