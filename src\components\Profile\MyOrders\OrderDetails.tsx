import React from 'react';

import { NavLink, useNavigate } from 'react-router-dom';

import { useModal } from '@/features/modals';
import { PriceDisplay } from '@/widgets/product-card';
import noImg from '@/shared/assets/images/no-image.png'
import type { Order } from '@/entities/order';

interface OrderDetailsProps {
  order: Order;
}

export const OrderDetails = ({ order }: OrderDetailsProps) => {

  return (
    <div className="p-2 sm:p-3 lg:p-4">
      <div className="space-y-3">
        {order.items.map((item) => (
          <NavLink
            to={`/catalog/${item.category.slug}/${item.slug}`}
            key={item.sku}
            className="md:flex justify-between md:space-x-3 border-t border-[#EBEBEB] pt-3"
          >
            <div className="flex space-x-2 md:space-x-3 md:w-1/2">
              <div className="w-[50px] min-w-[50px] h-[50px] rounded-md overflow-hidden bg-colSuperLight p-1">
                <img
                  className="w-full h-full object-contain"
                  src={item.files[0]?.small ? item.files[0]?.small : noImg}
                  alt={item.fullName}
                />
              </div>
              <div>
                <p className="text-colBlack text-sm font-medium line-clamp-2 break-all">
                  {item.fullName}
                </p>

                <div className="flex space-x-2 py-1">
                  <span className="text-xs text-colDarkGray">Код товара:</span>
                  <span className="text-xs text-colDarkGray">{item.sku}</span>
                </div>
                
                {item.status && (
                  <div className="mt-1">
                    <span 
                      className="text-xs px-2 py-0.5 rounded-md inline-block"
                      style={{
                        backgroundColor: item.status.background_color || '#EBEBEB',
                        color: item.status.text_color || '#343434'
                      }}
                    >
                      {item.status.name}
                    </span>
                  </div>
                )}
              </div>
            </div>
            <div className="flex basis-1/3 justify-between items-center md:items-center space-x-3 pl-[58px] md:pl-0 pt-3 md:pt-0">
              <div className="basis-1/3 text-colBlack font-semibold text-right  whitespace-nowrap">
                {item.quantity} {item.price.unit || 'шт'}
              </div>
              <div className="basis-1/3 text-colBlack whitespace-nowrap">
                <PriceDisplay price={item.price} alignment="right" />
              </div>
              <div className="basis-1/3 text-colBlack text-lg text-right font-bold whitespace-nowrap">
                {item.price.total} {item.price.currency.symbol}
              </div>
            </div>
          </NavLink>
        ))}
      </div>
    </div>
  );
};
