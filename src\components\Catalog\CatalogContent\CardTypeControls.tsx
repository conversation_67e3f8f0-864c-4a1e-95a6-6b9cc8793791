import { Icon } from '@iconify-icon/react';

const CardTypeControls = ({ cardType, setTypeCard }) => {
  return (
    <div className="flex justify-end items-center space-x-2">
      <button
        onClick={() => {
          setTypeCard('tile');
          localStorage.setItem('cardView', 'tile');
        }}
        className="icon-btn"
      >
        <Icon
          icon="famicons:grid"
          className={`icon-btn-icon ${cardType === 'tile' ? 'text-colGreen' : 'text-colGray'}`}
          width={24}
          height={24}
        />
      </button>
      <button
        onClick={() => {
          setTypeCard('line');
          localStorage.setItem('cardView', 'line');
        }}
        className="icon-btn"
      >
        <Icon
          icon="material-symbols:table-rows-rounded"
          className={`icon-btn-icon ${cardType === 'line' ? 'text-colGreen' : 'text-colGray'}`}
          width={24}
          height={24}
        />
      </button>
    </div>
  );
};

export default CardTypeControls;
