// src/widgets/footer/FooterDesktopNav.tsx (Temporary location)
import React from 'react';
import { NavLink } from 'react-router-dom';
import type { BasicFiltersResponse } from '@/entities/filter'; // Assuming this type exists

interface FooterDesktopNavProps {
  basicFilters: BasicFiltersResponse | undefined;
}

export const FooterDesktopNav: React.FC<FooterDesktopNavProps> = ({ basicFilters }) => {
  return (
    <div className="hidden lg:flex justify-between space-x-5 xl:space-x-10 2xl:space-x-20 w-full">
      <div className="w-full">
        <p className="text-colBlack font-medium pb-3">О компании</p>
        <ul>
          <li className="pb-2">
            <NavLink
              className="text-colDarkGray hover:text-colGreen"
              to="/about"
            >
              О компании
            </NavLink>
          </li>
          <li className="pb-2">
            <NavLink
              className="text-colDarkGray hover:text-colGreen"
              to="/contacts"
            >
              Контакты
            </NavLink>
          </li>
          <li className="pb-2">
            <NavLink
              className="text-colDarkGray hover:text-colGreen"
              to="/contacts" // Assuming rekvizity is part of contacts
            >
              Реквизиты
            </NavLink>
          </li>
          <li className="pb-2">
            <NavLink
              className="text-colDarkGray hover:text-colGreen"
              to="/contacts" // Assuming for partners is part of contacts or a dedicated page
            >
              Для партнёров
            </NavLink>
          </li>
        </ul>
      </div>
      <div className="w-full">
        <p className="text-colBlack font-medium pb-3">Покупателю</p>
        <ul>
          <li className="pb-2">
            <NavLink
              className="text-colDarkGray hover:text-colGreen"
              to={basicFilters?.data?.length > 0 ? `/catalog/${basicFilters?.data[0].slug}` : "/catalog"}
            >
              Каталог
            </NavLink>
          </li>
          <li className="pb-2">
            <NavLink
              className="text-colDarkGray hover:text-colGreen"
              to="/promotions" // Placeholder link
            >
              Акции
            </NavLink>
          </li>
          <li className="pb-2">
            <NavLink
              className="text-colDarkGray hover:text-colGreen"
              to="/sales" // Placeholder link
            >
              Товары со скидкой
            </NavLink>
          </li>
          <li className="pb-2">
            <NavLink
              className="text-colDarkGray hover:text-colGreen"
              to="/new-arrivals" // Placeholder link
            >
              Новинки
            </NavLink>
          </li>
        </ul>
      </div>
      <div className="w-full">
        <p className="text-colBlack font-medium pb-3">Информация</p>
        <ul>
          <li className="pb-2">
            <NavLink
              className="text-colDarkGray hover:text-colGreen"
              to="/faq"
            >
              Вопрос-ответ
            </NavLink>
          </li>
          <li className="pb-2">
            <NavLink
              className="text-colDarkGray hover:text-colGreen"
              to="/payment-delivery" // Placeholder link
            >
              Оплата и доставка
            </NavLink>
          </li>
          <li className="pb-2">
            <NavLink
              className="text-colDarkGray hover:text-colGreen"
              to="/warranty" // Placeholder link
            >
              Гарантия и обмен
            </NavLink>
          </li>
        </ul>
      </div>
    </div>
  );
};
