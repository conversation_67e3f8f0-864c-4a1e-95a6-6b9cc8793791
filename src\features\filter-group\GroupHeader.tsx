import { GroupHeaderArrow } from './GroupHeaderArrow';
import { useGroupContext } from './hook/useGroupContext';

export const GroupHeader = () => {
  const { data } = useGroupContext();

  switch (data.type) {
    case 'color':
      return <GroupHeaderArrow />;
    case 'multiple':
      return <div>Просто toggle</div>;
    case 'range':
      return <GroupHeaderArrow />;
    default:
      return null;
  }
};
