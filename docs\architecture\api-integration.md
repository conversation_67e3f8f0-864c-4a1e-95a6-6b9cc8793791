# API Integration Strategy

API integration in this project is primarily handled using **RTK Query**, a powerful data fetching and caching tool built on top of Redux Toolkit. This approach standardizes data fetching, simplifies state management related to server data, and provides built-in caching mechanisms.

## Core Setup (RTK Query)

*   **Base API:** The foundation is defined in `src/shared/api/api.ts`. Key configurations include:
    *   `fetchBaseQuery`: Configured with the base URL (`https://rosstok.ru/`).
    *   `prepareHeaders`: Automatically adds the `Authorization: Bearer <token>` header to requests if a token exists in the Redux user state. This ensures authenticated requests are handled correctly.
    *   `credentials: 'include'`: Necessary for sending cookies (like session IDs) with cross-origin requests if applicable, although often token-based auth makes this less critical for auth itself.
    *   **Custom Base Query Wrapper (`loggingBaseQuery`):** This wrapper enhances the default `fetchBaseQuery`:
        *   **Logging:** Logs request details and response/error information during development for easier debugging.
        *   **Global Error Handling:** Intercepts responses. If the backend indicates an error via `{ success: false, err: "...", err_code: "..." }`, it transforms the successful HTTP response into an RTK Query error state.
        *   **Toast Notifications:** Automatically displays error toasts using `sonner` for server errors (5xx), client errors (4xx, if not handled specifically), and unexpected network issues. Specific API error codes (defined in `SILENT_ERROR_CODES` within `api.ts`) can be excluded from automatic toasting if they are handled directly in the UI.
*   **Tag Types:** A predefined list of tags (`'Favorite', 'Order', 'Comparison', 'User', 'Cart', 'Organization', 'Review', 'Product'`, etc.) is declared in the base API setup (`src/shared/api/api.ts`). These tags are crucial for RTK Query's cache invalidation system.

## Defining Endpoints

*   API endpoints (representing specific backend routes) are defined by "injecting" them into the base API instance using `api.injectEndpoints({...})`.
*   **Location:** Following FSD, endpoint definitions are co-located with the feature or entity they primarily concern, within an `api` sub-directory.
    *   Example: Product-related endpoints (`getProduct`, `getVariants`) are in `src/entities/product/api/productApi.ts`.
    *   Example: Cart operations (`getUserCart`, `sendCart`) are in `src/features/cart/api/cartApi.ts`.
*   **Queries (`builder.query`):** Used for read operations (GET requests).
    *   Define the `query` function to return the URL path and parameters.
    *   Use `providesTags` to associate the cached query result with specific tags (e.g., `[{ type: 'Product', id: productId }]`). This allows the cache entry to be invalidated later.
    *   `keepUnusedDataFor`: Specifies how long data should remain in the cache after the last subscriber unsubscribes (defaults can be set globally, overridden per endpoint).
*   **Mutations (`builder.mutation`):** Used for write operations (POST, PUT, DELETE, etc.).
    *   Define the `query` function to return the URL, method, and body.
    *   Use `invalidatesTags` to specify which cache tags should be invalidated upon successful completion of the mutation. This triggers automatic refetching of queries associated with those tags.
    *   `transformErrorResponse`: Can be used optionally to customize error messages based on specific backend error codes before they are passed to the component or global handler.
    *   **Optimistic Updates (Optional):** For a smoother UX, mutations can use `onQueryStarted` to manually update the cache *before* the API call completes, and then revert the change if the call fails. *(Currently seems less utilized in favor of invalidateTags)*.

## Usage in Components

*   RTK Query automatically generates React hooks for each defined endpoint (e.g., `useGetProductQuery`, `useAddToCartMutation`).
*   Components import and use these hooks to interact with the API.
*   **Query Hooks:** Return an object containing `data`, `isLoading`, `isFetching`, `isSuccess`, `isError`, `error`, and a `refetch` function.
*   **Mutation Hooks:** Return a tuple containing a trigger function and an object with `isLoading`, `isSuccess`, `isError`, `error`. The trigger function is called to execute the mutation.
*   RTK Query handles the fetching lifecycle, caching, and updates the associated Redux state automatically.

## Legacy API Calls

*   Direct Axios instance (`src/api/axios.js`) and specific fetch functions (`src/api/searchProducts.js`) exist.
*   These bypass RTK Query's caching and state management.
*   **Refactoring Goal:** Migrate these legacy calls to use RTK Query mutations or queries defined in the appropriate entity/feature `api` slices. This will centralize API logic, provide consistent caching, and simplify component code.

## Key Files

*   `src/shared/api/api.ts`: Base RTK Query setup, global middleware, tag types.
*   `src/features/*/api/`: Feature-specific endpoint definitions.
*   `src/entities/*/api/`: Entity-specific endpoint definitions.
*   `src/api/` (Legacy): Older direct API calls needing migration.
