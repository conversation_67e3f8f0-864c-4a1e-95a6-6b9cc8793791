# GX Phone Widget React

A modular React implementation of the GX Phone Widget with phone number validation, formatting and verification capabilities.

## Features

- 📱 International phone number validation
- 🌍 Automatic country detection as you type
- 🎭 Real-time phone number formatting with country-specific masks
- ✅ SMS verification code support
- 🧩 Modular components for flexible integration
- 🎣 Custom hooks for reusability
- 🔄 Focus preservation during validation
- 🎫 Proper placeholder masks from the beginning

## Components

### PhoneInput

The main phone input component with country selection and optional verification.

```jsx
import { PhoneInput } from '@/shared/ui/gx-widget-react';

// Basic usage
<PhoneInput 
  onChange={(value) => console.log(value)} 
  onValid={(isValid) => console.log('Is valid:', isValid)}
/>

// With verification
<PhoneInput 
  requiredVerification={true}
  onVerificationComplete={(authData) => console.log('Verified:', authData)}
/>
```

### CountrySelector

A standalone country selection dropdown.

```jsx
import { CountrySelector, useCountries } from '@/shared/ui/gx-widget-react';

const MyComponent = () => {
  const { countries, defaultCountry } = useCountries();
  const [selectedCountry, setSelectedCountry] = useState(defaultCountry);
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <CountrySelector
      countries={countries}
      selectedCountry={selectedCountry}
      onSelect={setSelectedCountry}
      isOpen={isOpen}
      onToggle={setIsOpen}
    />
  );
};
```

### VerificationInput

A 4-digit verification code input component.

```jsx
import { VerificationInput } from '@/shared/ui/gx-widget-react';

const MyComponent = () => {
  const [code, setCode] = useState('');
  
  return (
    <VerificationInput
      value={code}
      onChange={setCode}
      onComplete={(code) => console.log('Code entered:', code)}
    />
  );
};
```

## Hooks

### useCountries

Hook to fetch and manage countries data.

```jsx
const { countries, defaultCountry, isLoading, error } = useCountries();
```

### usePhoneVerification

Hook for phone validation and verification.

```jsx
const {
  validateNumber,
  getNumberInfo, // For quick country detection
  sendVerificationCode,
  verifyCode,
  authenticateUser,
  isVerifying,
  sessionId
} = usePhoneVerification();

// Validate a phone number
const result = await validateNumber(phoneNumber, countryIso, countryCode);

// Quick check for country detection
const infoResult = await getNumberInfo(phoneNumber, countryIso, countryCode);

// Send verification code
const sendResult = await sendVerificationCode(phoneNumber, countryIso, countryCode);

// Verify the code
const verifyResult = await verifyCode(code);

// Authenticate with the verified code
const authResult = await authenticateUser(sessionId);
```

### usePhoneMask

Hook for phone number formatting with country-specific masks.

```jsx
const {
  value, // Formatted value for display
  rawValue, // Unformatted digits
  handleChange, // Input change handler
  inputProps, // Ready-to-use input props
  setInputValue, // Set the input value programmatically
  updateMask, // Update the mask format
  reset, // Reset the input
  maskApplied // Whether a mask has been applied
} = usePhoneMask(initialValue, mask, onChange);
```

## Utilities

### formatters

Utility functions for phone number formatting and validation.

```jsx
import { formatters } from '@/shared/ui/gx-widget-react';

// Format a phone number according to a mask
const formatted = formatters.formatPhoneNumber('1234567890', '(XXX) XXX-XXXX');

// Get only the digits from a phone number
const digits = formatters.cleanPhoneNumber('(*************');

// Format with country code
const withCode = formatters.formatWithCountryCode('1234567890', '1');

// Remove country code if present
const localNumber = formatters.stripCountryCode('441234567890', '44');

// Check if a phone number is potentially valid
const isValid = formatters.isPotentiallyValidPhoneNumber('1234567');

// Create a placeholder from a mask
const placeholder = formatters.createPlaceholderFromMask('(XXX) XXX-XXXX', '_');
```

## Backward Compatibility

The original `GxPhoneInputReact` component is still available for backward compatibility:

```jsx
import GxPhoneInputReact from '@/shared/ui/gx-widget-react';

// Or
import { GxPhoneInputReact } from '@/shared/ui/gx-widget-react';
```

## Key Improvements

1. **Correct Phone Number Format in API Calls** - Now sends just the local number (7987503) instead of appending the country code (447987503) when the API expects just the local part.

2. **Maintained Input Focus** - The input now maintains focus after validation, country detection, and error messages so you don't have to manually refocus.

3. **Immediate Mask Application** - Phone input masks are immediately applied when the component mounts, not just after validation.

4. **Real-time Country Detection** - Automatically detects and selects the correct country on each keystroke, with proper API flags.

5. **Faster Detection** - Country detection now starts with as few as 3 digits for quicker response.

6. **Improved Placeholder** - Uses mask-based placeholders to guide users on the expected format.

7. **Modular Architecture** - Components are split into smaller, reusable pieces following React best practices.

Last updated: 2025-04-08