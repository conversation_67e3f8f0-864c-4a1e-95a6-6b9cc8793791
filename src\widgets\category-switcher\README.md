# Widget: Category Switcher

## Purpose

This widget provides a UI for filtering a list (typically search results, comparison items, or favorites) by product category. It displays a list of relevant categories (often with product counts) as selectable buttons or chips.

## Composition

*   **Entities:** Displays category data (`CategoryBase`, `ProductListCategoryChain`) usually derived from API responses related to the current context (e.g., search results, comparison list). It does *not* typically fetch category data itself.
*   **Shared UI:** Uses buttons, icons (`categoryIcon`), and layout utilities from `shared/ui` and `shared/assets`.

## Key UI Components (`ui/`)

*   **`CategorySwitcher.tsx`:** The main component that receives a list of categories (often in `ProductListCategoryChain` format), the currently selected category ID, and a callback function (`onCategoryChange`). It renders the "All" button and buttons for each category.
*   **`CategorySwitcherItem.tsx`:** (Not directly used by `CategorySwitcher.tsx` in the current implementation, but represents the conceptual item) Renders a single category button/chip, including its name, count, and potentially an image. Handles the selected state visually.

## Logic & State

*   **State Management:** This widget is generally stateless regarding the *selection*. It receives the `selectedCategory` and the `onCategoryChange` callback as props from its parent component (usually a page).
*   **Data Input:** Expects an array of category data, typically structured as `ProductListCategoryChain[]`, where each item contains the category details (often the last category in a hierarchy) and the count of associated items in the current context.

## Usage

*   The `CategorySwitcher` is used on pages where filtering by category is needed for a displayed list:
    *   Search Results Page (`/search` - *currently `src/components/Catalog/Search.tsx`*)
    *   Comparison Page (`/comparison` - `src/pages/Comparison/Comparison.tsx`)
    *   Favorites Page (`/favorites` - `src/pages/Favorites/Favorites.tsx`)
*   The parent page component fetches the data (e.g., search results, comparison list) which includes the relevant category distribution, manages the `selectedCategory` state, and passes these down as props along with the `onCategoryChange` handler.

## Related Entities/Widgets/Pages

*   `entities/category`: Defines the structure of the category data displayed.
*   `pages/SearchPage`, `pages/ComparisonPage`, `pages/FavoritesPage`: These pages typically fetch the necessary data (including category counts) and manage the state for this widget.

## Migration Notes

*   The current implementation in `CategorySwitcher.tsx` directly renders buttons based on the `categories` prop. The `CategorySwitcherItem.tsx` file seems unused by it but could be integrated for better component structure.
*   Ensure the data format passed via the `categories` prop is consistent across different consuming pages.
