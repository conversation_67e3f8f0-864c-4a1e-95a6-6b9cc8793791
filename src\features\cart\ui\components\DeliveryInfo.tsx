import React from 'react';
// import { MdLocationOn } from 'react-icons/md';

import type { DeliveryInfo as DeliveryInfoType } from '@/entities/order/api/types';

interface DeliveryInfoProps {
  delivery?: DeliveryInfoType;
  className?: string;
}

export const DeliveryInfo: React.FC<DeliveryInfoProps> = ({ delivery, className = '' }) => {
  if (!delivery) {
    return null;
  }

  // For pickup delivery type
  if (delivery.type === 'pickup' && delivery.point) {
    const { point } = delivery;
    return (
      <div className={`flex items-start text-xs text-colDarkGray ${className}`}>
        {/* <MdLocationOn className="text-colGreen text-lg flex-shrink-0 mt-0.5 mr-1" /> */}
        <div>
          <div className="font-medium">{point.name}</div>
          <div>{point.city}, {point.address}</div>
        </div>
      </div>
    );
  }

  // For courier delivery type
  if (delivery.type === 'courier' && delivery.address) {
    return (
      <div className={`flex items-start text-xs text-colDarkGray ${className}`}>
        {/* <MdLocationOn className="text-colGreen text-lg flex-shrink-0 mt-0.5 mr-1" /> */}
        <div>
          <div className="font-medium">Доставка курьером</div>
          <div>{delivery.address}</div>
        </div>
      </div>
    );
  }

  // Default case - just show delivery type
  return (
    <div className={`flex items-start text-xs text-colDarkGray ${className}`}>
      {/* <MdLocationOn className="text-colGreen text-lg flex-shrink-0 mt-0.5 mr-1" /> */}
      <div>
        <div className="font-medium">
          {delivery.type === 'pickup' ? 'Самовывоз' : 
           delivery.type === 'courier' ? 'Доставка курьером' : 
           'Способ доставки не указан'}
        </div>
      </div>
    </div>
  );
};
