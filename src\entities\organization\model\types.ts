// src/entities/organization/model/types.ts

export interface Organization {
  id: number | string; // Assuming id can be number or string
  name: string;
  inn: string;
  kpp?: string; // Optional as it's not always present in forms
  address?: string; // Physical/postal address
  ogrn?: string;
  u_address?: string; // Juridical address
  f_address?: string; // Actual/factual address
  bank_account?: string;
  bank_bik?: string;
  correspondent_account?: string;
  bank_name?: string;
  // Add any other fields that come from the API or are used in forms
  [key: string]: any; // Allow other properties if the API is flexible
}

export interface OrganizationFormData {
  name: string;
  inn: string;
  kpp?: string;
  ogrn?: string;
  u_address?: string;
  f_address?: string;
  bank_account?: string;
  bank_bik?: string;
  correspondent_account?: string;
  bank_name?: string;
  // Used for creating/updating, might be a subset of Organization
}
