import { api } from '@/shared/api/api';

// Define types for API parameters
interface RegistrationCheckProps {
  email: string;
  phone?: string;
}

interface AuthWithEmailProps {
  email: string;
  password: string;
}

interface UserRegisterProps {
  email: string;
  password: string;
  name?: string;
}

interface ChangePasswordProps {
  oldPassword: string;
  newPassword: string;
}

interface PhoneEntryProps {
  session: string;
  name?: string;
  redirect_page?: string;
}

// Define response types
interface ApiResponseProps {
  success: boolean;
  message?: string;
}

interface RegistrationCheckResponseProps extends ApiResponseProps {
  exists: boolean;
}

interface AuthResponseProps extends ApiResponseProps {
  token?: string;
  user?: {
    id: string;
    email: string;
    name?: string;
  };
}

interface PhoneEntryResponseProps extends ApiResponseProps {
  token?: string;
  user?: {
    id: string;
    phone: string;
    name?: string;
  };
}

export const authenticationApi = api.injectEndpoints({
  endpoints: (builder) => ({
    registrationCheck: builder.mutation<
      RegistrationCheckResponseProps,
      RegistrationCheckProps
    >({
      query: (data) => ({
        url: '/api/User/check',
        method: 'POST',
        body: data,
      }),
    }),
    authWithEmail: builder.mutation<AuthResponseProps, AuthWithEmailProps>({
      query: (data) => ({
        url: '/api/User/auth',
        method: 'POST',
        body: data,
      }),
    }),
    userRegister: builder.mutation<AuthResponseProps, UserRegisterProps>({
      query: (data) => ({
        url: '/api/User/register',
        method: 'POST',
        body: data,
      }),
    }),
    resetPassword: builder.mutation<ApiResponseProps, string>({
      query: (email) => ({
        url: '/api/User/sendPasswordResetLink',
        method: 'POST',
        body: { email: email },
      }),
    }),
    changePassword: builder.mutation<ApiResponseProps, ChangePasswordProps>({
      query: (params) => ({
        url: '/api/Profile/updatePass',
        method: 'POST',
        body: params,
      }),
    }),
    phoneEntry: builder.mutation<PhoneEntryResponseProps, PhoneEntryProps>({
      query: (params) => ({
        url: '/api/User/phone/entry',
        method: 'POST',
        body: params,
      }),
    }),
  }),
});

export const {
  useRegistrationCheckMutation,
  useAuthWithEmailMutation,
  useUserRegisterMutation,
  useResetPasswordMutation,
  useChangePasswordMutation,
  usePhoneEntryMutation,
} = authenticationApi;
