import { ObserverItem } from '@/shared/types/observer';
import { ModalFilterSearchAll } from '@/shared/types/observer/modal.observer';
import { Button, Chips } from '@/shared/ui';
import { ProductFilter } from '../product-filter';
import { useModalAnimation } from './hook/useModalAnimation';
import { useModalSearchAllFilter } from './hook/useModalSearchAllFilter';

interface ModalAllFilterProps {
  data: ObserverItem<ModalFilterSearchAll>;
}

export const ModalSearchAllFilter: React.FC<ModalAllFilterProps> = ({ data }) => {
  const { isUnvisible } = useModalAnimation(data.observerDismiss);
  const {
    filters,
    handleChangeFilter,
    isActive,
    isLoading,
    handleAccept,
    handleChips,
    chips,
    resetFilters
  } = useModalSearchAllFilter(data);

  return (
    <div
      className={`absolute px-6 py-8 inset-0 bg-white transition-opacity duration-200 ease-in-out ${!isUnvisible ? 'opacity-100' : 'opacity-0'}`}
      style={{
        zIndex: `${data.observerId - 1}`
      }}
    >
      <div className='w-full lg:max-w-4xl mx-auto h-full m-auto'>
        <div className='flex flex-wrap justify-between'>
          <h2 className='text-2xl font-semibold text-[#222]'>Все фильтры</h2>
          <div className='flex gap-2'>
            <Button onClick={handleAccept} disabled={isActive}>
              Применить и закрыть
            </Button>
            <Button variant='secondary' onClick={data.onCancel}>
              Закрыть
            </Button>
          </div>
        </div>
        <br />
        <div className='flex gap-[10px] flex-wrap '>
          {!!chips.length &&
            chips.map(
              (item, index) =>
                item.value !== null && (
                  <Chips
                    title={item.title}
                    text={
                      item.type === 'multiple'
                        ? item.text
                        : `от ${item.value[0]} до ${item.value[1]}`
                    }
                    onClick={() => handleChips(item)}
                    key={`${item.parentId}-${index}`}
                  />
                )
            )}
          {!!chips.length && (
            <Chips
              title={'Очистить фильтр'}
              onClick={resetFilters}
              disabled={isLoading}
              variant='secondary'
            />
          )}
        </div>
        <div className='flex flex-wrap gap-4 w-full mt-4'>
          {filters &&
            filters.map((item, index) => {
              return (
                <div
                  className='w-full sm:w-[48%] lg:w-[32%] h-fit grow bg-[#F4F5F5] p-4 rounded-[8px]'
                  key={index}
                >
                  {item.map((filter, index) => (
                    <div
                      key={filter.id}
                      className={`  ${item.length - 1 === index ? '' : 'border-b-2'} border-[#ffffff] py-[12px] px-[6px] will-change-[height]`}
                    >
                      <ProductFilter
                        data={filter}
                        onChange={handleChangeFilter}
                        mode='full'
                        show={false}
                        disabled={isLoading}
                      />
                    </div>
                  ))}
                </div>
              );
            })}
        </div>
      </div>
    </div>
  );
};
