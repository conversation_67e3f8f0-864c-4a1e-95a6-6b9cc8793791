// src/features/cart/model/hooks/useAddToCart.ts
import { useMemo, useState } from 'react';

import { useDispatch } from 'react-redux';

import { useGetCartItemPriceMutation } from '@/entities/price';
import { useAuthContext } from '@/entities/user/model/AuthContext';
import { useSendCartMutation } from '@/features/cart';

import { addToCart } from '../cartSlice';

import type { Product } from '@/entities/product';

// Define possible button states as a type
export type AddToCartButtonStateType =
  | 'normal'       // Default state - can be added to cart
  | 'outOfStock'   // Item is out of stock
  | 'noPrice'      // Item has no price
  | 'preorder'     // Item is available for preorder
  | 'loading';     // Button is in loading state

interface UseAddToCartReturn {
  handleAddToCartClick: (e: React.MouseEvent) => Promise<void>;
  buttonState: {
    text: string;
    disabled: boolean;
    loading: boolean;
    stateType: AddToCartButtonStateType;
    hasPreorderInfo: boolean;
  };
}

export const useAddToCart = (product: Product): UseAddToCartReturn => {
  const [localLoading, setLocalLoading] = useState(false);
  const dispatch = useDispatch();
  // const { isAuthenticated } = useAuthContext();
  const [sendCart] = useSendCartMutation();
  const [getItemPrice] = useGetCartItemPriceMutation();

  const handleAddToCartClick = async (e: React.MouseEvent): Promise<void> => {
    e.preventDefault();
    setLocalLoading(true);

    try {
      let newProduct: Product & { quantity: number; selected: boolean };

      // If user is authenticated, we can skip the price API call and use the price from the cart API response
      // if (isAuthenticated) {
        try {
          // First get the server response without updating the UI
          const result = await sendCart({
            id: product.id,
            quantity: 1,
            selected: false,
          });

          if ('error' in result) {
            // Error toast is now handled by the API layer
            return;
          }

          if ('data' in result) {
            // Now that we have the server price, add to cart with the correct price
            console.log('Server response price in addToCart:', result.data.data.price);

            // Make sure the price is correctly formatted
            const updatedPrice = {
              ...result.data.data.price,
              final: Number(result.data.data.price.final),
              base: Number(result.data.data.price.base),
            };

            console.log('Updated price in addToCart:', updatedPrice);

            newProduct = {
              ...product,
              quantity: 1,
              price: updatedPrice,
              selected: false,
            };

            // Update Redux store with the correct price from the server
            dispatch(addToCart(newProduct));
          }
        } catch {
          // If server request fails, fall back to optimistic update
          newProduct = {
            ...product,
            quantity: 1,
            selected: false,
          };

          // Update Redux store with the local price
          dispatch(addToCart(newProduct));
        }
      // } else {
      //   // For non-authenticated users, we still need to get the price
      //   const priceResponse = await getItemPrice({
      //     item_id: product.id,
      //     quantity: 1,
      //   });

      //   if ('data' in priceResponse) {
      //     newProduct = {
      //       ...product,
      //       quantity: 1,
      //       price: priceResponse.data.data.price,
      //       selected: false,
      //     };

      //     // Update local state
      //     dispatch(addToCart(newProduct));
      //   }
      // }
    } catch {
      // Error toast is now handled by the API layer
    } finally {
      setLocalLoading(false);
    }
  };

  // Determine button state based on product availability and loading states
  const buttonState = useMemo(() => {
    let text = 'В корзину';
    let disabled = false;
    let stateType: AddToCartButtonStateType = 'normal';
    let hasPreorderInfo = false;

    // Handle loading state first
    if (localLoading) {
      stateType = 'loading';
      return {
        text,
        disabled: true,
        loading: true,
        stateType,
        hasPreorderInfo,
      };
    }

    // First check availability - this takes priority
    if (product?.availability) {
      if (product.availability.stock === 0) {
        if (product.availability.preorder === null) {
          // Product is out of stock with no preorder
          disabled = true;
          text = 'Нет в наличии';
          stateType = 'outOfStock';
          return {
            text,
            disabled: true,
            loading: false,
            stateType,
            hasPreorderInfo,
          };
        } else {
          // Product has preorder info
          text = product.availability.preorder.formatted;
          stateType = 'preorder';
          hasPreorderInfo = true;
        }
      }
    } else {
      // No availability information means not available
      disabled = true;
      text = 'Нет в наличии';
      stateType = 'outOfStock';
      return {
        text,
        disabled: true,
        loading: false,
        stateType,
        hasPreorderInfo,
      };
    }

    // Only check price if product is in stock (availability check passed)
    if (product?.price?.base === 0 || product?.price?.base === null) {
      disabled = true;
      text = 'Нет цены';
      stateType = 'noPrice';
    }

    return {
      text,
      disabled: disabled,
      loading: false,
      stateType,
      hasPreorderInfo,
    };
  }, [product?.availability, product?.price, localLoading]);

  return {
    handleAddToCartClick,
    buttonState: {
      text: buttonState.text,
      disabled: buttonState.disabled,
      loading: buttonState.loading,
      stateType: buttonState.stateType,
      hasPreorderInfo: buttonState.hasPreorderInfo,
    },
  };
};
