# Widget: Product Card

## Purpose

This widget provides standardized UI representations for displaying product information in various list formats (grid tiles, different line views). It's a key component for catalog pages, search results, cart, favorites, comparison, and recommendation sections.

## Composition

The Product Card widget integrates data and functionality from:

*   **Entities:** Primarily displays data from the `Product` entity (`entities/product`). Accesses `Price` information (`entities/price`).
*   **Features:** Incorporates action buttons and state hooks from features like:
    *   `AddToCartButton`, `QuantityControl`, `RemoveFromCartButton` (`features/cart`)
    *   `FavoriteButton` (`features/favorite`)
    *   `ComparisonButton` (`features/comparison`)
*   **Shared UI:** Uses basic elements like images, text formatting, layout utilities, and potentially skeleton loaders from `shared/ui`.

## Key UI Components (`ui/`)

*   **`ProductCard.tsx`:** The standard grid tile view of a product.
*   **`ProductCardLine.tsx`:** A detailed horizontal line view, often used in cart or comparison.
*   **`ProductCardLineSmall.tsx`:** A more compact horizontal line view.
*   **Skeletons:** Corresponding skeleton loader components (`ProductCardSkeleton.tsx`, `ProductCardLineSkeleton.tsx`, etc.) for displaying loading states.
*   **Sub-components:**
    *   `PreviewGallery.tsx`: Handles displaying the product image(s), including hover/swipe interactions for multiple images and tag/button overlays.
    *   `PriceDisplay.tsx`: Standardized component for rendering product price, including handling discounts and currency symbols.
    *   `ProductAttributesDisplay.tsx`: Renders a list of key product attributes (like SKU, color, size).

## Logic & State (`model/`)

*   **`hooks/useProductCard.ts`:** A custom hook to encapsulate common logic needed within product card variants. It determines if the product is currently in the cart and selects the appropriate price to display (either the base product price or the potentially updated price from the cart state).

## Usage

*   Product card components are used extensively wherever lists of products need to be displayed:
    *   Catalog pages (`pages/Catalog/...`)
    *   Search results (`pages/Search/...`)
    *   Cart page (`pages/Cart/CartPage.tsx`)
    *   Favorites page (`pages/Favorites/Favorites.tsx`)
    *   Comparison page (`pages/Comparison/Comparison.tsx`)
    *   Home page sections (New Arrivals, Sales, Recommendations) (`pages/Home/Home.tsx`)
    *   Recently Viewed section (`features/recent-items/`)

## Key Considerations

*   **Performance:** As these components are often rendered in long lists, performance is critical. Use `React.memo` where appropriate and ensure efficient data selection and rendering.
*   **Consistency:** Use the standardized sub-components (`PriceDisplay`, `PreviewGallery`, `ProductAttributesDisplay`) to maintain a consistent look and feel across different card variants.
*   **Responsiveness:** Ensure all card variants adapt appropriately to different screen sizes.