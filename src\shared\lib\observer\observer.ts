import { ObserverItem } from '@/shared/types/observer';

let orderCounter = 9999;

export class Observer<T> {
  private payloads: Array<ObserverItem<T>> = [];
  private subscribers: Array<(data: ObserverItem<T>[]) => void> = [];

  subscribe(observer: (data: ObserverItem<T>[]) => void): () => void {
    this.subscribers.push(observer);

    return () => {
      const index = this.subscribers.indexOf(observer);
      this.subscribers.splice(index, 1);
    };
  }

  addObserver(data: T): number {
    let id = orderCounter;
    orderCounter += 2;
    const observer = {
      ...data,
      observerDismiss: false,
      observerId: id
    };
    this.payloads.push(observer);
    this.notifyObservers();
    return id;
  }

  updateObserver(observerId: number, data: T): void {
    const observer = this.payloads.find((payload) => payload.observerId === observerId);
    if (observer) {
      this.payloads.map((payload) =>
        payload.observerId === observerId ? { ...payload, ...data } : payload
      );
      this.notifyObservers();
    }
  }

  dissmissObserver(payloadId?: number): void {
    if (!payloadId) {
      this.payloads.length = 0;
      console.log(this.payloads, this.subscribers);
      this.notifyObservers();
      return;
    }

    const observer = this.payloads.find((payload) => payload.observerId === payloadId);
    if (observer) {
      observer.observerDismiss = true;
    }
    this.notifyObservers();

    setTimeout(() => {
      this.payloads = this.payloads.filter((payload) => !payload.observerDismiss);
      this.notifyObservers();
    }, 300);
  }

  notifyObservers() {
    this.subscribers.forEach((subscriber) => subscriber([...this.payloads]));
  }
}
