import { useCallback, useEffect, useRef, useState } from 'react';

import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';

import { useAuthContext } from '@/entities/user/model/AuthContext';
import {
  addToFavorite,
  removeFromFavorite,
  setFavorite,
  useLazyGetFavoritesQuery,
  useRemoveFromFavoritesMutation,
  useSendFavoritesMutation
} from '@/features/favorite';
import { getFromSessionStorage, saveToSessionStorage } from '@/features/storage/lib';

import type { RootState } from '@/app/providers/store';
import type { Product } from '@/entities/product';
import type { ProductListRequest } from '@/shared/types/ProductListRequest';

/**
 * Enhanced hook for accessing and managing favorites data throughout the application.
 * This is the single source of truth for favorites data.
 *
 * Features:
 * - Handles both authenticated and unauthenticated states
 * - Provides access to favorites data from Redux store
 * - Manages favorites data fetching with optimized caching
 * - Provides helper methods for finding items in favorites
 * - Includes methods for syncing with server and storage
 */
export const useFavorites = () => {
  const dispatch = useDispatch();
  const { isAuthenticated } = useAuthContext();
  const favorites = useSelector((state: RootState) => state.favorite.favorite);
  const hasLoadedFromStorageRef = useRef(false);

  // RTK Query hooks
  const [
    triggerGetFavorites,
    { isLoading, error }
  ] = useLazyGetFavoritesQuery();

  const [sendFavorites] = useSendFavoritesMutation();
  const [removeFavorites] = useRemoveFromFavoritesMutation();

  // State for individual product operations
  const [isUpdating, setIsUpdating] = useState(false);

  // Helper function to check if a product is in favorites
  const isInFavorites = useCallback(
    (productId: number) => {
      return favorites.some((item) => item.id === productId);
    },
    [favorites]
  );

  // Helper function to find a product in favorites by ID
  const findFavoriteItem = useCallback(
    (productId: number) => {
      return favorites.find((item) => item.id === productId) || null;
    },
    [favorites]
  );

  // Load favorites from sessionStorage
  const loadFromStorage = useCallback(() => {
    try {
      const storedFavorites = getFromSessionStorage('favorite') as Product[] | null;
      if (storedFavorites && Array.isArray(storedFavorites)) {
        dispatch(setFavorite(storedFavorites));
      } else {
        console.log('[useFavorites] No valid data found in storage');
      }
      hasLoadedFromStorageRef.current = true;
    } catch (error) {
      console.error('[useFavorites] Error loading from storage:', error);
    }
  }, [dispatch]);

  // Save favorites to sessionStorage
  const saveToStorage = useCallback(
    (favoritesData = favorites) => {
      try {
        if (!isAuthenticated && favoritesData) {
          saveToSessionStorage('favorite', favoritesData);
        } else {
          console.log('[useFavorites] Not saving - authenticated or no data');
        }
      } catch (error) {
        console.error('[useFavorites] Error saving to storage:', error);
      }
    },
    [isAuthenticated] // Remove 'favorites' from dependencies to prevent infinite loop
  );

  const refreshFromServer = useCallback(async () => {
    console.log('[useFavorites] refreshFromServer called. Attempting to trigger query...');
    try {
      const result = await triggerGetFavorites(undefined, false);

      if (result.data?.data) {
        dispatch(setFavorite(result.data.data));
      }
      return result;
    } catch (error) {
      console.error('[useFavorites] Error fetching from server:', error);
      throw error;
    }
  }, [triggerGetFavorites, dispatch]);

  const syncToServer = useCallback(
    async (favoritesData = favorites) => {
      if (!isAuthenticated || !favoritesData?.length) {
        return Promise.resolve();
      }

      try {
        const payload: ProductListRequest = {
          ids: favoritesData.map((item) => item.id)
        };

        return await sendFavorites(payload);
      } catch (error) {
        console.error('[useFavorites] Error syncing to server:', error);
        return Promise.reject(error);
      }
    },
    [isAuthenticated, favorites, sendFavorites]
  );

  const clearData = useCallback(() => {
    dispatch(setFavorite([]));
    sessionStorage.removeItem('favorite');
  }, [dispatch]);

  // Auto-save to storage when favorites change (for non-authenticated users)
  // Only save after initial load to prevent overwriting stored data
  useEffect(() => {
    // Only auto-save if:
    // 1. Not authenticated (local storage mode)
    // 2. Has loaded from storage at least once (prevents overwriting on initial load)
    // 3. Has favorites data
    if (!isAuthenticated && hasLoadedFromStorageRef.current && favorites && favorites.length >= 0) {
      saveToStorage(favorites);
    } else {
    }
  }, [favorites, isAuthenticated, saveToStorage]);

  // Individual product operations
  const handleFavoriteClick = useCallback(
    async (product: Product, e?: React.MouseEvent) => {
      if (e) {
        e.preventDefault();
        e.stopPropagation();
      }

      setIsUpdating(true);
      try {
        const isCurrentlyInFavorites = isInFavorites(product.id);

        if (isCurrentlyInFavorites) {
          // Optimistically remove from local state
          dispatch(removeFromFavorite(product));

          if (isAuthenticated) {
            const result = await removeFavorites({ id: product.id });
            if ('error' in result) {
              // Revert on error
              dispatch(addToFavorite(product));
              toast.error('Failed to remove from favorites');
            }
          }
        } else {
          // Optimistically add to local state
          dispatch(addToFavorite(product));

          if (isAuthenticated) {
            const result = await sendFavorites({ id: product.id });
            if ('error' in result) {
              // Revert on error
              dispatch(removeFromFavorite(product));
              toast.error('Failed to add to favorites');
            }
          }
        }
      } catch (error) {
        // Revert on error
        const isCurrentlyInFavorites = isInFavorites(product.id);
        if (isCurrentlyInFavorites) {
          dispatch(addToFavorite(product));
        } else {
          dispatch(removeFromFavorite(product));
        }
        toast.error('Failed to update favorites');
      } finally {
        setIsUpdating(false);
      }
    },
    [isAuthenticated, isInFavorites, dispatch, sendFavorites, removeFavorites]
  );

  return {
    // Data and state
    favorites,
    isLoading: isLoading || isUpdating,
    isError: !!error,

    // Helper functions
    isInFavorites,
    findFavoriteItem,

    // Individual product operations
    handleFavoriteClick,

    // Sync methods (for SyncProvider)
    loadFromStorage,
    saveToStorage,
    refreshFromServer,
    syncToServer,
    clearData
  };
};
