import type React from 'react';
import { useState } from 'react';

import { IconButton } from '@mui/material';

import { Button, CopyButton } from '@/shared/ui';

import { OrderActions } from './OrderActions';

import type { Order } from '@/entities/order';
import { Link } from 'react-router-dom';

interface OrderHeaderProps {
  order: Order;
}

export const OrderHeader = ({ order }: OrderHeaderProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  return (
    <div className="flex justify-between w-full items-start">
      <div className="flex flex-col gap-2 items-start">
        <div className="flex justify-between items-center">
          <div className="flex justify-between items-start w-full">
            <div className="flex items-center gap-1">
              <span>№ {order.order_number}</span>
              <CopyButton
                textToCopy={order.order_number.toString()}
                toastMessage="Номер заказа скопирован"
                iconClassName="!w-4 cursor-pointer !mr-3"
              />
              <Button variant='link' size='small'>
                <Link to={`/profile/orders/${order.order_number}`}>
                Перейти к странице заказа
                </Link>
              </Button>
            </div>
          </div>
        </div>
        
        <div className='p-2 rounded-md font-semibold' style={{ backgroundColor: order.status.background_color, color: order.status.text_color}} >{order.status.name}</div>
        <span className="text-colBlack">Создан: {order.date}</span>
        <div className="flex gap-2">
          <span className="text-colBlack mr-1 leading-[120%]">
            Сумма заказа:
          </span>
          <span className="text-colBlack leading-[120%] font-bold">
            {order.total.amount} {order?.total?.currency?.symbol || '₽'}
          </span>
        </div>
      </div>

      <OrderActions orderNumber={order.order_number} />
    </div>
  );
};
