import React, { useState } from 'react';

import Lightbox from 'yet-another-react-lightbox';

import 'yet-another-react-lightbox/styles.css';
import { RatingStars } from '@/entities/review';
import { ReviewActions } from './ReviewActions.tsx';
import { useModal } from '@/features/modals/model/context';
import { Menu, MenuItem, IconButton } from '@mui/material';
import { MoreVert } from '@mui/icons-material';
import { Button } from '@/shared/ui';

export const Review = ({ review, variant_id }) => {
  const [index, setIndex] = useState(-1);
  const [anchorEl, setAnchorEl] = useState(null);
  const { showModal } = useModal();

  const {
    comment_user_name,
    rating,
    comment,
    pluses,
    minuses,
    created_at,
    files,
    my_comment,
  } = review;

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ru-RU', {
      day: '2-digit',
      month: 'long',
      year: 'numeric',
    }).format(date);
  };

  const photos =
    files?.map((item, i) => ({
      src: item.small,
      id: i,
    })) || [];

  return (
    <div className="p-5 border rounded-[10px] flex flex-col relative">
      <div className="flex gap-2 mb-5">
        <div className="flex items-center gap-2 flex-grow">
          <div className="font-semibold text-lg">
            {comment_user_name || 'Анонимный пользователь'}
          </div>
          <div className="text-colDarkGray">{formatDate(created_at)}</div>
        </div>
        <div className="flex items-center gap-2">
          <RatingStars totalStars={5} initialRating={rating} />
          
          {my_comment && <ReviewActions review={review} variant_id={variant_id} />}
        </div>
      </div>

      {comment ? (
        <div className="mb-3">
          <div className="text-sm font-semibold text-colDarkGray mb-1">
            Комментарий:
          </div>
          <div>{comment}</div>
        </div>
      ) : null}

      {pluses ? (
        <div className="mb-3">
          <div className="text-sm font-semibold text-colDarkGray mb-1">
            Достоинства:
          </div>
          <div>{pluses}</div>
        </div>
      ) : null}

      {minuses ? (
        <div>
          <div className="text-sm font-semibold text-colDarkGray mb-1">
            Недостатки:
          </div>
          <div>{minuses}</div>
        </div>
      ) : null}

      {photos.length > 0 ? (
        <>
          <div className="w-full flex flex-nowrap scrollable overflow-x-auto gap-2 py-5">
            {photos.map((photo, i) => (
              <div key={photo.id} className="shrink-0 p-1 border rounded">
                <img
                  src={photo.src}
                  alt=""
                  className="w-[100px] h-[100px] object-contain cursor-pointer"
                  onClick={() => setIndex(i)}
                />
              </div>
            ))}
          </div>
          <Lightbox
            open={index >= 0}
            index={index}
            close={() => setIndex(-1)}
            slides={photos}
          />
        </>
      ) : null}
    </div>
  );
};

export default Review;
