import React from 'react';
import { NavLink } from 'react-router-dom';
import { PriceDisplay } from '@/widgets/product-card';
import type { Product } from '@/entities/product';

interface ProductPreviewProps {
  product: Product;
  showPrice?: boolean;
  showQuantity?: boolean;
  quantity?: number;
}

export const ProductPreview = ({ 
  product,
  showPrice = false,
  showQuantity = false,
  quantity
}: ProductPreviewProps) => {
  return (
    <NavLink
      to={`/catalog/${product.category.slug}/${product.slug}`}
      className="md:flex justify-between md:space-x-3 pt-3"
    >
      <div className="flex space-x-2 md:space-x-3 md:w-1/2">
        <div className="w-[50px] min-w-[50px] h-[50px] rounded-md overflow-hidden bg-colSuperLight p-1">
          <img
            className="w-full h-full object-contain"
            src={product.files[0]?.small}
            alt={product.fullName}
          />
        </div>
        <div>
          <p className="text-colBlack text-sm font-medium line-clamp-2 break-all">
            {product.fullName}
          </p>

          <div className="flex space-x-2 py-1">
            <span className="text-xs text-colDarkGray">Код товара:</span>
            <span className="text-xs text-colDarkGray">{product.sku}</span>
          </div>
        </div>
      </div>
      {(showPrice || showQuantity) && product.price && (
        <div className="flex basis-1/3 justify-between items-center md:items-center space-x-3 pl-[58px] md:pl-0 pt-3 md:pt-0">
          {showQuantity && (
            <div className="basis-1/3 text-colBlack font-semibold text-right whitespace-nowrap">
              {quantity} {product.price.unit || 'шт'}
            </div>
          )}
          {showPrice && (
            <>
              <div className="basis-1/3 text-colBlack whitespace-nowrap">
                <PriceDisplay price={product.price} alignment="right" />
              </div>
              <div className="basis-1/3 text-colBlack text-lg text-right font-bold whitespace-nowrap">
                {product.price.total} {product.price.currency.symbol}
              </div>
            </>
          )}
        </div>
      )}
    </NavLink>
  );
};

