import React from 'react';

interface SvgIconProps {
  size?: number;
  color?: string;
  stroke?: number;
  className?: string;
}

export const ChevronLeft: React.FC<SvgIconProps> = ({
  size = 24,
  color = 'currentColor',
  stroke = 2,
  className
}) => {
  return (
    <svg
      width={size + 'px'}
      height={size + 'px'}
      viewBox='0 0 24 24'
      fill='none'
      color={color}
      xmlns='http://www.w3.org/2000/svg'
      className={className}
    >
      <path
        d='M15 5L9 12L15 19'
        stroke='currentColor'
        strokeWidth={stroke}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  );
};
