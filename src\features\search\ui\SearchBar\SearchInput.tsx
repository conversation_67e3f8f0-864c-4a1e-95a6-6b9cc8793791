import { memo } from 'react';
import search from '@/shared/assets/icons/search.svg';

export interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
  onFocus: () => void;
  onSubmit: (e: React.FormEvent) => void;
}

/**
 * Search input field with submit button
 */
export const SearchInput = memo(({ value, onChange, onFocus, onSubmit }: SearchInputProps) => {
  return (
    <form
      onSubmit={onSubmit}
      className="z-20 w-full border-colGreen border rounded-lg flex justify-between"
    >
      <input
        className="w-full h-[34px] mm:h-10 outline-none rounded-l-md bg-white px-3 border-none lining-nums proportional-nums"
        type="search"
        placeholder="Поиск по сайту"
        onFocus={onFocus}
        onChange={(e) => {
          onFocus();
          onChange(e.target.value);
        }}
        value={value}
      />
      <button type="submit" className="bg-colGreen rounded-r-md w-14">
        <img className="mx-auto" src={search} alt="*" />
      </button>
    </form>
  );
});

SearchInput.displayName = 'SearchInput';
