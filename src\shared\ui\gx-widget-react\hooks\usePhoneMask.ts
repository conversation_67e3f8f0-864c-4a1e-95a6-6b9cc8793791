import { useState, useEffect, useCallback } from 'react';
import { formatPhoneNumber, cleanPhoneNumber } from '../utils/formatters';

interface UsePhoneMaskProps {
  initialValue?: string;
  mask?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

interface UsePhoneMaskReturn {
  value: string;
  rawValue: string;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  inputProps: {
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  };
  setInputValue: (newValue: string) => void;
  updateMask: (newMask: string) => void;
  reset: () => void;
  maskApplied: boolean;
}

export const usePhoneMask = ({
  initialValue = '',
  mask = '',
  onChange,
}: UsePhoneMaskProps): UsePhoneMaskReturn => {
  const [value, setValue] = useState<string>(initialValue || '');
  const [formattedValue, setFormattedValue] = useState<string>('');
  const [currentMask, setCurrentMask] = useState<string>(mask);
  const [maskApplied, setMaskApplied] = useState<boolean>(false);

  const applyMask = useCallback((rawValue: string, maskPattern: string) => {
    if (typeof maskPattern === 'string' && maskPattern && rawValue) {
      return formatPhoneNumber(rawValue, maskPattern);
    }
    return rawValue;
  }, []);

  useEffect(() => {
    if (mask && mask !== currentMask) {
      setCurrentMask(mask);
      if (value) {
        const newFormattedValue = applyMask(value, mask);
        setFormattedValue(newFormattedValue);
        setMaskApplied(true);
      }
    }
  }, [mask, currentMask, value, applyMask]);

  useEffect(() => {
    if (currentMask && value) {
      const newFormattedValue = applyMask(value, currentMask);
      setFormattedValue(newFormattedValue);
      setMaskApplied(true);
    } else if (value) {
      setFormattedValue(value);
    }
  }, [value, currentMask, applyMask]);

  useEffect(() => {
    if (initialValue && initialValue !== value) {
      setValue(initialValue);
      if (currentMask) {
        const newFormattedValue = applyMask(initialValue, currentMask);
        setFormattedValue(newFormattedValue);
        setMaskApplied(true);
      } else {
        setFormattedValue(initialValue);
      }
    }
  }, [initialValue, currentMask, value, applyMask]);

  useEffect(() => {
    if (typeof currentMask === 'string' && currentMask && !maskApplied) {
      setFormattedValue('');
      setMaskApplied(true);
    }
  }, [currentMask, maskApplied]);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      let inputValue = e.target.value.replace(/[^\d\s\-().]/g, '');

      if (currentMask) {
        setValue(cleanPhoneNumber(inputValue));
        const newFormattedValue = applyMask(inputValue, currentMask);
        setFormattedValue(newFormattedValue);
        setMaskApplied(true);

        if (onChange) {
          const syntheticEvent = {
            ...e,
            target: {
              ...e.target,
              value: newFormattedValue,
            },
          };
          onChange(syntheticEvent as React.ChangeEvent<HTMLInputElement>);
        }
      } else {
        setValue(inputValue);
        setFormattedValue(inputValue);
        if (onChange) {
          onChange(e);
        }
      }
    },
    [currentMask, onChange, applyMask]
  );

  const reset = useCallback(() => {
    setValue('');
    setFormattedValue('');
    setMaskApplied(false);
  }, []);

  const setInputValue = useCallback(
    (newValue: string) => {
      setValue(newValue);
      if (currentMask && newValue) {
        const newFormattedValue = applyMask(newValue, currentMask);
        setFormattedValue(newFormattedValue);
        setMaskApplied(true);
      } else {
        setFormattedValue(newValue);
      }
    },
    [currentMask, applyMask]
  );

  const updateMask = useCallback(
    (newMask: string) => {
      if (newMask !== currentMask) {
        setCurrentMask(newMask);
        if (value) {
          const newFormattedValue = applyMask(value, newMask);
          setFormattedValue(newFormattedValue);
          setMaskApplied(true);
        }
      }
    },
    [currentMask, value, applyMask]
  );

  return {
    value: formattedValue,
    rawValue: value,
    handleChange,
    inputProps: {
      value: formattedValue,
      onChange: handleChange,
    },
    setInputValue,
    updateMask,
    reset,
    maskApplied,
  };
};

export default usePhoneMask;