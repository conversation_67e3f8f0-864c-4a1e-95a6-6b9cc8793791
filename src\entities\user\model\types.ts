// src/entities/user/model/types.ts

/**
 * User data model
 */
export interface User {
  name: string;
  photo: string | null;
  phone: string;
  email: string;
  gender: string;
  date_of_birth: string | null;
  id?: number;
  public_token?: string;
  role?: {
    id: number;
    name: string;
  };
}

/**
 * Token source tracking to handle multiple auth providers
 */
export type TokenSource = 'app' | 'gx' | null;

/**
 * User state in Redux store
 */
export interface UserState {
  token: string | null;
  isAuthenticated: boolean;
  isInitialized: boolean;
  data: User | null;
  tokenSource: TokenSource;
}
