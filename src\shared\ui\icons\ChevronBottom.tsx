import React from 'react';

interface SvgIconProps {
  size?: number;
  color?: string;
  stroke?: number;
}

export const ChevronBottom: React.FC<SvgIconProps> = ({
  size = 24,
  color = 'currentColor',
  stroke = 2
}) => {
  return (
    <svg
      width={size + 'px'}
      height={size + 'px'}
      viewBox='0 0 24 24'
      fill='none'
      color={color}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M19 9L12 15L5 9'
        stroke='currentColor'
        strokeWidth={stroke}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  );
};
