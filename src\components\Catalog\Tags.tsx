import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';

import { Advantages } from '@/components/Home/Advantages';
import { Brands } from '@/components/Home/Brands';
import { Promotions } from '@/components/Home/Promotions';
import { useGetBasicFiltersQuery, useGetFiltersMutation } from '@/entities/filter';
import { useGetVariantsMutation } from '@/entities/product/api/productApi';
import { useGetTagQuery } from '@/entities/tag/api/tagApi';
import { AllFiltersModal } from '@/features/modals';
import { scrollToTop } from '@/shared/lib/scrollToTop';
import { Breadcrumbs } from '@/widgets/breadcrumbs';

import CatalogContent from './CatalogContent/CatalogContent';
import CatalogSidebar from './CatalogSidebar/CatalogSidebar';

import type { FiltersState } from '@/types/Filters/FiltersState';

/**
 * Tag catalog component for displaying products filtered by a specific tag
 *
 * This component is a specialized version of the Catalog component that:
 * 1. Handles tag-specific filtering logic
 * 2. Displays tag information at the top
 * 3. Uses a simplified filter set appropriate for tag pages
 */
const TagProducts = () => {
  console.log('============ TAGS COMPONENT RENDER START ============');

  // Get tag from URL query parameters
  const [searchParams] = useSearchParams();
  const tagParam = searchParams.get('tags');
  console.log('Current tag from URL params:', tagParam);

  // Routing hooks for navigation and URL parsing
  const navigate = useNavigate();
  const location = useLocation();
  console.log('Current location.search:', location.search);

  // Fetch all tags for empty state
  const { data: basicFilters } = useGetBasicFiltersQuery();

  // Fetch tag data
  const {
    data: tag,
    isSuccess: tagIsSuccess,
    isLoading: tagIsLoading
  } = useGetTagQuery(tagParam, { skip: !tagParam });

  // Flag to track if component is mounted
  const isMounted = useRef(true);

  // Check if we should show tag selection page instead of a specific tag
  const shouldShowTagList = !tagParam;

  // If we need to show tags list, don't proceed with filter loading
  if (shouldShowTagList) {
    return (
      <div className='content lining-nums proportional-nums'>
        <Breadcrumbs />
        <div className='flex flex-col gap-3 mb-6'>
          <h3 className='font-semibold text-xl mm:text-2xl lg:text-4xl text-colBlack'>
            Товары по тегам
          </h3>
          <p className='text-gray-500 mb-4'>
            Выберите тег для просмотра товаров с соответствующим статусом.
          </p>

          <div className='flex flex-wrap gap-4 py-5'>
            {basicFilters?.tags?.map((tag) => (
              <a
                key={tag.id}
                href={`/tags?tags=${tag.tag}`}
                className='inline-block py-2 px-4 rounded-lg text-base font-medium transition-opacity hover:opacity-80'
                style={{
                  backgroundColor: tag.background_color || '#f0f0f0',
                  color: tag.text_color || '#000000'
                }}
              >
                {tag.tag}
              </a>
            ))}
          </div>
        </div>

        {/* Additional promotional components */}
        <Promotions />
        <Brands />
        <Advantages />
      </div>
    );
  }

  // Tracks state of filters modal (for mobile view)
  const [filtersModalOpen, setFiltersModalOpen] = useState(false);

  // ===== FILTERS STATE MANAGEMENT =====

  /**
   * Main filters state holding all available filters
   */
  const [filters, setFilters] = useState<FiltersState>({
    basics: {
      price: {
        min: 0,
        max: 0
      },
      tags: [],
      brands: [],
      rating: []
    },
    dynamics: [],
    more: [],
    category_chain: []
  });

  // Loading states for filters
  const [filtersLoading, setFiltersLoading] = useState(false);
  const [filtersBlock, setFiltersBlock] = useState(false);

  /**
   * Trigger for filter updates
   */
  const [trigger, setTrigger] = useState('');
  console.log('Current trigger value:', trigger);

  // Reference to previous filters state for comparison
  const previousFilters = useRef({});

  // References for API request cancellation
  const getFiltersRef = useRef(null);
  const getProductsRef = useRef(null);

  // Get filter mutation hook from RTK Query
  const [
    getFilters,
    { isLoading: filtersIsLoading, isSuccess: filtersIsSuccess, error: filtersError }
  ] = useGetFiltersMutation();

  /**
   * Fetches new filter options based on current selection
   */
  const getNewFiltersList = async (sendObject, trigger) => {
    console.log('============ getNewFiltersList CALLED ============');
    console.log('Trigger type:', trigger);
    console.log('Send object:', JSON.stringify(sendObject));

    // Cancel any in-flight request
    if (getFiltersRef.current) {
      console.log('Cancelling previous filters request');
      getFiltersRef.current.abort();
    }

    // Create new abort controller
    const abortController = new AbortController();
    getFiltersRef.current = abortController;

    // Set appropriate loading state based on trigger type
    if (trigger === 'tags') {
      console.log('Setting filtersLoading to true for trigger:', trigger);
      setFiltersLoading(true);
    } else if (trigger === 'filters') {
      console.log('Setting filtersBlock to true for filter update');
      setFiltersBlock(true);
    }

    try {
      console.log('Calling filters API...');
      // Call the filter API endpoint with abort signal
      const newFilters = await getFilters({
        ...sendObject,
        signal: abortController.signal
      });

      console.log('Filters API response received');

      // Type guard to check if response has data
      if ('data' in newFilters) {
        console.log('Valid filters data received:', Object.keys(newFilters.data));

        // Process "more" filters by marking them as additional
        const more = newFilters.data.more.map((obj) => ({
          ...obj,
          additional_filter: true
        }));
        console.log('Processed "more" filters:', more.length);

        // Combine regular dynamic filters with "more" filters
        const newDynamics = newFilters.data.dynamics.concat(more);
        console.log('Combined dynamics count:', newDynamics.length);

        // Create the updated filters state
        const newFiltersState = {
          ...filters,
          basics: newFilters.data.basics,
          dynamics: newDynamics
        };

        console.log('New filter state prepared');
        console.log(
          'Price min/max:',
          newFiltersState?.basics?.price?.min,
          newFiltersState?.basics?.price?.max
        );
        console.log('Tags count:', newFiltersState?.basics?.tags?.length);
        console.log('Brands count:', newFiltersState?.basics?.brands?.length);

        // Update URL with new filter parameters
        const queryParams = buildQueryParams(getSendFiltersObject2(newFiltersState), sort, page);
        console.log('Building URL with query params:', queryParams);

        // Preserve the original tag parameter
        let newQueryParams = queryParams;
        if (!queryParams.includes('tags=') && tagParam) {
          newQueryParams = queryParams ? `${queryParams}&tags=${tagParam}` : `tags=${tagParam}`;
        }

        // Only update URL if component is still mounted
        if (isMounted.current) {
          navigate(`?${newQueryParams}`, { replace: true });

          // Update references and state
          console.log('Updating previousFilters reference');
          previousFilters.current = newFiltersState;

          console.log('Setting new filters state');
          setFilters(newFiltersState);

          console.log('Setting trigger to:', trigger);
          setTrigger(trigger);
        }
      } else {
        console.error('Filters API returned unexpected format:', newFilters);
      }
    } catch (error) {
      // Only log error if it's not an abort error
      if (error.name !== 'AbortError') {
        console.error('Failed to fetch filters:', error);
      } else {
        console.log('Filters request was aborted');
      }
    } finally {
      // Only update state if component is still mounted
      if (isMounted.current) {
        // Reset loading states
        if (trigger === 'tags') {
          console.log('Resetting filtersLoading to false');
          setFiltersLoading(false);
        } else if (trigger === 'filters') {
          console.log('Resetting filtersBlock to false');
          setFiltersBlock(false);
        }

        // Clear reference if this controller is still the current one
        if (getFiltersRef.current === abortController) {
          console.log('Clearing getFiltersRef');
          getFiltersRef.current = null;
        }
      }
      console.log('============ getNewFiltersList COMPLETED ============');
    }
  };

  // ===== PRODUCTS STATE MANAGEMENT =====

  // Get products mutation hook from RTK Query
  const [
    getVariants,
    { isLoading: getVariantsIsLoading, isSuccess: getVariantsIsSuccess, error: getVariantsError }
  ] = useGetVariantsMutation();

  // Products state and loading indicator
  const [products, setProducts] = useState([]);
  const [productsLoading, setProductsLoading] = useState(false);

  /**
   * Fetches products based on current filters, sort, and pagination
   */
  const getProducts = async (sendObject) => {
    console.log('============ getProducts CALLED ============');
    console.log('Send object:', JSON.stringify(sendObject));

    // Cancel any in-flight request
    if (getProductsRef.current) {
      console.log('Cancelling previous products request');
      getProductsRef.current.abort();
    }

    // Create new abort controller
    const abortController = new AbortController();
    getProductsRef.current = abortController;

    console.log('Setting productsLoading to true');
    setProductsLoading(true);

    try {
      console.log('Calling products API...');
      const productsResponse = await getVariants({
        ...sendObject,
        signal: abortController.signal
      });

      console.log('Products API response received');

      if (productsResponse.data?.success === 'ok') {
        console.log('Valid products data received, count:', productsResponse.data?.data?.length);
        // Only update state if component is still mounted
        if (isMounted.current) {
          setProducts(productsResponse.data);
        }
      } else {
        console.error('Products API returned unexpected format:', productsResponse);
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Error fetching products:', error);
      } else {
        console.log('Products request was aborted');
      }
    } finally {
      // Only update state if component is still mounted
      if (isMounted.current) {
        console.log('Setting productsLoading to false');
        setProductsLoading(false);

        if (getProductsRef.current === abortController) {
          console.log('Clearing getProductsRef');
          getProductsRef.current = null;
        }
      }
      console.log('============ getProducts COMPLETED ============');
    }
  };

  /**
   * Flag to track first load for URL parameter handling
   */
  const isFirstLoad = useRef(true);
  console.log('isFirstLoad.current:', isFirstLoad.current);

  // ===== FILTER CHANGE EFFECT =====

  /**
   * Effect that triggers when filters change
   */
  useEffect(() => {
    console.log('============ FILTERS CHANGE EFFECT ============');
    console.log('Filters object changed');
    console.log('Current state of isFirstLoad:', isFirstLoad.current);

    if (isFirstLoad.current) {
      console.log('First load detected, skipping filters effect');
      return;
    }

    // Compare with previous filters
    const currentFiltersJson = JSON.stringify(filters);
    const previousFiltersJson = JSON.stringify(previousFilters.current);
    const hasChanged = currentFiltersJson !== previousFiltersJson;

    console.log('Has filters changed?', hasChanged);

    // Only trigger API calls if filters actually changed
    if (hasChanged) {
      console.log('Filters have changed, preparing API calls');
      const sendObject = getSendFiltersObject();
      console.log('getSendFiltersObject result:', sendObject);

      // Fetch updated filter options
      console.log('Calling getNewFiltersList with trigger: filters');
      getNewFiltersList(
        {
          ...sendObject
        },
        'filters'
      );

      // Fetch products with updated filters
      console.log('Calling getProducts with updated filters');
      getProducts({
        ...sendObject,
        page: 1,
        limit: 20,
        orderBy: sort.sortBy || 'popularity',
        sortOrder: sort.sortOrder || 'desc'
      });
    } else {
      console.log('Filters have not changed, skipping API calls');
    }
    console.log('============ END FILTERS CHANGE EFFECT ============');
  }, [filters]);

  // ===== TAG CHANGE EFFECT =====

  /**
   * Effect that triggers when tag query parameter changes
   */
  useEffect(() => {
    console.log('============ TAG CHANGE EFFECT ============');
    console.log('Tag changed to:', tagParam);

    // Only process if we have a tag parameter
    if (tagParam) {
      // Pre-select the current tag in the filters
      const tagFilters = {
        category_id: null,
        min_price: null,
        max_price: null,
        brands: [],
        tags: [tagParam?.toUpperCase()],
        filters: {},
        last_changed: {}
      };

      // Fetch filters for this tag
      console.log('Calling getNewFiltersList with tag params');
      getNewFiltersList(tagFilters, 'tags');

      // Fetch products for this tag
      console.log('Calling getProducts with tag params');
      getProducts({
        ...tagFilters,
        page: 1,
        limit: 20,
        orderBy: sort.sortBy || 'popularity',
        sortOrder: sort.sortOrder || 'desc'
      });

      console.log('Setting page to 1');
      setPage(1);

      console.log('Scrolling to top');
      scrollToTop();
      isFirstLoad.current = false;
    }
    console.log('============ END TAG CHANGE EFFECT ============');
  }, [tagParam]);

  // ===== URL PARAMS CHANGE EFFECT =====

  /**
   * Effect to handle URL parameter changes
   */
  useEffect(() => {
    console.log('============ SEARCH PARAMS CHANGE EFFECT ============');
    console.log('location.search changed:', location.search);

    if (isFirstLoad.current) {
      console.log('First load, will be handled by tag change effect');
      return;
    }

    const queryParams = parseQueryParams(location.search);
    console.log('Parsed query params:', queryParams);

    // Add additional URL parameter handling if needed

    console.log('============ END SEARCH PARAMS CHANGE EFFECT ============');
  }, [location.search]);

  // ===== FILTER RESET FUNCTIONALITY =====

  /**
   * Resets all filters to their default values while maintaining the tag selection
   */
  const resetFilters = async () => {
    console.log('============ resetFilters CALLED ============');
    console.log('Resetting all filters except tag');

    if (!tagParam) {
      console.log('No tag parameter, nothing to reset');
      return;
    }

    console.log('Calling getNewFiltersList with reset values');
    getNewFiltersList(
      {
        category_id: null,
        min_price: null,
        max_price: null,
        brands: [],
        tags: [tagParam?.toUpperCase()],
        filters: {},
        last_changed: {}
      },
      'tags'
    );

    console.log('Calling getProducts with reset values');
    getProducts({
      page: page,
      limit: 20,
      orderBy: sort.sortBy,
      sortOrder: sort.sortOrder,
      category_id: null,
      min_price: null,
      max_price: null,
      brands: [],
      tags: [tagParam?.toUpperCase()],
      filters: {},
      last_changed: {}
    });

    console.log('Scrolling to top');
    scrollToTop();
    console.log('============ resetFilters COMPLETED ============');
  };

  // ===== PAGINATION STATE =====

  // Current page state
  const [page, setPage] = useState(1);
  console.log('Current page:', page);

  /**
   * Handles pagination changes
   */
  const handlePagination = (e, p) => {
    console.log('============ handlePagination CALLED ============');
    console.log('Changing page from', page, 'to', p);

    setPage(p);

    console.log('Calling getProducts for new page');
    getProducts({
      ...getSendFiltersObject(),
      page: p,
      limit: 20,
      orderBy: sort.sortBy,
      sortOrder: sort.sortOrder
    });

    console.log('Scrolling to top');
    scrollToTop();
    console.log('============ handlePagination COMPLETED ============');
  };

  // ===== SORTING STATE =====

  // Sort state with default sorting by popularity descending
  const [sort, setSort] = useState({
    sortBy: 'popularity',
    sortOrder: 'desc'
  });
  console.log('Current sort:', sort);

  // Reference to previous sort state for comparison
  const sortPrevious = useRef(sort);

  /**
   * Effect that triggers when sort changes
   */
  useEffect(() => {
    console.log('============ SORT CHANGE EFFECT ============');
    console.log('Sort changed:', sort);

    const currentSortJson = JSON.stringify(sort);
    const previousSortJson = JSON.stringify(sortPrevious.current);
    const hasChanged = currentSortJson !== previousSortJson;

    console.log('Has sort changed?', hasChanged);

    if (hasChanged) {
      console.log('Sort has changed, calling getProducts');
      getProducts({
        ...getSendFiltersObject(),
        page: page,
        limit: 20,
        orderBy: sort.sortBy,
        sortOrder: sort.sortOrder
      });

      console.log('Updating sortPrevious reference');
      sortPrevious.current = sort;
    } else {
      console.log('Sort has not changed, skipping API call');
    }
    console.log('============ END SORT CHANGE EFFECT ============');
  }, [sort]);

  // ===== UTILITY FUNCTIONS =====

  /**
   * Transforms filters state into an object for API requests
   * Ensures the current tag is always included in the tags array
   */
  const getSendFiltersObject2 = (filters) => {
    console.log('============ getSendFiltersObject2 CALLED ============');
    console.log('Input filters:', Object.keys(filters));

    // Extract selected brands
    const brands = filters?.basics?.brands?.reduce((acc, brand) => {
      if (brand.is_selected) {
        acc.push(brand.id);
      }
      return acc;
    }, []);
    console.log('Selected brands:', brands);

    // Extract selected tags, ensuring current tag is included
    const tags = filters?.basics?.tags?.reduce((acc, tag) => {
      if (tag.is_selected) {
        acc.push(tag.tag);
      }
      return acc;
    }, []);

    // Always include the current tag
    if (tagParam && !tags.includes(tagParam.toUpperCase())) {
      tags.push(tagParam.toUpperCase());
    }

    console.log('Selected tags with current tag:', tags);

    // Extract selected dynamic filters
    const dynamicFilters = filters?.dynamics
      ?.filter((filter) => filter.values.some((value) => value.is_selected))
      .reduce((acc, filter) => {
        acc[filter.id] = filter.values
          .filter((value) => value.is_selected)
          .map((value) => value.id);
        return acc;
      }, {});
    console.log('Dynamic filters:', dynamicFilters);

    // Create the final request object
    const result = {
      category_id: null, // No category for tag pages
      min_price: filters?.basics?.price?.current_values?.min || null,
      max_price: filters?.basics?.price?.current_values?.max || null,
      brands: brands || [],
      tags: tags || (tagParam ? [tagParam?.toUpperCase()] : []),
      filters: dynamicFilters || {},
      last_changed: filters?.lastChanged || {}
    };

    console.log('getSendFiltersObject2 result:', result);
    console.log('============ getSendFiltersObject2 COMPLETED ============');
    return result;
  };

  /**
   * Transforms the current filters state into an object for API requests
   */
  const getSendFiltersObject = () => {
    console.log('============ getSendFiltersObject CALLED ============');

    const brands = filters?.basics?.brands?.reduce((acc, brand) => {
      if (brand.is_selected) {
        acc.push(brand.id);
      }
      return acc;
    }, []);
    console.log('Selected brands:', brands);

    // Extract selected tags, ensuring current tag is included
    const tags = filters?.basics?.tags?.reduce((acc, tag) => {
      if (tag.is_selected) {
        acc.push(tag.tag);
      }
      return acc;
    }, []);

    // Always include the current tag
    if (tagParam && !tags.includes(tagParam.toUpperCase())) {
      tags.push(tagParam.toUpperCase());
    }

    console.log('Selected tags with current tag:', tags);

    const dynamicFilters = filters?.dynamics
      ?.filter((filter) => filter.values.some((value) => value.is_selected))
      .reduce((acc, filter) => {
        acc[filter.id] = filter.values
          .filter((value) => value.is_selected)
          .map((value) => value.id);
        return acc;
      }, {});
    console.log('Dynamic filters:', dynamicFilters);

    const result = {
      category_id: null, // No category for tag pages
      min_price: filters?.basics?.price?.current_values?.min || null,
      max_price: filters?.basics?.price?.current_values?.max || null,
      brands: brands || [],
      tags: tags || (tagParam ? [tagParam?.toUpperCase()] : []),
      filters: dynamicFilters || {},
      last_changed: filters?.lastChanged || {}
    };

    console.log('getSendFiltersObject result:', result);
    console.log('============ getSendFiltersObject COMPLETED ============');
    return result;
  };

  /**
   * Parses URL query parameters into filter, sort, and pagination objects
   */
  const parseQueryParams = (queryString) => {
    console.log('============ parseQueryParams CALLED ============');
    console.log('Query string:', queryString);

    const params = new URLSearchParams(queryString);
    const filtersObject = {
      min_price: null,
      max_price: null,
      brands: [],
      tags: tagParam ? [tagParam?.toUpperCase()] : [], // Always include current tag
      filters: {},
      last_changed: {}
    };
    const sortObject = {
      sortBy: undefined,
      sortOrder: undefined
    };
    let page = undefined;

    // Parse min_price and max_price
    if (params.has('min_price')) {
      filtersObject.min_price =
        params.get('min_price') === 'null' ? null : parseInt(params.get('min_price'), 10);
      console.log('Parsed min_price:', filtersObject.min_price);
    }
    if (params.has('max_price')) {
      filtersObject.max_price =
        params.get('max_price') === 'null' ? null : parseInt(params.get('max_price'), 10);
      console.log('Parsed max_price:', filtersObject.max_price);
    }

    // Parse brands
    if (params.has('brands')) {
      filtersObject.brands = params.get('brands').split(',').map(Number);
      console.log('Parsed brands:', filtersObject.brands);
    }

    // Parse tags (but always keep current tag)
    if (params.has('tags')) {
      const urlTags = params.get('tags').split(',');
      // Only overwrite if the passed tag is the one we're already using
      // This prevents multiple tags being used
      if (urlTags.length === 1 && urlTags[0].toUpperCase() === tagParam?.toUpperCase()) {
        filtersObject.tags = [tagParam?.toUpperCase()];
      }
      console.log('Parsed tags with current tag:', filtersObject.tags);
    }

    // Parse dynamic filters (filter_*)
    params.forEach((value, key) => {
      if (key.startsWith('filter_')) {
        const filterId = key.replace('filter_', '');
        filtersObject.filters[filterId] = value.split(',').map(Number);
        console.log(`Parsed filter_${filterId}:`, filtersObject.filters[filterId]);
      }
    });

    // Parse last_changed
    if (params.has('last_changed_type')) {
      filtersObject.last_changed.type = params.get('last_changed_type');
      console.log('Parsed last_changed_type:', filtersObject.last_changed.type);
    }
    if (params.has('last_changed_filter')) {
      filtersObject.last_changed.filter = parseInt(params.get('last_changed_filter'), 10);
      console.log('Parsed last_changed_filter:', filtersObject.last_changed.filter);
    }

    // Parse sortBy and sortOrder
    if (params.has('sort_by')) {
      sortObject.sortBy = params.get('sort_by');
      console.log('Parsed sort_by:', sortObject.sortBy);
    }
    if (params.has('sort_order')) {
      sortObject.sortOrder = params.get('sort_order');
      console.log('Parsed sort_order:', sortObject.sortOrder);
    }

    // Parse page
    if (params.has('page')) {
      page = parseInt(params.get('page'), 10);
      console.log('Parsed page:', page);
    }

    const result = {
      filtersObject,
      sortObject,
      page
    };

    console.log('parseQueryParams result:', result);
    console.log('============ parseQueryParams COMPLETED ============');
    return result;
  };

  /**
   * Builds URL query parameters from filter, sort, and pagination objects
   */
  const buildQueryParams = (filtersObject, sortObject, page) => {
    console.log('============ buildQueryParams CALLED ============');
    console.log('Filters object:', filtersObject);
    console.log('Sort object:', sortObject);
    console.log('Page:', page);

    const params = new URLSearchParams();

    // Always include the tag
    if (tagParam) {
      params.set('tags', tagParam);
      console.log('Added tag to URL:', tagParam);
    }

    // Price range
    if (filtersObject.min_price !== undefined) {
      params.set('min_price', filtersObject.min_price);
      console.log('Added min_price to URL:', filtersObject.min_price);
    }
    if (filtersObject.max_price !== undefined) {
      params.set('max_price', filtersObject.max_price);
      console.log('Added max_price to URL:', filtersObject.max_price);
    }

    // Brands
    if (filtersObject.brands?.length) {
      params.set('brands', filtersObject.brands.join(','));
      console.log('Added brands to URL:', filtersObject.brands.join(','));
    }

    // We don't add tags to the URL as that would conflict with our main tag

    // Dynamic Filters
    if (filtersObject.filters && Object.keys(filtersObject.filters).length > 0) {
      for (const key in filtersObject.filters) {
        params.set(`filter_${key}`, filtersObject.filters[key].join(','));
        console.log(`Added filter_${key} to URL:`, filtersObject.filters[key].join(','));
      }
    }

    // Last Changed
    if (filtersObject.last_changed.filter !== undefined) {
      params.set('last_changed_type', filtersObject.last_changed.type);
      params.set('last_changed_filter', filtersObject.last_changed.filter);
      console.log('Added last_changed to URL');
    }

    // Sort parameters
    if (sortObject.sortBy !== undefined) {
      params.set('sort_by', sortObject.sortBy);
      console.log('Added sort_by to URL:', sortObject.sortBy);
    }
    if (sortObject.sortOrder !== undefined) {
      params.set('sort_order', sortObject.sortOrder);
      console.log('Added sort_order to URL:', sortObject.sortOrder);
    }

    // Pagination
    if (page) {
      params.set('page', page);
      console.log('Added page to URL:', page);
    }

    const result = params.toString();
    console.log('Final URL query params:', result);
    console.log('============ buildQueryParams COMPLETED ============');
    return result;
  };

  // Track mounted state to prevent state updates after unmounting
  useEffect(() => {
    isMounted.current = true;

    return () => {
      // Mark component as unmounted to prevent state updates
      isMounted.current = false;

      // Clean up any pending requests
      if (getFiltersRef.current) {
        console.log('Aborting filters request');
        getFiltersRef.current.abort();
      }
      if (getProductsRef.current) {
        console.log('Aborting products request');
        getProductsRef.current.abort();
      }
    };
  }, []);

  console.log('============ COMPONENT RENDER END ============');

  // ===== COMPONENT RENDER =====
  return (
    <div className='content lining-nums proportional-nums'>
      {/* Breadcrumbs navigation */}
      <Breadcrumbs />

      {/* Tag header */}
      <div className='flex flex-col gap-3 mb-6'>
        {tagIsSuccess && (
          <div className='flex items-center gap-4'>
            <span
              className='py-2 px-4 rounded-lg text-lg font-medium'
              style={{
                backgroundColor: tag?.background_color || '#f0f0f0',
                color: tag?.text_color || '#000000'
              }}
            >
              {tag?.tag}
            </span>
            <h3 className='font-semibold text-xl mm:text-2xl lg:text-4xl text-colBlack'>
              Товары со статусом {tag?.tag}
            </h3>
          </div>
        )}
        {tagIsLoading && <div className='h-12 w-48 bg-gray-200 animate-pulse rounded'></div>}
      </div>

      {/* Main catalog layout with sidebar and content */}
      <div className='flex pb-10 min-h-[420px]'>
        {/* Sidebar with filters - hidden on mobile */}
        <div className='md:block hidden basis-1/4 mr-5'>
          <CatalogSidebar
            setFiltersModalOpen={setFiltersModalOpen}
            filters={filters}
            setFilters={setFilters}
            trigger={trigger}
            setTrigger={setTrigger}
            resetFilters={resetFilters}
            filtersIsLoading={filtersLoading}
            filtersBlock={filtersBlock}
          />
        </div>

        {/* Main content with products */}
        <CatalogContent
          setFiltersModalOpen={setFiltersModalOpen}
          products={products}
          getVariantsIsLoading={productsLoading}
          page={page}
          handlePagination={handlePagination}
          sort={sort}
          setSort={setSort}
        />
      </div>

      {/* Additional promotional components */}
      <Promotions />
      <Brands />
      <Advantages />

      {/* Mobile filters modal */}
      <AllFiltersModal
        open={filtersModalOpen}
        setOpen={setFiltersModalOpen}
        filters={filters}
        setFilters={setFilters}
        trigger={trigger}
        setTrigger={setTrigger}
        resetFilters={resetFilters}
        filtersIsLoading={filtersLoading}
        filtersBlock={filtersBlock}
      />
    </div>
  );
};

export default TagProducts;
