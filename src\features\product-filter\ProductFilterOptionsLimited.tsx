import { useProductFilterOptionLimited } from './hook/useProductFilterOptionLimited';
import { FilterOptionColor } from './ui/Options/FilterOptionColor';
import { FilterOptionRange } from './ui/Options/FilterOptionRange';
import { FilterOptionText } from './ui/Options/FilterOptionText';
import { FilterOptionVisual } from './ui/Options/FilterOptionVisual';

export const ProductFilterOptionsLimited = () => {
  const { visible, bodyRef, isMore, limitedData, onChange, disabled, handleModal } =
    useProductFilterOptionLimited();

  if (!limitedData) return null;

  const option = () => {
    switch (limitedData.type) {
      case 'color': {
        return <FilterOptionColor data={limitedData} onChange={onChange} disabled={disabled} />;
      }
      case 'text': {
        return <FilterOptionText data={limitedData} onChange={onChange} disabled={disabled} />;
      }
      case 'image': {
        return <FilterOptionVisual data={limitedData} onChange={onChange} disabled={disabled} />;
      }
    }
  };

  const options = () => {
    switch (limitedData.input_type) {
      case 'multiple': {
        return option();
      }
      case 'range': {
        return <FilterOptionRange data={limitedData} onChange={onChange} disabled={disabled} />;
      }
    }
  };

  return (
    <div
      className={`${visible ? 'overflow-auto' : 'overflow-hidden'} transition-all will-change-[height] lining-nums`}
      ref={bodyRef}
    >
      {options()}
      {isMore && (
        <button
          className='pt-2 w-full text-left text-colGreen font-semibold text-[15px]'
          onClick={handleModal}
          disabled={disabled}
        >
          Посмотреть ещё
        </button>
      )}
    </div>
  );
};
