import { useFilterGroup } from './hook';
import { FilterGroupContext, FilterGroupContextProps } from './model/FilterGroupContext';

interface FilterGroupProps {
  children: React.ReactNode;
}

export const FilterGroup: React.FC<FilterGroupProps & FilterGroupContextProps> = ({
  children,
  ...other
}) => {
  const contextValue = useFilterGroup(other);

  return (
    <FilterGroupContext.Provider value={contextValue}>
      <div>{children}</div>
    </FilterGroupContext.Provider>
  );
};
