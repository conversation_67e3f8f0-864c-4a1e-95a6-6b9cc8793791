// src/shared/ui/ErrorBoundary/ErrorBoundaryWrapper.tsx
import type React from 'react';
import { useEffect } from 'react';

import { useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';

import ErrorBoundary from './ErrorBoundary';
import { errorService } from '@/shared/lib/services/errorService';
import { RootState } from '@/app/providers/store';

interface WrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode | ((error: Error, reset: () => void) => React.ReactNode);
  showToast?: boolean;
  componentName?: string;
  logError?: boolean;
}

/**
 * Wrapper for ErrorBoundary that includes route information and sets up global error listeners
 */
export const ErrorBoundaryWrapper: React.FC<WrapperProps> = ({
  children,
  fallback,
  showToast,
  componentName,
  logError,
}) => {
  const location = useLocation();
  const user = useSelector((state: RootState) => state.user);
  
  // Setup global error listeners once on app mount
  useEffect(() => {
    // Make Redux state accessible to the error service
    window.__REDUX_STATE__ = { user };
    
    // Setup global error listeners for unhandled errors & promise rejections
    errorService.setupGlobalErrorListeners();
  }, [user]);

  return (
    <ErrorBoundary
      fallback={fallback}
      showToast={showToast}
      logError={logError}
      resetCondition={location.pathname}
      additionalInfo={{
        route: location.pathname,
        componentName: componentName || 'Unknown',
        search: location.search,
        hash: location.hash,
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

export default ErrorBoundaryWrapper;
