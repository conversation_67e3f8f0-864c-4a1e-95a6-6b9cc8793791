import type { AdditionalServerResponseData } from '@/shared/types/AdditionalServerResponseData';

/**
 * Represents a single pickup point location
 */
export interface PickupPoint {
  /** Unique identifier for the pickup point */
  uuid: string;
  /** Name of the pickup point */
  name: string;
  /** Country where the pickup point is located */
  country: string;
  /** Region/state/province where the pickup point is located */
  region: string;
  /** City where the pickup point is located */
  city: string;
  /** Full address of the pickup point */
  address: string;
  /** Latitude coordinate for map display */
  lat: string;
  /** Longitude coordinate for map display */
  lon: string;
}

/**
 * Response from the pickup points API
 */
export interface GetPickupPointsResponse extends AdditionalServerResponseData {
  /** Array of available pickup points */
  data: PickupPoint[];
}

/**
 * Request parameters for the pickup points API
 */
export interface GetPickupPointsRequest {
  /** Order number to get pickup points for */
  order_number: string;
}

/**
 * Request parameters for assigning a pickup point to an order
 */
export interface AssignPickupPointRequest {
  /** Order number to assign pickup point to */
  order_number: string;
  /** Delivery type (pickup or delivery) */
  delivery_type: 'pickup';
  /** Array of item IDs to assign to this pickup point */
  items?: number[];
  /** UUID of the pickup point to assign */
  pickup_point_uuid: string;
  /** Delivery address for courier delivery */
  delivery_address?: string;
}

/**
 * Response from the assign pickup point API
 */
export interface AssignPickupPointResponse extends AdditionalServerResponseData {
  /** Success status */
  success: boolean;
}
