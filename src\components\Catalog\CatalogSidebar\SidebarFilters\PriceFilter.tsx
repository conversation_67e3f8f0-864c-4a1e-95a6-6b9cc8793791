// src/components/PriceFilter.jsx

import React, { useEffect, useRef, useState } from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Slider,
} from "@mui/material";

import { ArrowIcon } from "@/shared/ui";
import {CTextField} from "@/shared/ui";

import { useDebounce  } from "react-use";

/**
 * Price filter component with slider and input fields
 * 
 * This component handles price range filtering with these key features:
 * 1. Slider for visual price range selection
 * 2. Input fields for precise min/max values
 * 3. Debounced updates to prevent excessive API calls
 * 4. Special handling for category changes
 * 
 * The component maintains several pieces of state:
 * - priceFilter: The input field values
 * - sliderValue: The slider position values
 * - previousValues: Reference to track actual value changes
 * 
 * @param {Object} filters - Current filters state from parent
 * @param {Function} setFilters - Function to update parent filters state
 * @param {string} trigger - Identifies what triggered filter update
 * @param {Function} setTrigger - Function to update trigger value
 */
const PriceFilter = React.memo(function PriceFilter({ filters, setFilters, trigger, setTrigger}) {
  console.log('============ PRICE FILTER RENDER ============');
  console.log('Trigger in PriceFilter:', trigger);
  console.log('Price filter data available:', !!filters?.basics?.price);
  console.log('Min/Max from filters:', 
    filters?.basics?.price?.min,
    filters?.basics?.price?.max);
  console.log('Current values from filters:',
    filters?.basics?.price?.current_values?.min,
    filters?.basics?.price?.current_values?.max);
  
  // Track previous price values to prevent unnecessary updates
  const previousValues = useRef({});
  console.log('Previous values in ref:', previousValues.current);

  // State for the input field values
  const [priceFilter, setPriceFilter] = useState([
    Number(filters?.basics?.price?.min || 0),
    Number(filters?.basics?.price?.max || 0),
  ]);
  console.log('Current priceFilter state:', priceFilter);
  
  // State for the slider position
  // This is separate from priceFilter to handle cases where input values
  // are temporarily invalid during user typing
  const [sliderValue, setSliderValue] = useState([
    Number(filters?.basics?.price?.min || 0),
    Number(filters?.basics?.price?.max || 0),
  ]);
  console.log('Current sliderValue state:', sliderValue);

  // ===== INPUT FIELD HANDLERS =====

  /**
   * Handles changes to the minimum price input field
   * Updates only the priceFilter state, not the slider
   * 
   * @param {Event} event - Input change event
   */
  const handleChangeMin = (event) => {
    console.log('Min price input changed to:', event.target.value);
    const newMin = event.target.value;
    setPriceFilter((prev) => [newMin, prev[1]]);
  };

  /**
   * Handles changes to the maximum price input field
   * Updates only the priceFilter state, not the slider
   * 
   * @param {Event} event - Input change event
   */
  const handleChangeMax = (event) => {
    console.log('Max price input changed to:', event.target.value);
    const newMax = event.target.value;
    setPriceFilter((prev) => [prev[0], newMax]);
  };

  /**
   * Validates and corrects the minimum price value when input field loses focus
   * Ensures min value is within allowed range and not greater than max value
   * Updates both priceFilter and sliderValue to keep them in sync
   */
  const validateAndSetMin = () => {
    console.log('validateAndSetMin called, current value:', priceFilter[0]);
    
    // Use filter min if input is empty, otherwise convert to number
    let min =
      priceFilter[0] === ""
        ? filters?.basics?.price?.min
        : Number(priceFilter[0]);
    console.log('Initial min after conversion:', min);
    
    // Ensure min is not less than allowed minimum
    min = Math.max(min, filters?.basics?.price?.min);
    console.log('Min after enforcing minimum:', min);

    // Ensure min is not greater than current max
    if (min > priceFilter[1]) {
      min = priceFilter[1];
      console.log('Min adjusted to not exceed max:', min);
    }

    // Update both state values to keep them in sync
    console.log('Setting price filter min to:', min);
    setPriceFilter((prev) => [min, prev[1]]);
    console.log('Setting slider value min to:', min);
    setSliderValue([min, priceFilter[1]]);

    // Note: we don't call setFilters directly here
    // Instead we rely on the debounced update based on priceFilter changes
  };

  /**
   * Validates and corrects the maximum price value when input field loses focus
   * Ensures max value is within allowed range and not less than min value
   * Updates both priceFilter and sliderValue to keep them in sync
   */
  const validateAndSetMax = () => {
    console.log('validateAndSetMax called, current value:', priceFilter[1]);
    
    // Use filter max if input is empty, otherwise convert to number
    let max =
      priceFilter[1] === ""
        ? filters?.basics?.price?.max
        : Number(priceFilter[1]);
    console.log('Initial max after conversion:', max);
    
    // Ensure max is not greater than allowed maximum
    max = Math.min(max, filters?.basics?.price?.max);
    console.log('Max after enforcing maximum:', max);

    // Ensure max is not less than current min
    if (max < priceFilter[0]) {
      max = priceFilter[0];
      console.log('Max adjusted to not be below min:', max);
    }

    // Update both state values to keep them in sync
    console.log('Setting price filter max to:', max);
    setPriceFilter((prev) => [prev[0], max]);
    console.log('Setting slider value max to:', max);
    setSliderValue([priceFilter[0], max]);

    // Note: we don't call setFilters directly here
    // Instead we rely on the debounced update based on priceFilter changes
  };

  /**
   * Handles changes from the slider component
   * Updates both priceFilter and sliderValue states
   * 
   * @param {Event} event - Slider change event
   * @param {Array} newValue - New slider value array [min, max]
   */
  const handleSliderChange = (event, newValue) => {
    console.log('Slider changed to:', newValue);
    
    // Convert values to numbers and handle potential NaN values
    const [newMin, newMax] = newValue.map(val => Number(val) || 0);
    console.log('Setting price filter to:', [newMin, newMax]);
    setPriceFilter([newMin, newMax]);
    console.log('Setting slider value to:', [newMin, newMax]);
    setSliderValue([newMin, newMax]);
  };

  // ===== FILTER STATE SYNCHRONIZATION =====

  /**
   * Updates the local state when the filters prop changes
   * This is important for:
   * 1. Initial load
   * 2. When filters are reset
   * 3. When category changes (which changes available price range)
   */
  useEffect(() => {
    console.log('============ PRICE FILTER PROP CHANGE EFFECT ============');
    console.log('Filters prop changed');
    
    // Only update if values actually changed
    const currentMin = filters?.basics?.price?.current_values?.min;
    const currentMax = filters?.basics?.price?.current_values?.max;
    const prevMin = previousValues.current[0];
    const prevMax = previousValues.current[1];
    
    console.log('Current values from props:', currentMin, currentMax);
    console.log('Previous values from ref:', prevMin, prevMax);
    
    const valuesChanged = prevMin !== currentMin || prevMax !== currentMax;
    console.log('Values have changed?', valuesChanged);
    
    if (valuesChanged) {
      // Safe defaults that make logical sense
      const safeMin = typeof currentMin === 'number' ? currentMin : Number(filters?.basics?.price?.min || 0);
      const safeMax = typeof currentMax === 'number' ? currentMax : Number(filters?.basics?.price?.max || 0);
      console.log('Safe values calculated:', safeMin, safeMax);
      
      // Update state and slider values
      console.log('Setting price filter to:', [safeMin, safeMax]);
      setPriceFilter([safeMin, safeMax]);
      console.log('Setting slider value to:', [safeMin, safeMax]);
      setSliderValue([safeMin, safeMax]);
      
      // Update reference
      console.log('Updating previous values ref to:', [safeMin, safeMax]);
      previousValues.current = [safeMin, safeMax];
    }
    console.log('============ END PRICE FILTER PROP CHANGE EFFECT ============');
  }, [filters]);

  /**
   * Special handling for category, tag, or brand changes
   * Resets the price filter to the available range for the new context
   */
  useEffect(() => {
    console.log('============ PRICE FILTER TRIGGER CHANGE EFFECT ============');
    console.log('Trigger changed to:', trigger);
    
    // Special handling when category, tag, or brand changes
    if (trigger === "categoryId" || trigger === "tags" || trigger === "brands") {
      console.log('Special trigger detected, resetting price filter');
      
      // Safe values with proper fallbacks
      const currentMin = filters?.basics?.price?.current_values?.min;
      const currentMax = filters?.basics?.price?.current_values?.max;
      const minValue = typeof currentMin === 'number' ? currentMin : Number(filters?.basics?.price?.min || 0);
      const maxValue = typeof currentMax === 'number' ? currentMax : Number(filters?.basics?.price?.max || 0);
      console.log('Calculated reset values:', minValue, maxValue);
      
      // Reset price filter to the available range
      console.log('Setting price filter to reset values');
      setPriceFilter([minValue, maxValue]);
      console.log('Setting slider value to reset values');
      setSliderValue([minValue, maxValue]);

      // Update previous values to prevent unnecessary updates
      console.log('Updating previous values ref to reset values');
      previousValues.current = [minValue, maxValue];
    }
    console.log('============ END PRICE FILTER TRIGGER CHANGE EFFECT ============');
  }, [trigger, filters]);

  /**
   * Debounced update to parent filter state
   * Prevents excessive API calls while user is adjusting values
   * 
   * Key considerations in this implementation:
   * 1. Skip updates on initial load and category changes
   * 2. Skip updates if values haven't actually changed
   * 3. Create a clean copy of the filters state for updates
   * 4. Track the last filter changed for API optimization
   */
  useDebounce(
    () => {
      console.log('============ PRICE FILTER DEBOUNCE CALLBACK ============');
      console.log('Debounce triggered after price change');
      console.log('Current trigger value:', trigger);
      
      // Skip during initial load or category/tag/brand changes
      if (trigger === "categoryId" || trigger === "tags" || trigger === "brands") {
        console.log('Special trigger detected, clearing trigger and skipping update');
        setTrigger("");
        return;
      }

      // Skip if price data is not available
      if (!filters?.basics?.price) {
        console.log('No price filter data available, skipping');
        return;
      }

      // Skip if values haven't changed (prevents unnecessary updates)
      const prevMin = Number(previousValues.current[0]);
      const prevMax = Number(previousValues.current[1]);
      const currMin = Number(priceFilter[0]);
      const currMax = Number(priceFilter[1]);
      
      console.log('Previous values:', prevMin, prevMax);
      console.log('Current values:', currMin, currMax);
      
      const valuesUnchanged = prevMin === currMin && prevMax === currMax;
      console.log('Values unchanged?', valuesUnchanged);
      
      if (valuesUnchanged) {
        console.log('Values have not changed, skipping update');
        return;
      }

      // Create a clean copy of the current filters state
      console.log('Creating clean copy of filters state');
      const currentState = JSON.parse(JSON.stringify(filters));

      // Ensure current_values object exists
      if (!currentState.basics.price.current_values) {
        console.log('Creating missing current_values object');
        currentState.basics.price.current_values = {};
      }
      
      // Update the price filter current values
      console.log('Setting current_values.min to', currMin);
      console.log('Setting current_values.max to', currMax);
      currentState.basics.price.current_values = {
        min: currMin,
        max: currMax,
      };

      // Track which filter was last changed (for API optimization)
      console.log('Setting lastChanged to basics.price');
      currentState.lastChanged = {
        type: "basics",
        filter: "price",
      };
      
      // Update reference and trigger parent state update
      console.log('Updating previous values ref');
      previousValues.current = [currMin, currMax];
      console.log('Calling setFilters with updated state');
      setFilters(currentState);
      console.log('============ END PRICE FILTER DEBOUNCE CALLBACK ============');
    },
    1000, // 1 second debounce delay
    [priceFilter, filters, trigger] // Only trigger when these dependencies change
  );

  console.log('============ END PRICE FILTER RENDER ============');
  
  return (
    <Accordion
      sx={{
        boxShadow: "none",
        padding: 0,
        margin: 0,
        border: "none",
        "&:before": {
          display: "none",
        },
        "&.Mui-expanded": {
          margin: 0,
        },
      }}
      defaultExpanded
      disableGutters
    >
      <AccordionSummary
        sx={{ padding: 0, flexDirection: "row-reverse", gap: "8px" }}
        style={{ minHeight: 0 }}
        expandIcon={<ArrowIcon className="!w-4 !h-4 rotate-[180deg]" />}
      >
        <span className="font-semibold text-colBlack">Цена, ₽</span>
      </AccordionSummary>
      <AccordionDetails sx={{ padding: 0 }}>
        <Slider
          sx={{ color: "#15765B" }}
          size="small"
          getAriaLabel={() => "Price range"}
          value={[Number(sliderValue[0]) || 0, Number(sliderValue[1]) || 0]}
          min={Number(filters?.basics?.price?.min) || 0}
          max={Number(filters?.basics?.price?.max) || 100000}
          onChange={handleSliderChange}
          valueLabelDisplay="auto"
        />
        <Box>
          <div className="grid grid-cols-2 gap-3 pb-3">
            <CTextField
              label={`от ${filters?.basics?.price?.min}`}
              name="min_price"
              type="number"
              value={priceFilter[0]}
              onChange={handleChangeMin}
              onBlur={validateAndSetMin}
            />
            <CTextField
              label={`до ${filters?.basics?.price?.max}`}
              name="max_price"
              type="number"
              value={priceFilter[1]}
              onChange={handleChangeMax}
              onBlur={validateAndSetMax}
            />
          </div>
        </Box>
      </AccordionDetails>
    </Accordion>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function to prevent unnecessary re-renders
  const shouldUpdate = !(
    prevProps.trigger === nextProps.trigger &&
    prevProps.filters?.basics?.price?.min === nextProps.filters?.basics?.price?.min &&
    prevProps.filters?.basics?.price?.max === nextProps.filters?.basics?.price?.max &&
    prevProps.filters?.basics?.price?.current_values?.min === 
      nextProps.filters?.basics?.price?.current_values?.min &&
    prevProps.filters?.basics?.price?.current_values?.max === 
      nextProps.filters?.basics?.price?.current_values?.max
  );
  
  console.log('PriceFilter should update?', shouldUpdate);
  console.log('Prev trigger:', prevProps.trigger, 'Next trigger:', nextProps.trigger);
  console.log('Prev min:', prevProps.filters?.basics?.price?.min, 'Next min:', nextProps.filters?.basics?.price?.min);
  console.log('Prev max:', prevProps.filters?.basics?.price?.max, 'Next max:', nextProps.filters?.basics?.price?.max);
  
  return !shouldUpdate;
});

export default PriceFilter;