import { ModalFilterItem, ObserverItem } from '@/shared/types/observer';
import { ProductFilterURLMultiple } from '@/types/Filters/ProductFilterURL';
import { useEffect, useState } from 'react';

export const useModalItemFilter = (data: ObserverItem<ModalFilterItem>) => {
  const [selects, setSelects] = useState<ProductFilterURLMultiple[]>([]);
  const [isSuccess, setIsSuccess] = useState(false);

  const handleClick = (item: { alone: ProductFilterURLMultiple }) => {
    const alone = item.alone;
    const isAlone = selects.findIndex((select) => select.id === alone.id);
    if (isAlone > -1) {
      setSelects((prev) => [...prev.filter((item) => item.id !== alone.id)]);
    } else {
      const item: ProductFilterURLMultiple = {
        parentId: data.data.id,
        id: alone.id,
        type: 'multiple',
        value: true
      };
      const items: ProductFilterURLMultiple[] = [...selects, item];
      setSelects(items);
    }
  };

  useEffect(() => {
    if (selects.length) setIsSuccess(true);
    else setIsSuccess(false);
  }, [selects]);

  return { selects, isSuccess, handleClick };
};
