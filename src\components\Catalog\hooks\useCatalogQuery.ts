import { buildQuery, parseQuery, QueryFilters } from '@/shared/lib/queryUtils';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

export function useCatalogFilters() {
  const navigate = useNavigate();
  const { search } = useLocation();

  // state хранит только наши filter_*, range_*, toggle_* параметры
  const [filters, setFilters] = useState<QueryFilters>(() => parseQuery(search));

  // При изменении URL (back/forward или ручная правка) синхронизируем state
  useEffect(() => {
    setFilters(parseQuery(search));
  }, [search]);

  // Вызываем при любом изменении filters
  const syncToUrl = (newFilters: QueryFilters) => {
    const q = buildQuery(newFilters);
    navigate(`?${q}`, { replace: true });
  };

  return { filters, setFilters, syncToUrl };
}
