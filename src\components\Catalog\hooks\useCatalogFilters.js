import { useState, useRef, useEffect, useCallback } from 'react';
import { useGetFiltersMutation } from '@/entities/filter';
import { buildFilterObject } from '../utils/filterTransforms';

/**
 * Custom hook for handling catalog filter state and operations
 * 
 * @param {string} categoryId - Current category ID from URL
 * @returns {Object} Object containing filter state and operations
 */
export const useCatalogFilters = (categoryId) => {
  // Main filters state
  const [filters, setFilters] = useState({
    basics: {
      price: {
        min: 0,
        max: 0,
      },
      tags: [],
      brands: [],
      rating: [],
    },
    dynamics: [],
    more: [],
    category_chain: [],
  });
  
  // Loading states
  const [filtersLoading, setFiltersLoading] = useState(false);
  const [filtersBlock, setFiltersBlock] = useState(false);
  const [filtersError, setFiltersError] = useState(null);
  
  // Trigger state for special handling
  const [trigger, setTrigger] = useState('');
  
  // Track previous filters for comparison
  const previousFilters = useRef({});
  
  // Get filters API mutation
  const [getFiltersMutation] = useGetFiltersMutation();
  
  // Reference to cancel in-flight requests
  const getFiltersRef = useRef(null);

  /**
   * Fetches new filter options based on current selection
   * 
   * @param {Object} sendObject - Filter parameters for API
   * @param {string} triggerType - What triggered the filter update
   */
  const getNewFiltersList = useCallback(async (sendObject, triggerType) => {
    // Cancel any in-flight request
    if (getFiltersRef.current) {
      getFiltersRef.current.abort();
    }
    
    // Create a new abort controller
    const abortController = new AbortController();
    getFiltersRef.current = abortController;
    
    // Reset error state
    setFiltersError(null);
    
    // Set appropriate loading state
    if (
      triggerType === 'categoryId' ||
      triggerType === 'tags' ||
      triggerType === 'brands'
    ) {
      setFiltersLoading(true);
    } else if (triggerType === 'filters') {
      setFiltersBlock(true);
    }
    
    try {
      // Add signal to request
      const requestObject = {
        ...sendObject,
        signal: abortController.signal,
      };
      
      // Call API
      const newFilters = await getFiltersMutation(requestObject);
      
      // Process response if not aborted
      if ('data' in newFilters) {
        // Process "more" filters
        const more = newFilters.data.more.map((obj) => ({
          ...obj,
          additional_filter: true,
        }));
        
        // Combine with regular dynamics
        const newDynamics = newFilters.data.dynamics.concat(more);
        
        // Create the updated state
        const newFiltersState = {
          ...filters,
          basics: newFilters.data.basics,
          dynamics: newDynamics,
        };
        
        // Update references and state
        previousFilters.current = newFiltersState;
        setFilters(newFiltersState);
        setTrigger(triggerType);
      } else if (newFilters.error) {
        console.error('Failed to fetch filters:', newFilters.error);
        setFiltersError(newFilters.error.message || 'Failed to load filters');
      }
    } catch (error) {
      // Only set error if not aborted
      if (error.name !== 'AbortError') {
        console.error('Error fetching filters:', error);
        setFiltersError(error.message || 'Failed to load filters');
      }
    } finally {
      // Reset loading states
      if (
        triggerType === 'categoryId' ||
        triggerType === 'tags' ||
        triggerType === 'brands'
      ) {
        setFiltersLoading(false);
      } else if (triggerType === 'filters') {
        setFiltersBlock(false);
      }
      
      // Clear abort controller reference if it matches
      if (getFiltersRef.current === abortController) {
        getFiltersRef.current = null;
      }
    }
  }, [filters, getFiltersMutation]);

  /**
   * Resets all filters to their default values
   */
  const resetFilters = useCallback(async () => {
    getNewFiltersList(
      {
        category_id:
          categoryId === 'tags' || categoryId === 'brands' ? null : categoryId,
        min_price: null,
        max_price: null,
        brands: [],
        tags: [],
        filters: {},
        last_changed: {},
      },
      'categoryId'
    );
  }, [categoryId, getNewFiltersList]);

  /**
   * Creates a filter object from current state for API requests
   * 
   * @returns {Object} Filter object for API
   */
  const getSendFiltersObject = useCallback(() => {
    return buildFilterObject(filters, categoryId);
  }, [filters, categoryId]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (getFiltersRef.current) {
        getFiltersRef.current.abort();
        getFiltersRef.current = null;
      }
    };
  }, []);

  return {
    filters,
    setFilters,
    filtersLoading,
    filtersBlock,
    filtersError,
    trigger,
    setTrigger,
    getNewFiltersList,
    resetFilters,
    getSendFiltersObject,
    previousFilters
  };
};
