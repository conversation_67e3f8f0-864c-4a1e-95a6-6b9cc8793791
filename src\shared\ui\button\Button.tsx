import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/shared/lib/utils';
import { Link } from 'react-router-dom';
import { type ReactNode, type ButtonHTMLAttributes } from 'react';

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-semibold ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none',
  {
    variants: {
      variant: {
        primary: 'bg-green-700 text-primary-foreground hover:bg-green-800 active:bg-green-900 disabled:bg-gray-400 disabled:text-gray-300',
        outline: 'border border-green-700 bg-background text-green-700 hover:bg-green-100 hover:text-green-800 active:bg-green-200 active:text-green-900 active:border-green-800 focus-visible:bg-green-100 focus-visible:border-green-700 focus-visible:text-green-800 focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 disabled:border-gray-400 disabled:text-gray-400 disabled:bg-transparent',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80 disabled:bg-gray-400 disabled:text-gray-300',
        ghost: 'text-gray-900 hover:bg-accent hover:text-accent-foreground active:bg-gray-200 disabled:text-gray-400',
        link: 'text-primary underline-offset-4 hover:underline active:text-green-900 active:underline focus-visible:bg-green-100 focus-visible:border focus-visible:border-green-700 focus-visible:text-green-800 focus-visible:no-underline focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 disabled:text-gray-400 disabled:no-underline',
        added: 'bg-gray-200 text-gray-500 hover:bg-gray-300 active:bg-gray-400 disabled:bg-gray-400 disabled:text-gray-300',
        'secondary-gray': 'bg-gray-100 text-gray-500 hover:bg-gray-200 active:bg-gray-300 disabled:bg-gray-300 disabled:text-gray-400',
        'tertiary-gray': 'text-gray-500 hover:text-green-700 active:bg-gray-100 focus-visible:bg-gray-100 focus-visible:text-green-700 disabled:text-gray-400',
      },
      size: {
        default: 'h-[58px] px-8 text-[15px]',
        sm: 'h-[32px] px-[30px] py-[8px] text-[14px]',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
      fullWidth: {
        true: 'w-full',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'default',
      fullWidth: false,
    },
  }
);

export interface ButtonProps
  extends ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  isLoading?: boolean;
  children: ReactNode;
  className?: string;
  to?: string;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, fullWidth, asChild = false, isLoading = false, disabled, children, to, ...props }, ref) => {
    const Comp = asChild ? Slot : to ? Link : 'button';

    const isDisabled = disabled || isLoading;

    const content = isLoading ? (
      <span className="animate-spin rounded-full h-5 w-5 border-b-2 border-current"></span>
    ) : (
      children
    );

    const commonProps = {
      className: cn(buttonVariants({ variant, size, fullWidth, className })),
      ref,
      disabled: isDisabled,
      ...props,
    };

    if (to && Comp === Link) {
      return (
        <Link to={to} {...commonProps}>
          {content}
        </Link>
      );
    }

    return (
      <Comp {...commonProps}>
        {content}
      </Comp>
    );
  }
);

Button.displayName = 'Button';

export { Button, buttonVariants };
