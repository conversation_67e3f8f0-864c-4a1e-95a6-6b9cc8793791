import type { ReactNode } from 'react';
import { createContext, useContext, useState, useCallback } from 'react';

import type { ModalTypes } from './types';

type ModalContent = {
  [K in keyof ModalTypes]: {
    type: K;
  } & ModalTypes[K];
}[keyof ModalTypes];

interface ModalContextType {
  showModal: (content: ModalContent) => void;
  hideModal: () => void;
  modalContent: ModalContent | null;
  isModalVisible: boolean;
  isLoading: boolean;
  setModalLoading: (loading: boolean) => void;
}

const ModalContext = createContext<ModalContextType>({
  showModal: () => {},
  hideModal: () => {},
  modalContent: null,
  isModalVisible: false,
  isLoading: false,
  setModalLoading: () => {},
});

export const ModalProvider = ({ children }: { children: ReactNode }) => {
  const [modalContent, setModalContent] = useState<ModalContent | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Use useCallback to memoize handlers
  const showModal = useCallback((content: ModalContent) => {
    // Set content first, then visibility
    setModalContent(content);
    // Use setTimeout to ensure content is set before showing
    setTimeout(() => {
      setIsModalVisible(true);
      setIsLoading(false);
    }, 0);
  }, []);

  const hideModal = useCallback(() => {
    setIsLoading(false);
    setIsModalVisible(false);
    // Clear content after animation
    setTimeout(() => {
      setModalContent(null);
    }, 300); // Typical animation duration
  }, []);

  const setModalLoading = useCallback((loading: boolean) => {
    setIsLoading(loading);
  }, []);

  return (
    <ModalContext.Provider
      value={{
        showModal,
        hideModal,
        modalContent,
        isModalVisible,
        isLoading,
        setModalLoading,
      }}
    >
      {children}
    </ModalContext.Provider>
  );
};

export const useModal = () => {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};
