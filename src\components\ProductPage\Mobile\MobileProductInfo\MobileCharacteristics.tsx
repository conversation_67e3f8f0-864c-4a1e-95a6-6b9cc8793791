// src/components/ProductPage/Mobile/MobileProductInfo/MobileCharacteristics.tsx
import React from 'react';

// Assuming Attribute and AttributeValue interfaces are defined or can be imported/adapted
interface AttributeValue {
  value: string | number;
  text: string;
  current?: boolean; // Optional as not directly used for display logic here but part of the structure
  available?: boolean; // Optional
  color?: string;
  second_color?: string;
}

interface Attribute {
  id: number | string;
  name: string;
  values: AttributeValue[];
}

interface ProductModificationAttribute {
    id: number | string;
    text: string; // The specific value text for this modification
    // other properties if relevant like value_id, etc.
}

interface ProductModification {
  sku: string | number;
  attributes?: ProductModificationAttribute[];
  // other modification-specific properties like price, image, etc.
}

interface ProductData {
  description: string;
  attributes?: Attribute[];
  // other base product properties
}

interface MobileCharacteristicsProps {
  current?: ProductModification | null; // current can be null or undefined
  product: ProductData;
}

const MobileCharacteristics: React.FC<MobileCharacteristicsProps> = ({ current, product }) => {
  return (
    <>
      <h3 className='text-2xl my-5 font-semibold'>Характеристики</h3>

      <div className='flex flex-wrap flex-col gap-3'>
        <div className='flex items-end basis-[calc(100%/2-10px)]'>
          <div className='shrink leading-none text-colDarkGray mr-1'>Код товара</div>
          <div className='grow border-b-2 border-dotted'></div>
          <div className='flex items-end leading-none shrink ml-1'>
            {current?.sku}
          </div>
        </div>
        {
          product?.attributes?.map((attribute) => {
            const modAttr = current?.attributes?.find(mod => mod.id === attribute.id);

            if (modAttr) {
              return (
                <div key={`${attribute.id}-mod`} className='flex items-end'>
                  <div className='shrink self-start leading-none text-colDarkGray mr-1'>{attribute.name}</div>
                  <div className='grow self-start h-4 border-b-2 border-dotted'></div>
                  <div className='flex text-end leading-none shrink ml-1 max-w-[50%] break-all'>
                    {modAttr.text}
                  </div>
                </div>
              );
            } else if (attribute.values && attribute.values[0]?.text) {
              return (
                <div key={attribute.id} className='flex items-end'>
                  <div className='shrink self-start leading-none text-colDarkGray mr-1'>{attribute.name}</div>
                  <div className='grow self-start h-4 border-b-2 border-dotted'></div>
                  <div className='flex text-end leading-none shrink ml-1 max-w-[50%] break-all'>
                    {attribute.values[0].text}
                  </div>
                </div>
              );
            }
            return null; // Ensure a value is always returned
          })
        }
      </div>

      <h3 className='text-2xl mt-5 mb-[10px] font-semibold'>Описание</h3>
      <div className='text-[14px] whitespace-pre-line'> {/* Added whitespace-pre-line for better formatting of description */}
        {product.description || 'Интерьерное напольное велюровое кресло для отдыха - это винтаж кресло в современном исполнении, которое станем ярким аксессуаром в вашем доме. Кресло изготовлено в России, на фабрике, которая занимает одно из ведущих мест в мебельном рынке. Невероятно удобное широкое мебельное кресло с подлокотниками буквально окутывает своим комфортом, располагая выпить чашечку чая за столиком, провести время за чтением любимой книги или посмотреть телевизор. Особую статность придают высокие деревянные ножки в цвете тёмный венге. Кресло имеет деревянный каркас из хвойного бруса и березовой фанеры. Наше интерьерное стулкресло отлично подойдет в гостиную и спальню, прекрасно впишется на кухню и в детскую комнату. А так же послужит дополнением для веранды или как мебель для дачи и сада, а так же просторной прихожей, коридора и балкона. Просторная лоджия может стать уголком релакса, если наше кресло и пуф окажутся там. Станет ярким элементом в салоне красоты, спа или в педикюрном кабинете, станет отличным местом работы для бровиста, так же подойдет для зон ожидания клиентов, шикарно дополнит офисное пространство в конференц зале. В нашем магазине представлены прикроватные тумбы, пуфики из этой же коллекции мебели, а также декоративные подушки, поэтому вы с лёгкостью под себя сможете составить комплект, а также купить его в подарок близкому человеку. Товар сертифицирован! Сборка комфортна и не требует подручных инструментов, достаточно лишь вкрутить опоры, которые идут в комплекте и ваше кресло готово. Почувствуйте домашний уют и насладитесь комфортом наших кресел!'}
      </div>
    </>
  );
}

export default MobileCharacteristics;
