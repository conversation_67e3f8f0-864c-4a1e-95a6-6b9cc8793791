import { ProductFilterURL } from '@/types/Filters/ProductFilterURL';

type FilterState = {
  [key: string]:
    | { type: 'range'; from: number; to: number; value: { from: number; to: number } }
    | { type: 'range'; value: null }
    | { type: 'toggle'; value: boolean }
    | { type: 'multiple'; value: (string | number)[] };
};

export function updateFilterState(prev: FilterState, data: ProductFilterURL): FilterState {
  const parentId = String(data.parentId);

  if (data.type === 'multiple' && 'id' in data) {
    const current = prev[parentId] as { type: 'multiple'; value: (string | number)[] } | undefined;
    const currentValue = current?.value ?? [];

    const isSelected = currentValue.includes(data.id.toString());
    const updatedValue = isSelected
      ? currentValue.filter((v) => v != data.id)
      : [...currentValue, data.id];

    if (updatedValue.length === 0) {
      const { [parentId]: _, ...rest } = prev;
      return rest;
    }

    return {
      ...prev,
      [parentId]: {
        type: 'multiple',
        value: updatedValue
      }
    };
  }

  if (data.type === 'range') {
    if (data.value === null) {
      const { [data.parentId]: _, ...rest } = prev;
      return rest;
    }

    return {
      ...prev,
      [data.parentId.toString()]: {
        type: 'range',
        value: {
          from: +data.value[0],
          to: +data.value[1]
        },
        from: +data.value[0],
        to: +data.value[1]
      }
    };
  }
}
