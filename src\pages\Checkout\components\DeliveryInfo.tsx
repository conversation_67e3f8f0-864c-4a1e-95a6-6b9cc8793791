import type React from 'react';
import { useEffect, useState, useMemo, useRef } from 'react';

import { Icon } from '@iconify-icon/react';
// @ts-ignore - Suppressing TypeScript errors for Leaflet until types are properly installed
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

import { useGetPickupPointsQuery, useAssignPickupPointMutation } from '@/entities/order/api/orderApi';
import type { PickupPoint } from '@/entities/order/api/pickupPointsTypes';

interface DeliveryInfoProps {
  orderNumber?: string;
}

export const DeliveryInfo: React.FC<DeliveryInfoProps> = ({ orderNumber }) => {
  const [selectedPoint, setSelectedPoint] = useState<string | null>(null);

  // Only query if we have an order number
  const {
    data: pickupPointsData,
    isLoading: isLoadingPickupPoints,
    error: pickupPointsError,
  } = useGetPickupPointsQuery(
    { order_number: orderNumber || '' },
    { skip: !orderNumber }
  );

  // Mutation for assigning pickup point
  const [assignPickupPoint, { isLoading: isAssigning }] = useAssignPickupPointMutation();

  // Select the first pickup point by default when data is loaded
  useEffect(() => {
    if (
      pickupPointsData?.data &&
      pickupPointsData.data.length > 0 &&
      !selectedPoint
    ) {
      setSelectedPoint(pickupPointsData.data[0].uuid);
    }
  }, [pickupPointsData, selectedPoint]);

  // Get the currently selected pickup point using memoization to prevent unnecessary recalculations
  const selectedPointDetails = useMemo((): PickupPoint | undefined => {
    if (!pickupPointsData?.data || !selectedPoint) return undefined;
    return pickupPointsData.data.find((point) => point.uuid === selectedPoint);
  }, [pickupPointsData, selectedPoint]);

  // Reference to store the map instance
  // @ts-ignore - Using any type for Leaflet objects until types are properly installed
  const mapRef = useRef<any>(null);
  // @ts-ignore - Using any type for Leaflet objects until types are properly installed
  const markerRef = useRef<any>(null);

  // Initialize and update the map when selectedPointDetails changes
  // @ts-ignore - Suppressing TypeScript errors for Leaflet until types are properly installed
  useEffect(() => {
    if (!selectedPointDetails) return;

    const lat = Number(selectedPointDetails.lat);
    const lon = Number(selectedPointDetails.lon);

    // If map doesn't exist, create it
    if (!mapRef.current) {
      const mapElement = document.getElementById('map');
      if (!mapElement) return;

      // Create map
      mapRef.current = L.map('map').setView([lat, lon], 15);

      // Add OpenStreetMap tile layer
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
      }).addTo(mapRef.current);
    } else {
      // If map exists, update the view
      mapRef.current.setView([lat, lon], 15);
    }

    // Remove existing marker if it exists
    if (markerRef.current) {
      markerRef.current.remove();
    }

    // Create custom icon
    const customIcon = L.icon({
      iconUrl: '/assets/icons/map-marker.svg',
      shadowUrl: '/assets/icons/marker-shadow.png',
      iconSize: [50, 50],
      iconAnchor: [12, 41],
      popupAnchor: [12, -34],
      shadowSize: [50, 50],
      shadowAnchor: [1, 41],
    });

    // Add marker with popup
    markerRef.current = L.marker([lat, lon], { icon: customIcon })
      .addTo(mapRef.current)
      .bindPopup(`<strong>${selectedPointDetails.name}</strong><br>${selectedPointDetails.address}`)
      .openPopup();

    // Cleanup function
    return () => {
      if (mapRef.current) {
        mapRef.current.remove();
        mapRef.current = null;
      }
      markerRef.current = null;
    };
  }, [selectedPointDetails]);

  // Log when selected point changes (for debugging)
  useEffect(() => {
    if (selectedPoint) {
      // eslint-disable-next-line no-console
      console.log('Selected point changed:', selectedPoint);
      // eslint-disable-next-line no-console
      console.log('Selected point details:', selectedPointDetails);
    }
  }, [selectedPoint, selectedPointDetails]);

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100">
      <div className="p-4 bg-gray-50 border-b border-gray-100">
        <h3 className="font-bold text-lg">Самовывоз</h3>
      </div>

      <div className="p-4">
        {/* Loading state */}
        {isLoadingPickupPoints && orderNumber && (
          <div className="flex justify-center items-center p-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-colGreen"></div>
            <span className="ml-2">Загрузка пунктов выдачи...</span>
          </div>
        )}

        {/* Error state */}
        {pickupPointsError && (
          <div className="bg-red-50 p-3 rounded border border-red-100 text-red-800 mb-4">
            <p className="flex items-start">
              <Icon
                icon="solar:danger-bold"
                width="20"
                height="20"
                className="mr-2 flex-shrink-0"
              />
              <span>
                Ошибка при загрузке пунктов выдачи. Пожалуйста, попробуйте
                позже.
              </span>
            </p>
          </div>
        )}

        {pickupPointsData?.data && pickupPointsData.data.length > 0 && (
          <div className="mb-4">
            <h4 className="font-medium text-base mb-2">
              Выберите пункт выдачи:
            </h4>
            <div className="grid grid-cols-1 gap-3">
              {pickupPointsData.data.map((point) => (
                <button
                  key={point.uuid}
                  type="button"
                  className={`w-full text-left border rounded-lg p-3 cursor-pointer transition-colors ${
                    selectedPoint === point.uuid
                      ? 'border-colGreen bg-green-50'
                      : 'border-gray-200 hover:border-colGreen'
                  } ${isAssigning && selectedPoint === point.uuid ? 'opacity-75' : ''}`}
                  disabled={isAssigning}
                  onClick={() => {
                    setSelectedPoint(point.uuid);
                    // Only call the API if we have an order number
                    if (orderNumber) {
                      void assignPickupPoint({
                        order_number: orderNumber,
                        delivery_type: 'pickup',
                        pickup_point_uuid: point.uuid,
                      }).catch((error) => {
                        console.error('Failed to assign pickup point:', error);
                      });
                    }
                  }}
                  aria-pressed={selectedPoint === point.uuid}
                >
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      {isAssigning && selectedPoint === point.uuid ? (
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-colGreen"></div>
                      ) : selectedPoint === point.uuid ? (
                        <Icon
                          icon="solar:check-circle-bold"
                          width="20"
                          height="20"
                          className="text-colGreen"
                          aria-hidden="true"
                        />
                      ) : (
                        <Icon
                          icon="solar:circle-outline-linear"
                          width="20"
                          height="20"
                          className="text-gray-400"
                          aria-hidden="true"
                        />
                      )}
                    </div>
                    <div className="ml-3 flex-grow">
                      <h5 className="font-medium">{point.name}</h5>
                      <p className="text-sm text-gray-600">
                        {point.city}, {point.address}
                      </p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Selected pickup point details */}
        {selectedPointDetails && (
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-grow">
              {/* Pickup location */}
              {/* <div className="flex items-start gap-3">
                <Icon
                  icon="solar:map-point-bold"
                  width="24"
                  height="24"
                  className="text-colGreen mt-2"
                />

                <div>
                  <h4 className="font-medium text-base">
                    {selectedPointDetails?.name}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {selectedPointDetails?.address}
                  </p>
                </div>
              </div> */}

              {/* Pickup schedule */}
              <div className="flex items-start gap-3 mt-3">
                <Icon
                  icon="solar:clock-circle-bold"
                  width="24"
                  height="24"
                  className="text-colGreen mt-2"
                />

                <div>
                  <h4 className="font-medium text-base">Режим работы</h4>
                  <p className="text-sm text-gray-600">Пн-Вс: с 8 до 21</p>
                </div>
              </div>

              {/* Contact phone */}
              <div className="flex items-start gap-3 mt-3">
                <Icon
                  icon="solar:phone-bold"
                  width="24"
                  height="24"
                  className="text-colGreen mt-2"
                />

                <div>
                  <h4 className="font-medium text-base">Контактный телефон</h4>
                  <p className="text-sm text-gray-600">+7 (800) 555-35-35</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Map - OpenStreetMap */}
        {selectedPointDetails && (
          <div className="mt-4 h-64 bg-gray-100 rounded-lg overflow-hidden relative">
            <div id="map" className="h-full w-full"></div>
            <div className="absolute bottom-4 right-4 z-[1000]">
              <a
                href={`https://www.openstreetmap.org/?mlat=${selectedPointDetails.lat}&mlon=${selectedPointDetails.lon}#map=16/${selectedPointDetails.lat}/${selectedPointDetails.lon}`}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-colGreen text-white px-4 py-2 rounded-md shadow-md hover:bg-green-700 transition-colors"
              >
                Открыть карту
              </a>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
