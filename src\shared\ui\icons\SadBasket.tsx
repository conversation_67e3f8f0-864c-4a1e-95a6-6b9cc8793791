import React from 'react';

interface SvgIconProps {
  size?: number;
}

export const SadBasket: React.FC<SvgIconProps> = ({ size = 205 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox='0 0 205 205'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <ellipse cx='109.5' cy='186.5' rx='81.5' ry='2.5' fill='#D9D9D9' />
      <path
        d='M187.82 91.6113L187.36 95.2259L186.416 102.34L176.862 176.012C176.555 178.335 175.414 180.468 173.653 182.014C171.891 183.559 169.628 184.413 167.284 184.415H37.9441C35.603 184.412 33.3429 183.558 31.585 182.012C29.8271 180.466 28.6915 178.333 28.3898 176.012L18.8124 102.34L17.8685 95.2259L17.4541 91.6113H187.82Z'
        fill='#EF5261'
      />
      <path
        d='M191.043 67.875H14.2307C11.5606 67.875 9.396 70.0396 9.396 72.7097V90.391C9.396 93.0611 11.5606 95.2257 14.2307 95.2257H191.043C193.713 95.2257 195.878 93.0611 195.878 90.391V72.7097C195.878 70.0396 193.713 67.875 191.043 67.875Z'
        fill='#EF5261'
      />
      <path d='M187.383 95.2261L186.439 102.34H18.8121L17.8682 95.2261H187.383Z' fill='#DA364C' />
      <path
        d='M139.427 118.018H139.404C137.077 118.018 135.191 119.904 135.191 122.231V165.974C135.191 168.301 137.077 170.187 139.404 170.187H139.427C141.754 170.187 143.64 168.301 143.64 165.974V122.231C143.64 119.904 141.754 118.018 139.427 118.018Z'
        fill='#DA364C'
      />
      <path
        d='M102.637 118.018H102.614C100.287 118.018 98.4009 119.904 98.4009 122.231V165.974C98.4009 168.301 100.287 170.187 102.614 170.187H102.637C104.964 170.187 106.85 168.301 106.85 165.974V122.231C106.85 119.904 104.964 118.018 102.637 118.018Z'
        fill='#DA364C'
      />
      <path
        d='M65.8704 118.018H65.8474C63.5206 118.018 61.6343 119.904 61.6343 122.231V165.974C61.6343 168.301 63.5206 170.187 65.8474 170.187H65.8704C68.1972 170.187 70.0835 168.301 70.0835 165.974V122.231C70.0835 119.904 68.1972 118.018 65.8704 118.018Z'
        fill='#DA364C'
      />
      <path
        d='M126.094 22.5862L116.526 28.2534C115.377 28.9338 114.998 30.4165 115.678 31.5652L146.595 83.7599C147.276 84.9085 148.758 85.2881 149.907 84.6077L159.475 78.9406C160.623 78.2602 161.003 76.7774 160.322 75.6287L129.405 23.4341C128.725 22.2854 127.242 21.9058 126.094 22.5862Z'
        fill='#15765B'
      />
      <path
        d='M75.8536 23.4652L44.9366 75.6599C44.2562 76.8086 44.6358 78.2913 45.7845 78.9717L55.3518 84.6389C56.5005 85.3193 57.9833 84.9397 58.6637 83.791L89.5807 31.5964C90.2611 30.4477 89.8815 28.9649 88.7328 28.2845L79.1655 22.6174C78.0168 21.937 76.534 22.3166 75.8536 23.4652Z'
        fill='#15765B'
      />
      <path
        d='M54.428 78.6035C56.3352 78.6035 57.8813 77.0574 57.8813 75.1501C57.8813 73.2429 56.3352 71.6968 54.428 71.6968C52.5207 71.6968 50.9746 73.2429 50.9746 75.1501C50.9746 77.0574 52.5207 78.6035 54.428 78.6035Z'
        fill='#EBEBEB'
      />
      <path
        d='M150.846 78.6035C152.753 78.6035 154.299 77.0574 154.299 75.1501C154.299 73.2429 152.753 71.6968 150.846 71.6968C148.939 71.6968 147.393 73.2429 147.393 75.1501C147.393 77.0574 148.939 78.6035 150.846 78.6035Z'
        fill='#EBEBEB'
      />
      <g clipPath='url(#clip0_1454_20911)'>
        <path
          d='M103.079 170C121.304 170 136.079 155.225 136.079 137C136.079 118.775 121.304 104 103.079 104C84.8532 104 70.0786 118.775 70.0786 137C70.0786 155.225 84.8532 170 103.079 170Z'
          fill='#FCFFFE'
        />
        <path
          d='M86.5002 108.421C70.7819 117.496 65.3464 137.782 74.4214 153.5C83.4964 169.219 103.782 174.654 119.5 165.579C135.219 156.504 140.654 136.219 131.579 120.5C122.504 104.782 102.219 99.3464 86.5002 108.421ZM117.025 161.292C102.593 169.625 84.2535 164.59 75.921 150.158C67.671 135.868 72.4804 117.468 86.9127 109.136C101.345 100.803 119.684 105.838 127.934 120.128C136.267 134.56 131.458 152.96 117.025 161.292Z'
          fill='#EBEBEB'
        />
        <path
          d='M103.014 136.347C99.4999 136.347 97.1066 138.096 94.4122 141.026C93.9152 141.565 93.9504 142.406 94.49 142.902C95.0296 143.399 95.871 143.364 96.367 142.824C98.5675 140.431 101.028 139.003 103.014 139.003C105 139.003 107.46 140.431 109.661 142.824C110.157 143.364 110.998 143.399 111.538 142.902C112.077 142.406 112.113 141.565 111.616 141.026C108.921 138.096 106.217 136.347 103.014 136.347ZM91.8582 135.051C90.3913 135.051 89.2021 133.862 89.2021 132.395V130.27C89.2021 128.803 90.3913 127.614 91.8582 127.614C93.3252 127.614 94.5143 128.803 94.5143 130.27V132.395C94.5143 133.862 93.3252 135.051 91.8582 135.051ZM114.17 135.051C112.703 135.051 111.513 133.862 111.513 132.395V130.27C111.513 128.803 112.703 127.614 114.17 127.614C115.637 127.614 116.826 128.803 116.826 130.27V132.395C116.826 133.862 115.637 135.051 114.17 135.051Z'
          fill='#15765B'
        />
      </g>
      <defs>
        <clipPath id='clip0_1454_20911'>
          <rect width='66' height='66' fill='white' transform='translate(70 104)' />
        </clipPath>
      </defs>
    </svg>
  );
};
