import { TreeNode } from '@/types/CategorieTree';

export const transformCategory = (category: any): TreeNode => {
  const node: TreeNode = {
    id: category.id.toString(),
    label: category.name,
    link: category.slug,
    countProducts: category.product_count
  };

  if (category.children && category.children.length > 0) {
    node.children = category.children.map(transformCategory);
  }

  return node;
};

export const TransformCategoryToTree = (categories: any[]): TreeNode[] => {
  return categories.map(transformCategory);
};
