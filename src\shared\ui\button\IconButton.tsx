import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/shared/lib/utils';
import { Button, type ButtonProps } from './Button'; // Import the base Button

export interface IconButtonProps extends Omit<ButtonProps, 'size' | 'children' | 'fullWidth'> {
  icon: React.ReactElement;
  'aria-label': string; // Enforce accessibility
}

const IconButton = React.forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ className, variant, icon, 'aria-label': ariaLabel, asChild = false, ...props }, ref) => {
    return (
      <Button
        ref={ref}
        variant={variant}
        size="icon" // Always use the icon size
        className={cn('p-0', className)} // Remove padding if necessary, ensure centering
        asChild={asChild}
        aria-label={ariaLabel}
        {...props}
      >
        {React.cloneElement(icon, { className: 'h-5 w-5' })} {/* Ensure icon size */} 
      </Button>
    );
  }
);
IconButton.displayName = 'IconButton';

export { IconButton };
