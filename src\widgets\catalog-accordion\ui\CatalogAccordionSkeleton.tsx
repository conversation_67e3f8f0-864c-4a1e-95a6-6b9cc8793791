import { memo } from 'react';

interface CatalogAccordionSkeletonProps {
  className?: string;
}

export const CatalogAccordionSkeleton = memo(({ className = '' }: CatalogAccordionSkeletonProps) => {
  return (
    <div className={`animate-pulse ${className}`}>
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="h-5 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 w-4 bg-gray-200 rounded-full"></div>
            </div>
            <div className="pl-5 space-y-2">
              {Array.from({ length: 2 }).map((_, subIndex) => (
                <div key={subIndex} className="h-4 bg-gray-200 rounded w-2/3"></div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
});

CatalogAccordionSkeleton.displayName = 'CatalogAccordionSkeleton';
