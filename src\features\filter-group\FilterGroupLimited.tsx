interface FilterGroupLimited {
  groupTitle: string;
  // fields: string[];
  onChange: () => void;
  className?: string;
}

type Range = {
  min: number;
  max: number;
  value: number;
};

type Fields = {
  fields: unknown[];
};

type Props = FilterGroupLimited & (Range | Fields);

export const FilterGroupLimited: React.FC<Props> = ({ className }) => {
  return (
    <div className={`filtergrouplimited  ${className}`}>
      <span>Название</span>
    </div>
  );
};
