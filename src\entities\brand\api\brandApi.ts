import { api } from '@/shared/api/api';
import type { AdditionalServerResponseData } from '@/shared/types/AdditionalServerResponseData';
import type { ImageSet } from '@/shared/types/ImageSet';

// Updated Brand type to include code field
interface Brand {
  id: number;
  name: string;
  code: string;
  image: ImageSet;
  description?: string;
}

interface GetBrandResponse extends AdditionalServerResponseData {
  data: Brand;
}

export const brandApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getBrand: builder.query<Brand, string>({
      // Now querying by brand code instead of ID
      query: (code) => `/api/Brands/${code}`,
      transformResponse: (response: GetBrandResponse) => response.data,
      providesTags: (result, error, code) => [{ type: 'Brand', id: code }],
      keepUnusedDataFor: 60,
    }),
  }),
});

export const { useGetBrandQuery } = brandApi;
