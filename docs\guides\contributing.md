# Contribution Guidelines

Thank you for contributing to the Rostok Frontend project! To ensure consistency and maintainability, please adhere to the following guidelines.

## 1. Getting Started

*   Ensure you have the necessary [prerequisites](../../README.md#prerequisites) installed (Node.js v16+, npm v7+).
*   Clone the repository: `git clone <repository-url>`
*   Navigate into the project directory: `cd <repository-directory>`
*   Install dependencies: `npm install`
*   Run the development server: `npm run dev`

## 2. Branching Strategy

*   Create a new feature or bugfix branch from the main development branch (e.g., `main` or `develop`).
*   Use a descriptive branch name prefixed with `feature/`, `fix/`, `refactor/`, or `chore/`.
    *   Examples: `feature/user-profile-edit`, `fix/cart-calculation-bug`, `refactor/move-legacy-components-to-fsd`, `chore/update-dependencies`.

## 3. Code Style & Conventions

*   **Language:** Adhere to modern TypeScript and React (TSX) best practices.
*   **Architecture:** Follow **Feature-Sliced Design (FSD)** principles. Consult the [FSD Overview](../architecture/fsd-overview.md) and place new code in the correct layers/slices/segments. Aim to refactor legacy code you touch towards FSD.
*   **Linting & Formatting:**
    *   ESLint (`.eslintrc.cjs`) and Prettier (`package.json` or `.prettierrc.mjs`) enforce code style.
    *   **Run `npm run validate`** before committing to check for linting and type errors.
    *   Run `npm run format` to automatically format your code.
    *   Use IDE extensions for ESLint and Prettier for real-time feedback.
*   **Naming:**
    *   Components: `PascalCase` (e.g., `ProductCard.tsx`)
    *   Hooks: `useCamelCase` (e.g., `useAuth.ts`)
    *   Variables/Functions: `camelCase`
    *   Types/Interfaces: `PascalCase` (e.g., `Product.ts`)
    *   CSS Utility Classes: Use Tailwind CSS conventions.
*   **Components:**
    *   Use functional components with Hooks.
    *   Use named exports (`export const MyComponent = ...`). Avoid default exports for components, except potentially for Page components (`src/pages/`).
    *   Keep components focused on a single responsibility.
    *   Use `React.memo` for performance optimization where appropriate, especially for components that receive complex props or are rendered frequently within lists.
*   **State Management:**
    *   Use Redux Toolkit slices (`createSlice`) for client state.
    *   Use RTK Query (`createApi`, `injectEndpoints`) for server state and API interaction.
    *   Co-locate state (`model/`) and API (`api/`) logic within the relevant feature or entity slice.
    *   Utilize provided hooks (`useSelector`, `useAppDispatch`, RTK Query hooks).
*   **Styling:**
    *   Prioritize Tailwind CSS utility classes.
    *   Use the `cn` utility (`src/shared/lib/utils.ts`) for merging classes.
    *   Use Shadcn UI components from `src/shared/ui/` as the base for shared UI elements.
    *   Use MUI components sparingly, primarily for complex elements or legacy areas. See [Styling Guide](../architecture/styling.md).
*   **Comments:** Add JSDoc comments for non-obvious logic, complex functions, public APIs of features/widgets/entities, and exported types.

## 4. Committing

*   Write clear, concise commit messages. Using [Conventional Commits](https://www.conventionalcommits.org/) is encouraged (e.g., `feat: add user login modal`, `fix: correct total price in cart`).
*   Commit small, logical units of work frequently.

## 5. Pull Requests (PRs)

1.  Push your feature/bugfix branch to the remote repository.
2.  Create a Pull Request targeting the main development branch.
3.  **Title:** Use a clear, descriptive title (often mirrors the primary commit message).
4.  **Description:**
    *   Clearly explain the purpose and context of the change ("Why?").
    *   Summarize the implementation ("What?").
    *   Link to any relevant issue(s) (e.g., "Closes #123").
    *   Provide testing instructions if necessary.
    *   Include screenshots or GIFs for UI changes.
5.  **Assign Reviewers:** Request code reviews from relevant team members.
6.  **Address Feedback:** Respond to comments and push necessary changes to the *same* branch.
7.  **CI Checks:** Ensure all automated checks (linting, type-checking, tests) pass.
8.  **Merge:** Once approved and checks pass, a maintainer will merge the PR. Avoid merging your own significant PRs without approval.

## 6. Refactoring

When working on existing code, especially in legacy directories (`src/components`, `src/helpers`, etc.), take the opportunity to refactor towards the FSD structure if feasible within the scope of your task. Consult the [Refactoring Plan](../refactoring/plan.md).