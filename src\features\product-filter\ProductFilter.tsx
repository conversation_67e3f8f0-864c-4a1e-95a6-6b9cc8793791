import { useProductFilter } from './hook/useProductFilter';
import { ProductFilterContext } from './model/ProductFilterContext';
import { ProductFilterOptions } from './ProductFilterOptions';
import { ProductFilterOptionsAlone } from './ProductFilterOptionsAlone';
import { ProductFilterOptionsLimited } from './ProductFilterOptionsLimited';
import { ProductFilterTrigger } from './ProductFilterTrigger';
import { ProductFilterProps } from './type';

export const ProductFilter: React.FC<ProductFilterProps> = (props) => {
  const value = useProductFilter(props);

  const option = () => {
    switch (value.mode) {
      case 'limited':
        return <ProductFilterOptionsLimited />;
      case 'full':
        return <ProductFilterOptions />;
      case 'alone':
        return <ProductFilterOptionsAlone />;
    }
  };

  const trigger = () => {
    switch (value.mode) {
      case 'alone':
        return <span>{value.data.name}</span>;
      default:
        return <ProductFilterTrigger />;
    }
  };

  return (
    <ProductFilterContext.Provider value={value}>
      {trigger()}
      {option()}
    </ProductFilterContext.Provider>
  );
};
