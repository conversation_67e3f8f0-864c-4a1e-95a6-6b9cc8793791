# Entity: Filter

## Purpose

This entity represents the concept of **Filters** used for refining product lists in the catalog, search results, and potentially other areas. It defines the structure of available filters (price, brands, tags, dynamic attributes) and provides the API for fetching these filter options based on context (category, search query, current selections).

## Key Data Points / Structure (`model/types.ts` - `FiltersState`)

*   **`basics`**: Core, common filters.
    *   `price`: Min/max range and current selected range (`PriceFilter` type).
    *   `tags`: List of available product tags (`TagFilter` type), including selection status.
    *   `brands`: List of available brands (`BrandFilter` type), including selection status.
    *   `rating`: (Currently defined but seems unused) Potential rating filter range.
*   **`dynamics`**: Category-specific or context-specific attribute filters (`DynamicFilter` type). Each has an `id`, `name`, `type` ('color' or 'text'), and a list of possible `values` (`DynamicFilterValue`), including selection and availability status.
*   **`more`**: Additional dynamic filters, often initially hidden and shown in an "All Filters" modal. Structurally identical to `dynamics`.
*   **`category_chain` (in API Response):** Although returned by the filter API, this represents the category hierarchy and belongs conceptually to the `Category` entity.

## Structure

*   **`model/types.ts`:** Defines the structure of the filter state (`FiltersState`) and its constituent parts (`PriceFilter`, `TagFilter`, `BrandFilter`, `DynamicFilter`, `DynamicFilterValue`, etc.). *(Currently in `src/entities/filter/types.ts`)*
*   **`api/filterApi.ts`:** Defines RTK Query endpoints:
    *   `getFilters` (Mutation): Fetches the available filters based on provided context (category ID, search term, currently selected filters, etc.). Returns the full `FiltersState` structure along with category distribution. *Note: Defined as a mutation due to using POST for complex parameters, but functions like a query.*
    *   `getBasicFilters`: Query to fetch only the basic, globally available filters (like all brands, all tags), often used for initial setup or simpler contexts.
*   **`api/types.ts`:** Defines request (`GetFiltersRequest`) and response (`GetFiltersResponse`) types for the filter API endpoints.
*   **`ui/`:** Contains generic UI components for rendering filter groups and items:
    *   `FilterGroup/FilterGroup.tsx`: An accordion component to group related filter options under a title.
    *   `FilterItem/FilterCheckbox.tsx`: A standardized checkbox component for filter values.
    *   `PriceRange/PriceRange.tsx`: A component combining a slider and input fields for selecting a price range.
*   **`index.ts`:** Public API for the entity.

## Usage

*   **Fetching Filters:** Catalog, Search, Brand, and Tag pages use `useGetFiltersMutation` or `useGetBasicFiltersQuery` to fetch the relevant filter options for the current context.
*   **Displaying Filters:** Widgets like `CatalogSidebar` (`src/widgets/catalog/ui/CatalogSidebar/CatalogSidebar.tsx` - *currently in `components`*) and modals like `AllFiltersModal` (`src/features/modals/ui/modals/AllFiltersModal/`) consume the `FiltersState` and use the UI components (`FilterGroup`, `FilterCheckbox`, `PriceRange`) to render the filter controls.
*   **Applying Filters:** User selections in the filter UI update the local filter state, which is then used as input for the `getVariants` mutation (`entities/product/api`) to fetch filtered product results.

## Related Entities/Features

*   `entities/product`: Filters are applied to product lists.
*   `entities/category`, `entities/brand`, `entities/tag`: Data from these entities populates the filter options.
*   `features/catalog`, `features/search`: These features heavily rely on the filter entity for their functionality.
*   `widgets/catalog`: Widgets responsible for displaying filters and product lists.

## Migration Notes

*   The UI components for *specific* filter types (like `BrandsFilter`, `TagsFilter`, `CheckboxFilter` for dynamic attributes) currently reside within the *legacy* `src/components/Catalog/CatalogSidebar/SidebarFilters/` directory. These should ideally be:
    *   Either generalized and moved into `src/entities/filter/ui/` if they are simple representations based on filter type.
    *   Or moved into a potential `features/product-filtering` slice if they contain significant interaction logic beyond just displaying the filter data.
*   The state management for *applying* filters currently seems embedded within the page/catalog components (`src/components/Catalog/Catalog.tsx`, etc.). This logic should be extracted into a dedicated feature, possibly `features/product-filtering`, which would consume the `FiltersState` from this entity and manage the parameters sent to the `getVariants` product API.
