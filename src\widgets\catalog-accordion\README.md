# Widget: Catalog Accordion

## Purpose

This widget displays the product category hierarchy in a nested, collapsible accordion format. It's typically used in sidebars or dedicated catalog navigation areas.

## Composition

*   **Entities:** Relies heavily on the `category` entity (`entities/category`) to fetch the category tree structure using `useGetCategoryTreeQuery`.
*   **Shared UI:** Uses basic elements like `NavLink` for navigation, `ArrowIcon` (`shared/ui/icons`), and layout utilities. Potentially uses shared `Skeleton` components (`shared/ui`).

## Key UI Components (`ui/`)

*   **`CatalogAccordion.tsx`:** The main component that fetches category data, manages the accordion's open/closed state for different levels, and recursively renders the category tree using nested lists and buttons.
*   **`CatalogAccordionSkeleton.tsx`:** A skeleton loader displayed while the category data is being fetched.

## Logic & State

*   **Data Fetching:** Uses `useGetCategoryTreeQuery` hook from `entities/category/api/categoryApi.ts`.
*   **State Management:** Manages the open/closed state of different accordion levels internally using <PERSON><PERSON>'s `useState` hook (`accordion` state object with `parent`, `child`, `childLast` keys).
*   **URL Preservation:** Includes logic (`getCategoryUrl`) to construct `NavLink` `to` props that preserve existing URL query parameters when navigating between categories within the same page context (e.g., preserving filters when clicking a subcategory).
*   **Scroll Prevention:** Uses `preventScrollReset={true}` on `NavLink` components to avoid scrolling to the top of the page when navigating between categories using the accordion.

## Usage

*   The `CatalogAccordion` is primarily intended for use within sidebar widgets, such as `CatalogSidebar` (*currently located in legacy `components`*) on catalog, search, brand, or tag pages.
*   It takes an optional `categoryId` prop to fetch the tree relative to a specific category, or fetches the root tree if `categoryId` is null or undefined.

## Related Entities/Widgets

*   `entities/category`: Provides the data structure and API hook.
*   `widgets/catalog/CatalogSidebar`: A likely consumer of this accordion widget.

## Migration Notes

*   This widget appears reasonably well-contained. Ensure it's consistently used within a designated sidebar widget (`widgets/catalog/ui/CatalogSidebar/CatalogSidebar.tsx` once refactored) rather than directly on pages.
*   The component rendering logic could potentially be simplified by using a recursive sub-component for rendering each level of the tree.
