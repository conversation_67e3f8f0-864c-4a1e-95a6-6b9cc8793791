// src/pages/ComparisonPage.tsx
// NOTE: Temporary location. Ideally, this would be in src/pages/comparison-page/ui/ComparisonPage.tsx
// External Libraries
import React, { useEffect } from 'react'; // Added React import for React.FC
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useSelector } from 'react-redux';

import ComDetail from '@/components/Comparison/ComparisonDetail'; // Path to be updated later
import { useComparison } from '@/features/comparison';
import ErrorEmpty from '@/shared/ui/ErrorEmpty'; // Updated path from helpers/Errors/ErrorEmpty.jsx
import { scrollToTop } from '@/shared/lib/scrollToTop';
import { Breadcrumbs } from '@/widgets/breadcrumbs';
import {
  CategorySwitcher,
  useCategorySwitcher,
} from '@/widgets/category-switcher';

import type { RootState } from '@/app/providers/store';

const ComparisonPage: React.FC = () => { // Renamed from Comparison and typed as React.FC
  const {
    comparison: allComparisonItems,
    isLoading,
    isError: comparisonIsError,
    refreshFromServer,
    totalCount,
  } = useComparison();

  const { categories } = useSelector((state: RootState) => state.comparison);

  const {
    selectedCategory,
    filteredProducts: filteredComparison,
    handleCategoryChange,
    isUpdating
  } = useCategorySwitcher(categories, allComparisonItems);

  const isPageLoading = isLoading || isUpdating;

  useEffect(() => {
    void refreshFromServer();
  }, [refreshFromServer]);

  useEffect(() => {
    scrollToTop();
  }, []);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="pb-6 content flex flex-col gap-5">
        <Breadcrumbs />
        <h1 className="block pb-5 text-2xl font-semibold md:text-[40px] text-colBlack">
          Сравнение товаров
        </h1>
        {isPageLoading && categories?.length === 0 ? <p>Загрузка категорий...</p> : null} // Show loading only if categories are also not yet loaded
        {!isPageLoading && categories?.length > 0 ? (
          <CategorySwitcher
            categories={categories}
            selectedCategory={selectedCategory}
            onCategoryChange={handleCategoryChange}
            allItemsCount={totalCount}
          />
        ) : null}

        {isPageLoading ? (
          <div className="w-full h-96 bg-gray-200 animate-pulse rounded-md mt-5">
            <p className="text-center p-10 text-gray-500">Загрузка товаров...</p>
          </div>
        ) : null}
        {!isPageLoading &&
        !comparisonIsError &&
        filteredComparison &&
        filteredComparison.length > 0 ? (
          <ComDetail comparison={filteredComparison} />
        ) : null}
        {!isPageLoading && // Changed from !isLoading to !isPageLoading for consistency
        !comparisonIsError &&
        (!filteredComparison || filteredComparison.length === 0) ? (
          <ErrorEmpty
            title="Товаров для сравнения нет" // Updated title
            desc="Добавляйте понравившийся товар в сравнение, чтобы сравнить его с аналогами."
            height="420px"
          />
        ) : null}
        {comparisonIsError ? <p>Ошибка загрузки товаров для сравнения.</p> : null}
      </div>
    </DndProvider>
  );
};

export default ComparisonPage; // Renamed export
