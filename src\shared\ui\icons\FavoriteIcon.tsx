export const FavoriteIcon = (props) => {
  const color = props.favorite ? '#F04438' : '#727272';
  const fill = props.favorite ? 'nonzero' : 'evenodd';

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      fill="#333"
      className="cursor-pointer"
      {...props}
    >
      <rect width={24} height={24} fill="#F5F5F5" rx={12} />
      <path
        fill={color}
        fillRule={fill}
        d="M9 7.5c-1.375 0-2.5 1.136-2.5 2.602 0 1.897 1.361 3.744 2.847 5.176a19.433 19.433 0 0 0 2.655 2.128 18.995 18.995 0 0 0 2.652-2.11c1.483-1.424 2.846-3.274 2.846-5.202C17.5 8.637 16.376 7.5 15 7.5c-.635 0-1.264.402-1.78.893a6.103 6.103 0 0 0-.79.926l-.009.013-.001.002a.5.5 0 0 1-.84 0l-.001-.002-.01-.013a5.437 5.437 0 0 0-.198-.27 6.103 6.103 0 0 0-.59-.656C10.263 7.903 9.634 7.5 9 7.5Zm3 .735a6.978 6.978 0 0 0-.53-.566C10.902 7.13 10.03 6.5 9 6.5c-1.958 0-3.5 1.614-3.5 3.602 0 2.335 1.639 4.436 3.153 5.896a20.421 20.421 0 0 0 3.066 2.416l.014.009.006.003L12 18l-.261.426c.159.097.36.099.52.002L12 18l.258.428h.002l.004-.003.015-.01a8.494 8.494 0 0 0 .252-.16 19.958 19.958 0 0 0 2.815-2.238c1.517-1.455 3.154-3.558 3.154-5.923 0-1.98-1.543-3.594-3.5-3.594-1.031 0-1.902.629-2.47 1.17a6.978 6.978 0 0 0-.53.565Z"
      />
    </svg>
  );
};
