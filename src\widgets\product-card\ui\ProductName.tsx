
import type { Product } from '@/entities/product';

interface ProductNameProps {
  product: Product;
  className?: string;
  maxLines?: number;
  withLink?: boolean;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  color?: string;
  hover?: boolean;
}

export const ProductName = ({
  product,
  className = '',
  maxLines = 2,
  withLink = true,
  size = 'md',
  weight = 'semibold',
  color = 'text-colBlack',
  hover = true
}: ProductNameProps): JSX.Element => {
  if (!product) {
    return <span className='text-sm text-colDarkGray'>Название товара не указано</span>;
  }

  // Helper function to get text size class based on size prop
  const getTextSizeClass = (): string => {
    switch (size) {
      case 'xs':
        return 'text-xs';
      case 'sm':
        return 'text-sm';
      case 'md':
        return 'text-base';
      case 'lg':
        return 'text-lg';
      case 'xl':
        return 'text-xl';
      default:
        return 'text-base';
    }
  };

  // Helper function to get font weight class based on weight prop
  const getFontWeightClass = (): string => {
    switch (weight) {
      case 'normal':
        return 'font-normal';
      case 'medium':
        return 'font-medium';
      case 'semibold':
        return 'font-semibold';
      case 'bold':
        return 'font-bold';
      default:
        return 'font-semibold';
    }
  };

  // Construct the product name
  const productName = `${product?.groupName || ''} ${product?.name || ''}`.trim();

  // Construct the class name
  const nameClassName = `
    ${getFontWeightClass()} 
    ${getTextSizeClass()} 
    ${color} 
    cursor-pointer 
    leading-tight 
    line-clamp-${maxLines} 
    break-words 
    ${hover ? 'hover:underline' : ''} 
    ${className}
  `
    .replace(/\s+/g, ' ')
    .trim();

  // If withLink is true, wrap the name in a NavLink
  if (withLink && product?.category?.slug && product?.slug) {
    return (
      // <NavLink to={`/catalog/${product.category.slug}/${product.slug}`}>
      <span className={nameClassName}>{productName}</span>
      // </NavLink>
    );
  }

  // Otherwise, just return the name
  return <span className={nameClassName}>{productName}</span>;
};
