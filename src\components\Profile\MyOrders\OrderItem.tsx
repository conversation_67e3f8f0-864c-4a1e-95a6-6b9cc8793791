import { useState } from 'react';
import noImg from '@/shared/assets/images/no-image.png';
import { ExpandMore } from '@mui/icons-material';
import { OrderDetails } from './OrderDetails';
import { OrderHeader } from './OrderHeader';
import type { Order } from '@/entities/order'; // Ensure this path is correct

interface OrderItemProps {
  order: Order;
}

export const OrderItem = ({ order }: OrderItemProps) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="rounded-[10px] overflow-hidden border border-gray-200 shadow-sm">
      <div className="lg:flex justify-between lg:space-x-3 bg-gray-50 p-3 sm:p-4 lg:p-5">
        <OrderHeader order={order} />
        {/* Removed empty div, OrderHeader should handle layout */}
      </div>
      <div className="p-3 sm:p-4 lg:p-5">
        <div className="flex flex-wrap items-center justify-between gap-3">
          <div className="flex -space-x-2 overflow-hidden">
            {order.items.slice(0, 5).map((item) => (
              <div
                key={`${item.sku}-${item.id}`} // Use a more unique key if possible
                className="inline-block h-12 w-12 rounded-md ring-2 ring-white bg-gray-100 p-1"
              >
                <img
                  className="h-full w-full object-contain"
                  src={item.files?.[0]?.small || noImg}
                  alt={item.fullName}
                  title={item.fullName}
                />
              </div>
            ))}
            {order.items.length > 5 && (
              <div className="inline-flex items-center justify-center h-12 w-12 rounded-md ring-2 ring-white bg-gray-200 text-gray-600 text-sm font-medium">
                +{order.items.length - 5}
              </div>
            )}
          </div>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center text-sm font-semibold text-colGreen hover:text-green-700 transition-colors duration-150 outline-none mt-2 lg:mt-0"
            aria-expanded={isExpanded}
            aria-controls={`order-details-${order.order_number}`}
          >
            <span>
              {isExpanded ? 'Скрыть детали заказа' : 'Посмотреть детали заказа'}
            </span>
            <ExpandMore
              className={`transform transition-transform duration-200 ml-1 ${
                isExpanded ? 'rotate-180' : ''
              }`}
            />
          </button>
        </div>
      </div>
      {isExpanded && (
        <div id={`order-details-${order.order_number}`} className="border-t border-gray-200">
          <OrderDetails order={order} />
        </div>
      )}
    </div>
  );
};
