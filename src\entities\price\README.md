# Entity: Price

## Purpose

This entity represents **Pricing** information for products. It defines the structure of price data, including base price, discounts, final price, currency, and units, and provides API endpoints related to price calculation or retrieval.

## Key Data Points (`model/types.ts` - `PriceType`)

*   `base`: The original price before discounts (can be `null`).
*   `final`: The price the customer pays after discounts.
*   `discount`: An object containing discount details (`price`, `percent`, `discount_amount`, `reason`) if applicable, otherwise `null`.
*   `unit`: The unit of measurement for the price (e.g., "шт", "м²").
*   `currency`: An object containing currency details (`code`, `title`, `symbol`).
*   `total` (Optional, often calculated): The total price for a given quantity (`final * quantity`).

## Structure

*   **`model/types.ts`:** Defines the core `PriceType`, `Currency`, and `Discount` interfaces. *(Currently in `src/entities/price/types.ts`)*
*   **`api/priceApi.ts`:** Defines RTK Query endpoints:
    *   `getCartItemPrice` (Mutation): Fetches the *current* price for a specific item ID and quantity. This is crucial because prices might change or depend on quantity tiers. It returns the updated price for the single item and potentially updated cart totals. Invalidates `Cart` and `User` data tags.
*   **`api/types.ts`:** Defines request (`GetCartItemPriceRequest`) and response (`GetCartItemPriceResponse`) types for the price API.
*   **`index.ts`:** Public API for the entity.
*   **`ui/`:** (Currently empty) Could potentially hold a dedicated `PriceDisplay` component if the one in `widgets/product-card` is deemed too specific to that context, but the current location seems reasonable.

## Usage

*   **Displaying Prices:** The `PriceDisplay` widget (`src/widgets/product-card/ui/PriceDisplay.tsx`) is used throughout the application (product cards, product page, cart) to consistently render price information based on the `PriceType` structure.
*   **Calculating Cart Totals:** The `cartSlice` (`features/cart`) uses price information from cart items to calculate selected and overall totals.
*   **Fetching Updated Prices:** The `useQuantityControl` hook (`features/cart`) calls `useGetCartItemPriceMutation` when the quantity of a cart item changes to get the potentially updated price and then updates the cart state via the `changeQuantity` reducer.
*   **Product Data:** The `Product` entity (`entities/product`) includes a `price` field of type `PriceType`.

## Related Entities/Features

*   `entities/product`: Products have associated price information.
*   `features/cart`: Heavily relies on price data for calculations and uses the price API for updates.

## Migration Notes

*   Ensure all price rendering consistently uses the `PriceDisplay` widget or directly formats data according to the `PriceType` structure.
*   Verify that all scenarios where price might change based on quantity correctly utilize the `getCartItemPrice` mutation.
