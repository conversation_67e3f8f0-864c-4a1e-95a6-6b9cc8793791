import { ProductFilter } from '@/types/Filters/ProductFilter';
import { useState } from 'react';
import { useProductFilterContext } from './useProductFIlterContext';

export const useProductFilterOptionAlone = () => {
  const { data, onChange } = useProductFilterContext();

  const [filtered, setFiltered] = useState<ProductFilter>(data);

  const handleFilter = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (data.input_type !== 'multiple') return;

    const value = event.target.value.toLowerCase();
    if (!value) return setFiltered(data);

    if (data.type === 'color') {
      const result = data.values.filter(({ text }) => text.toLowerCase().includes(value));
      const updateData = { ...data, values: result };
      setFiltered(updateData);
    }
    if (data.type === 'text') {
      const result = data.values.filter(({ text }) => text.toLowerCase().includes(value));
      const updateData = { ...data, values: result };
      setFiltered(updateData);
    }
  };

  return { data, onChange, filtered, handleFilter };
};
