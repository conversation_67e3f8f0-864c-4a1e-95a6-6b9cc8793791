import { Forbidden } from '../icons';
import { Tooltip, TooltipContent, TooltipTrigger } from '../tooltip/tooltip';

interface SquareCheckboxProps {
  disabled?: boolean;
  onChange: (bool: boolean) => void;
  checked: boolean;
  colors: {
    color: string;
    second_color?: string;
  };
  id: string;
  text: string;
}

export const SquareCheckbox: React.FC<SquareCheckboxProps> = ({
  onChange,
  colors = {},
  disabled = false,
  id,
  text,
  checked
}) => {
  return (
    <Tooltip>
      <TooltipTrigger>
        <label
          className='flex justify-center items-center w-[50px] h-[50px] rounded-[10px] border-[white] border-[1px] border cursor-pointer has-[:checked]:border-colGreen has-[:disabled]:opacity-[.7]'
          htmlFor={id}
        >
          <input
            type='checkbox'
            id={id}
            className='absolute opacity-[0] hidden'
            onChange={(e) => onChange(e.target.checked)}
            disabled={disabled}
            checked={checked}
          />
          {!colors?.color && !colors?.second_color ? (
            <div>
              <Forbidden size={42} color='#B5B5B5' />
            </div>
          ) : (
            <div className='w-[40px] h-[40px] overflow-hidden rounded-full flex'>
              <span
                className='w-full h-full'
                style={{
                  backgroundColor: `${colors?.color}`
                }}
              />
              {colors?.second_color && (
                <span
                  className='w-full h-full'
                  style={{
                    backgroundColor: `${colors?.second_color}`
                  }}
                />
              )}
            </div>
          )}
        </label>
      </TooltipTrigger>
      <TooltipContent className='bg-[#1f1f1f] py-[12px] px-[16px] rounded-[12px] text-[white]'>
        {text}
      </TooltipContent>
    </Tooltip>
  );
};
