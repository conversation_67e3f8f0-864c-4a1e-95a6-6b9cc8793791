import { useGetCategoryTreeQuery } from '@/entities/category';
import { ProductFilterURL } from '@/types/Filters/ProductFilterURL';
import { CatalogMain } from './CatalogMainContent';
import { CatalogSidebar } from './CatalogMainSidebar';

export type ProductFilterURLInput = { alone: ProductFilterURL } | { multiple: ProductFilterURL[] };

export const CatalogRoot = () => {
  const { data } = useGetCategoryTreeQuery(null);

  return (
    <div className={'content py-8'}>
      <div className='flex'>
        <CatalogSidebar categoryTree={data?.children} />
        <CatalogMain categoryTree={data?.children} />
      </div>
    </div>
  );
};
