# Entity: Brand

## Purpose

Represents the **Brand** or manufacturer of products within the e-commerce platform.

## Key Data Points

*   `id`: Unique identifier.
*   `name`: Brand name.
*   `code`: A string identifier for the brand (used in API lookups).
*   `image`: Brand logo (`ImageSet`).
*   `description`: Optional brand description.

## Structure

*   **`model/types.ts`:** (Implicit) Defines the `Brand` interface based on API responses. Should be explicitly defined here.
*   **`api/brandApi.ts`:** Defines RTK Query endpoints:
    *   `getBrand`: Query to fetch detailed information for a *single* brand using its `code`. Uses `providesTags`.
*   **`ui/BrandHeader/BrandHeader.tsx`:** A simple UI component intended to display brand information (name, logo, description) at the top of brand-specific pages. *(Currently contains placeholder logic and needs data integration)*.
*   **`index.ts`:** Exports API hooks and UI components.

## Usage

*   **Brand Pages:** The `pages/BrandsPage` (*currently `src/components/Catalog/Brands.tsx`*) uses the `useGetBrandQuery` hook (when a specific brand code is provided in the URL) to fetch and display brand details using the `BrandHeader` component.
*   **Filtering:** Brand information (ID, name, image) is often included in filter options fetched by `entities/filter/api/filterApi.ts` (`getFilters` or `getBasicFilters`) and displayed in filter widgets/modals.
*   **Product Association:** The `Product` entity (`entities/product`) includes an optional `brand` field referencing this entity.

## Related Entities/Features

*   `entities/product`: Products can belong to a brand.
*   `entities/filter`: Brand data is used within filtering mechanisms.
*   `pages/BrandsPage`: Displays brand-specific product listings and brand information.

## Migration Notes

*   Explicitly define the `Brand` interface in `model/types.ts`.
*   Refactor the `BrandHeader` component to correctly use the data fetched from `useGetBrandQuery` instead of placeholder logic.
*   Ensure consistency in how brands are referenced (ID vs. code). The API currently uses `code` for fetching a single brand, while filtering likely uses `id`.
