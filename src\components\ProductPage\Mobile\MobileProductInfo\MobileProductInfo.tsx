// src/components/ProductPage/Mobile/MobileProductInfo/MobileProductInfo.tsx
import React from 'react';
import MobileCharacteristics from './MobileCharacteristics'; // Will resolve to .tsx
import MobileFiles from './MobileFiles'; // Assuming .jsx for now
import MobileReviews from './MobileReviews'; // Assuming .jsx for now
import MobileInfo from './MobileInfo'; // Assuming .jsx for now

// Reusing interfaces similar to MobileCharacteristics.tsx
// It would be ideal to have these in a shared types file if used across many components.
interface AttributeValue {
  value: string | number;
  text: string;
  current?: boolean;
  available?: boolean;
  color?: string;
  second_color?: string;
}

interface Attribute {
  id: number | string;
  name: string;
  values: AttributeValue[];
}

interface ProductModificationAttribute {
  id: number | string;
  text: string;
}

interface ProductModification {
  sku: string | number;
  attributes?: ProductModificationAttribute[];
  // other modification-specific properties
}

interface ReviewSummary {
  // Define structure based on actual data for product.reviews
  // Example:
  // rating: number;
  // total_count: number;
  // total_count_text: string;
  [key: string]: any; // Placeholder
}

interface ProductFile {
    // Define structure based on actual data for product.files
    [key: string]: any; // Placeholder
}

interface ProductData {
  description: string;
  attributes?: Attribute[];
  files?: ProductFile[];
  reviews: ReviewSummary; // Assuming reviews is a mandatory part of product data for MobileReviews
  // other base product properties
}

interface MobileProductInfoProps {
  current?: ProductModification | null;
  product: ProductData;
}

const MobileProductInfo: React.FC<MobileProductInfoProps> = ({ current, product }) => {
  return (
    <>
      {/* Render characteristics only if current modification has attributes or if product itself has attributes to display */}
      {(current?.attributes || product?.attributes) && (
        <div className='mt-10'>
          <MobileCharacteristics current={current} product={product} />
        </div>
      )}

      {product?.files && product.files.length > 0 && (
        <div className='mt-20'>
          <MobileFiles product={product} />
        </div>
      )}

      {/* Assuming product.reviews will always be present if MobileReviews is to be rendered */}
      <div className='mt-20'>
        <MobileReviews reviews={product.reviews} />
      </div>

      <div className='mt-20 mb-10'>
        <MobileInfo />
      </div>
    </>
  );
};

export default MobileProductInfo;
