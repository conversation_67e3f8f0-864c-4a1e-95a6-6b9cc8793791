// src/redux/api/favoritesEndpoints.js
import { api } from '@/shared/api/api';

import type { GetFavoriteResponse } from './types';
import type { AdditionalServerResponseData } from '@/shared/types/AdditionalServerResponseData';
import type { ProductListRequest } from '@/shared/types/ProductListRequest';

export const favoritesEndpoints = api.injectEndpoints({
  endpoints: (builder) => ({
    getFavorites: builder.query<GetFavoriteResponse, void>({
      query: () => '/api/ProductsFavourites/get',
      providesTags: [{ type: 'Favorite', id: 'LIST' }],
    }),
    sendFavorites: builder.mutation<
      AdditionalServerResponseData,
      ProductListRequest
    >({
      query: (data) => ({
        url: '/api/ProductsFavourites/set',
        method: 'POST',
        body: data,
      }),
      // Don't invalidate any tags to prevent automatic refetching
      invalidatesTags: () => [],
      // Instead, manually update the cache
      async onQueryStarted(_arg, { queryFulfilled }) {
        try {
          await queryFulfilled;
          // No need to update the cache here - the Redux store is already updated
          // by the favorite slice, and components read from there
        } catch (_error) {
          // Error handling is done by the API layer
        }
      },
    }),
    removeFromFavorites: builder.mutation<
      AdditionalServerResponseData,
      ProductListRequest
    >({
      query: (data) => ({
        url: '/api/ProductsFavourites/delete',
        method: 'POST',
        body: data,
      }),
      // Don't invalidate any tags to prevent automatic refetching
      invalidatesTags: () => [],
      // Instead, manually update the cache
      async onQueryStarted(_arg, { queryFulfilled }) {
        try {
          await queryFulfilled;
          // No need to update the cache here - the Redux store is already updated
          // by the favorite slice, and components read from there
        } catch (_error) {
          // Error handling is done by the API layer
        }
      },
    }),
  }),
});

export const {
  useGetFavoritesQuery,
  useLazyGetFavoritesQuery,
  useSendFavoritesMutation,
  useRemoveFromFavoritesMutation,
} = favoritesEndpoints;
