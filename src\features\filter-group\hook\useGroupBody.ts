import { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { useGroupContext } from './useGroupContext';

export const useGroupBody = () => {
  const { isOpen, data } = useGroupContext();
  const [bodyHeight, setBodyHeight] = useState<number>(0);
  const bodyRef = useRef<HTMLDivElement | null>(null);
  const [visible, setVisible] = useState(false);

  useLayoutEffect(() => {
    if (!bodyRef.current) return;
    const height = bodyRef.current.scrollHeight;
    setBodyHeight(height);
    bodyRef.current.style.height = isOpen ? `${height}px` : '0px';
  }, [isOpen]);

  useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        setVisible(true);
      }, 200);
    } else {
      setVisible(false);
    }
  }, [isOpen]);

  return { bodyRef, bodyHeight, isOpen, data, visible };
};
