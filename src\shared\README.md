# Layer: `shared`

## Purpose

The `shared` layer contains reusable code and assets that are not specific to any particular business domain or feature of the application. It represents the lowest layer in the FSD hierarchy and has no dependencies on other layers.

## Responsibilities

* **Reusable UI Kit:** Providing a library of generic, application-agnostic UI components that can be used across the application.
* **Utility Functions:** Supplying common helper functions used across different parts of the application.
* **Core API Configuration:** Defining the base API setup, including configuration for RTK Query.
* **Common Types:** Defining broadly applicable TypeScript types and interfaces.
* **Global Configuration:** Storing application-wide constants or configuration values.
* **Static Assets:** Hosting reusable static assets like icons and images.

## Structure

The `shared` layer is organized by the type of code it contains:

```
src/shared/
├── api/                # Base API configuration
│   ├── api.ts          # RTK Query base setup
│   └── baseQuery.ts    # Custom fetch wrapper
├── assets/             # Static files
│   ├── icons/          # SVG icons
│   └── images/         # Images
├── config/             # Global configuration
│   └── index.ts        # Constants and environment variables
├── lib/                # Utility functions
│   ├── hooks/          # Custom hooks
│   ├── utils.ts        # General utilities
│   ├── cn.ts           # Class name utility
│   └── date.ts         # Date formatting utilities
├── types/              # Common TypeScript types
│   ├── common.ts       # Generic types
│   └── api.ts          # API-related types
├── ui/                 # Reusable UI components
│   ├── button/
│   ├── input/
│   ├── modal/
│   ├── icons/
│   └── ...
└── index.ts            # Public API exports
```

## Dependencies

* The `shared` layer **cannot** have dependencies on any other layer (`app`, `pages`, `widgets`, `features`, `entities`).
* It can import from external libraries (`node_modules`).

## Examples in Furnica

* **UI Components:** `src/shared/ui/button.tsx`, `src/shared/ui/input/`, `src/shared/ui/modal/`.
* **Utilities:** `src/shared/lib/cn.ts` (utility for merging Tailwind classes), `src/shared/lib/scrollToTop.ts`.
* **API:** `src/shared/api/api.ts` (base RTK Query configuration).
* **Assets:** SVG icons, background images, and other static files.
* **Types:** `src/shared/types/` for common type definitions.
* **Config:** App-wide constants and environment-specific settings.

## Key Characteristics

1. **Domain Agnostic:** Code in the `shared` layer should not contain business logic or domain-specific concepts.
2. **Highly Reusable:** Components and utilities should be designed for broad reuse across the application.
3. **Abstraction Layer:** Often provides abstraction over third-party libraries to make future library changes easier.
4. **Foundation:** Serves as the foundation for all other layers to build upon.

## Best Practices

1. **Keep It Generic:** Avoid adding business-specific logic or components to this layer. If a component or utility is tied to a specific feature or entity, it belongs in that layer instead.
2. **Clear Documentation:** Document UI components and utilities thoroughly, especially their props, parameters, and return values.
3. **Component Consistency:** UI components should follow consistent patterns for props, styling, and behavior.
4. **Careful API Design:** Exports from the `shared` layer are used extensively, so design their interfaces carefully.
5. **Minimize Dependencies:** Keep external dependencies to a minimum in this layer, as they will affect the entire application.
6. **Consistent Naming:**
   * UI component files should use either kebab-case (`button.tsx`) or PascalCase (`Button.tsx`), being consistent within the project
   * Utility functions should use camelCase (`formatDate.ts`)
   * Types/interfaces should use PascalCase (`ApiResponse.ts`)
7. **Structured UI Components:** For more complex UI components, use a folder structure:
   ```
   ui/
   ├── button/
   │   ├── Button.tsx
   │   ├── button.module.css (or variants.ts for Tailwind/Shadcn)
   │   └── index.ts
   ```

## Common Segments

* **`api/`**: Contains the base API setup and configuration.
* **`assets/`**: Static files like icons and images.
* **`config/`**: Application-wide constants and configuration.
* **`lib/`**: Utility functions and generic custom hooks.
* **`types/`**: Shared TypeScript interfaces and type definitions.
* **`ui/`**: Reusable UI components.

## UI Kit Components

Furnica's UI kit includes:

* **Basic Elements:** Button, Input, Checkbox, Radio, Select, etc.
* **Layout:** Container, Grid, Stack, Divider, etc.
* **Feedback:** Alert, Skeleton, Toast, Spinner, etc.
* **Navigation:** Link, Breadcrumb, Tabs, etc.
* **Overlays:** Modal, Popover, Tooltip, etc.
* **Data Display:** Badge, Table, Card, etc.

These components are often built on top of Shadcn UI primitives and styled with Tailwind CSS.

## Migration Notes

When refactoring legacy code:
* Identify truly reusable, domain-agnostic components and utilities
* Move them to the appropriate segment within the `shared` layer
* Ensure they don't contain business logic or dependencies on higher layers
* Update imports to use the new structure
* Consider standardizing the UI kit to use Shadcn UI / Tailwind CSS patterns
