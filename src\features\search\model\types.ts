export interface SearchState {
  searchTerm: string;
  isSearching?: boolean;
  recentSearches: string[];
  suggestions: {
    products: any[];
    categories: any[];
  };
  isSuggestionsOpen: boolean;
  isLoading: boolean;
  error: string | null;
  recentProductIds?: number[];
  selectedCategoryId: string | null;
}

export interface SearchParams {
  query: string;
  category_id?: string | null;
  page?: number;
  limit?: number;
}
