# Layer: `widgets`

## Purpose

Widgets are composite UI components that combine lower-level components (`features`, `entities`, `shared/ui`) into meaningful, self-contained blocks of the user interface. They represent distinct sections or parts of a page.

## Responsibilities

* **Composition:** Assembling UI and functionality from `features` and `entities` layers into a cohesive block.
* **Layout & Structure:** Defining the internal layout and structure of the widget.
* **Context Provision (Optional):** Sometimes providing a specific context for the features they contain.
* **Data Delegation:** Widgets usually don't fetch data directly but use data provided by the page or trigger hooks from `features`/`entities`. They might initiate data fetching for their *own* specific needs if the data isn't relevant outside the widget.
* **Business Logic (Minimal):** Widgets should contain minimal business logic, primarily focused on orchestrating their child components. Core logic resides in `features` and `entities`.

## Structure

Widgets are typically organized by their name and follow this structure:

```
src/widgets/
├── product-card/         # Widget slice
│   ├── ui/               # UI components
│   │   ├── ProductCard.tsx
│   │   ├── ProductCardLine.tsx
│   │   └── PriceDisplay.tsx
│   ├── lib/              # Helper functions for this widget
│   │   └── hooks/
│   │       └── useProductCard.ts
│   ├── index.ts          # Public API exports
│   └── README.md         # Documentation
└── ...
```

## Dependencies

* Widgets can depend on `features`, `entities`, and `shared`.
* Widgets **cannot** depend on `app` or `pages`.
* Widgets should generally not have strong dependencies on other widgets, though they might be composed together on a page.

## Examples in Furnica

* `src/widgets/header/`: Contains `Header`, `PreHeader`, `MobileTabbar`, etc. It uses features like `Search` and displays entity counts (cart, favorites).
* `src/widgets/product-card/`: Contains `ProductCard`, `ProductCardLine`, etc. It displays `Product` entity data and integrates features like `AddToCartButton`, `FavoriteButton`, `ComparisonButton`.
* `src/widgets/breadcrumbs/`: Displays navigation breadcrumbs based on the current route or entity hierarchy.
* `src/widgets/catalog-accordion/`: UI for displaying the category tree in an accordion format.
* `src/widgets/category-switcher/`: UI for switching between categories (used on search/comparison pages).

## Key Characteristics

1. **Self-Contained:** A widget should function as a complete, independent unit within its context, with clear boundaries.
2. **Composable:** Widgets can be easily placed on different pages or within other larger components.
3. **Reusable:** The same widget can be used in multiple places or contexts, though it may accept different props or configurations.
4. **Domain-Aware:** Unlike pure UI components in `shared/ui`, widgets understand their specific business domain and purpose.

## Best Practices

1. **Keep Logic Minimal:** Delegate business logic to the appropriate `features` or `entities` rather than implementing it within the widget.
2. **Prefer Composition:** Build widgets by composing smaller components from lower layers rather than creating monolithic components.
3. **Clear API:** Define a clear public API through the `index.ts` file, exporting only what's needed by higher layers.
4. **Consistent Naming:**
   - Widget folders should be named using kebab-case (`product-card`)
   - Main component files should use PascalCase (`ProductCard.tsx`)
5. **Extract Reusable Parts:** If a component within a widget could be reused across multiple widgets, consider moving it to `shared/ui` or creating a new widget.
6. **Document Clearly:** Each widget should have its own README.md explaining its purpose, usage, and key components.

## Migration Notes

Many components currently in `src/components/` function as widgets and should be refactored according to the FSD structure:

* Identify distinct UI blocks with a clear, focused purpose
* Determine whether they're truly widgets or if they are pages/features/entities
* Extract any business logic to the appropriate `features` or `entities` layer
* Organize according to the structure outlined above
