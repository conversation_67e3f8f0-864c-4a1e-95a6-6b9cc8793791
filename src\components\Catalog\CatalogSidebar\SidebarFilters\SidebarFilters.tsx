import { IOSSwitch } from '@/shared/ui';
import { FormControlLabel } from '@mui/material';
import SidebarFiltersSkeleton from '../SidebarFiltersSkeleton';
import BrandsFilter from './BrandsFilter';
import DynamicFilters from './DynamicFilters';
import PriceFilterDirect from './PriceFilterDirect';
import TagsFilters from './TagsFilters';

function SidebarFilters({
  setFiltersModalOpen,
  filters,
  setFilters,
  resetFilters,
  isLoading,
  trigger,
  setTrigger,
  filtersBlock
}) {
  // Check if some dynamics are marked as additional
  const hasAdditionalFilters = filters?.dynamics?.some((obj) => obj.additional_filter === true);
  console.log('Has additional filters:', hasAdditionalFilters);

  // Missing handleChange function
  const handleChange = (type, checked) => {
    console.log('High rating toggle changed:', type, checked);
    // This function appears to be incomplete in the original code
    // Should implement rating filter functionality
  };

  return (
    <>
      {isLoading && <SidebarFiltersSkeleton />}
      <div
        className={`${
          isLoading ? 'hidden' : ''
        }  border border-colSuperLight rounded-2xl px-3 pb-5 shadow-[0px_15px_20px_0px_rgba(0,_0,_0,_0.05)] mt-2 relative sticky top-0`}
      >
        {filtersBlock && (
          <div className=' cursor-wait absolute top-0 left-0 w-full h-full bg-white opacity-30 z-10'></div>
        )}
        {/* Price Filter Rendering */}
        {filters?.basics?.price && (
          <PriceFilterDirect
            filters={filters}
            setFilters={setFilters}
            trigger={trigger}
            setTrigger={setTrigger}
          />
        )}

        {/* Brands Filter Rendering */}
        {filters?.basics?.brands?.length > 0 && (
          <BrandsFilter filters={filters} setFilters={setFilters} />
        )}
        {/* Tags Filter Rendering */}
        {filters?.basics?.tags?.length > 0 ? (
          <TagsFilters filters={filters} setFilters={setFilters} />
        ) : (
          console.log('Not rendering TagsFilters - no tags data')
        )}
        {/* Dynamic Filters Rendering */}
        {filters?.dynamics?.length > 0 ? (
          <DynamicFilters filters={filters} setFilters={setFilters} />
        ) : (
          console.log('Not rendering DynamicFilters - no dynamics data')
        )}
        <FormControlLabel
          sx={{ margin: '10px 0' }}
          control={
            <IOSSwitch
              sx={{ m: 1 }}
              defaultChecked
              onChange={(e) => handleChange('highRating', e.target.checked)}
            />
          }
          labelPlacement='start'
          label={<p className='text-sm font-semibold text-colBlack'>Высокий рейтинг</p>}
        />
        {/* "All Filters" button */}
        {filters?.dynamics?.some((obj) => obj.additional_filter === true) > 0 && (
          <button
            onClick={() => {
              console.log('All filters button clicked');
              setFiltersModalOpen(true);
            }}
            className='bg-white border border-colGreen w-full rounded-md mb-3 p-2 text-colBlack font-semibold outline-none'
          >
            Все фильтры
          </button>
        )}
        <span
          onClick={() => {
            console.log('Reset filters clicked');
            resetFilters();
          }}
          className='text-colDarkGray font-semibold flex justify-center cursor-pointer'
        >
          Очистить фильтр
        </span>
      </div>
    </>
  );
}

export default SidebarFilters;
