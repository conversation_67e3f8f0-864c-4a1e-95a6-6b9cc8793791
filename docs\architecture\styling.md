# Styling Approach

The project employs a hybrid styling strategy, primarily using **Tailwind CSS** for utility-first styling, complemented by **Material UI (MUI)** components for specific complex UI elements and potentially legacy sections. **Shadcn UI** components are the preferred choice for new shared UI elements.

## 1. Tailwind CSS (Primary)

*   **Configuration:** Defined in `tailwind.config.js`. Includes custom colors (`colGreen`, `colBlack`, etc.) and screen breakpoints. It also references Shadcn UI's theme variables.
*   **Usage:** Applied directly in JSX via `className` attributes. Used for layout, spacing, typography, colors, and general component styling.
*   **`cn` Utility:** The `cn` utility function (from `clsx` and `tailwind-merge`), located in `src/shared/lib/utils.ts` (and aliased in `src/shared/lib/cn.ts`), **must** be used to conditionally apply Tailwind classes and correctly merge potentially conflicting utility classes (especially when combining base styles with variant styles or overrides).
*   **Global Styles:** Base styles, Tailwind layer directives (`@tailwind base`, `@tailwind components`, `@tailwind utilities`), and global CSS variables (including Shadcn/theme variables) are defined or imported in `src/app/index.css`.

## 2. Material UI (MUI) (Secondary/Legacy)

*   **Usage:** Utilized for specific, complex components where building from scratch with Tailwind/Shadcn would be significantly more effort (e.g., `DatePicker` from `@mui/x-date-pickers`, potentially `Modal`, `Accordion`, `Checkbox`, `Slider` where used). Also present in legacy code (e.g., parts of the Profile section, older forms).
*   **Styling MUI:**
    *   **`sx` Prop:** The preferred method for applying one-off style overrides directly on MUI components. It allows access to the theme (though theme usage seems minimal currently) and easy application of responsive styles or direct CSS properties.
    *   **Styled Components (`styled`)**: Used occasionally for creating reusable, specifically styled versions of MUI components (e.g., `src/helpers/FAQStyledAccordion.jsx`, `src/shared/ui/IOSSwitch.tsx`). Uses `@mui/material/styles` or potentially `@emotion/styled`.
    *   **Tailwind Classes (Limited):** While possible to apply Tailwind classes via `className` to some MUI components, it's often less reliable than `sx` due to MUI's internal structure and styling. Use with caution.
*   **Goal:** Minimize the introduction of *new* MUI components, favoring Shadcn UI or pure Tailwind solutions. Refactor existing complex MUI implementations where a simpler Shadcn/Tailwind alternative provides equivalent functionality and accessibility without significant effort.

## 3. Shadcn UI (Preferred for Shared UI)

*   **Purpose:** Acts as a library of reusable, unstyled component *primitives* built with accessibility in mind. These are copied into the project (`src/shared/ui/`) and then styled using Tailwind CSS utility classes, often leveraging `cva` (class-variance-authority) for variants.
*   **Location:** Base Shadcn components reside in `src/shared/ui/`. Custom components built *using* these primitives might also live there or within specific feature/widget UI segments if not broadly shared.
*   **Customization:** Because the components are part of the project's source code, they can be directly modified and styled to fit the application's design system.
*   **Configuration:** `components.json` defines project structure aliases (`@/shared/ui`, `@/shared/lib/utils`) and Tailwind settings, used by the Shadcn UI CLI for adding new components.

## 4. Global Styles & Fonts

*   Global resets, base styles, Tailwind directives, and CSS variable definitions are located in `src/app/index.css`.
*   The primary font ('Raleway') is imported via `<link>` tags in `index.html`.

## Conventions & Goals

*   **Prioritize Tailwind & Shadcn UI:** Use Tailwind for general styling and layout. Build or adapt shared UI components using Shadcn UI primitives located in `src/shared/ui/`.
*   **Use `cn`:** Always use the `cn` utility for combining Tailwind classes dynamically or merging overrides.
*   **Limit MUI:** Reserve MUI for components offering complex functionality not easily replicated (like date pickers) or where significant legacy implementation exists. Avoid mixing MUI styling methods (`sx` vs. `styled`) heavily within the same component if possible.
*   **Consistency:** Maintain visual consistency by primarily using the Tailwind theme configuration (colors, spacing, fonts) even when overriding MUI styles via the `sx` prop.
*   **Refactoring:** Gradually replace legacy custom components or excessive MUI usage with standardized `shared/ui` components (based on Shadcn) or pure Tailwind solutions during the FSD migration.