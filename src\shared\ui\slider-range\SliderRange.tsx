import { cn } from '@/shared/lib';
import * as SliderPrimitive from '@radix-ui/react-slider';
import { forwardRef } from 'react';
import { Tooltip, TooltipContent, TooltipTrigger } from '../tooltip/tooltip';

export const SliderRange = forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>
>(({ className, ...props }, ref) => {
  return (
    <SliderPrimitive.Root
      ref={ref}
      className={cn('relative flex w-full touch-none select-none items-center', className)}
      {...props}
    >
      <SliderPrimitive.Track className='relative h-[2px] w-full grow overflow-hidden rounded-full bg-primary/20'>
        <SliderPrimitive.Range className='absolute h-full bg-[#15765B]' />
      </SliderPrimitive.Track>
      {(props.value ?? props.defaultValue)?.map((_, index) => (
        <SliderPrimitive.Thumb
          key={index}
          className='group block h-4 w-4 rounded-full border border-[#15765B] bg-[#15765B] shadow transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#BFF4E5] disabled:pointer-events-none disabled:opacity-50 cursor-pointer'
        >
          <Tooltip>
            <TooltipTrigger asChild>
              <div className=' absolute left-0 top-0 opacity-0 p-2 bg-[red]'></div>
            </TooltipTrigger>
            <TooltipContent
              className='[&>svg]:fill-[colGreen] group/badge'
              isArrow={true}
              arrowClass='fill-colGreen'
            >
              <div className='bg-colGreen text-white lining-nums py-1 px-2 rounded-[6px] text-[14px] transition-transform'>
                {props.value[index]}
              </div>
            </TooltipContent>
          </Tooltip>
        </SliderPrimitive.Thumb>
      ))}
    </SliderPrimitive.Root>
  );
});

SliderRange.displayName = SliderPrimitive.Root.displayName;
