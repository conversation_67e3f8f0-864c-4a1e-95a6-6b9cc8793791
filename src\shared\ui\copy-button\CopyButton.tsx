import { Icon } from '@iconify-icon/react';
import { ContentCopy } from '@mui/icons-material';
import { toast } from 'sonner';

interface CopyButtonProps {
  textToCopy: string;
  className?: string;
  containerClassName?: string;
  iconClassName?: string;
  toastMessage?: string;
}

export const CopyButton = ({
  textToCopy,
  className = '',
  containerClassName = '',
  iconClassName = '',
  toastMessage = 'Скопировано',
}: CopyButtonProps) => {
  const handleCopy = () => {
    navigator.clipboard.writeText(textToCopy);
    toast(toastMessage);
  };

  return (
    <div className={`flex  justify-center items-center cursor-pointer ${containerClassName} ${className}`} onClick={handleCopy}>
      <Icon icon="si:copy-line" className={` text-colGray opacity-50 hover:opacity-100 ${iconClassName}`} />
    </div>
  );
};
