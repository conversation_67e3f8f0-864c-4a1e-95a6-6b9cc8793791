/**
 * Format a phone number based on a mask pattern
 * The mask is used as a formatting guide, but won't restrict input length
 * 
 * @param {string} value - The phone number to format
 * @param {string} mask - The mask pattern (X represents a digit)
 * @returns {string} - The formatted phone number
 */
export const formatPhoneNumber = (value, mask) => {
  if (!value || !mask) return value;
  
  let result = '';
  let valueIndex = 0;
  const digitsOnly = value.replace(/\D/g, '');
  
  // Process digits according to mask first
  for (let i = 0; i < mask.length && valueIndex < digitsOnly.length; i++) {
    if (mask[i].toUpperCase() === 'X') {
      result += digitsOnly[valueIndex];
      valueIndex++;
    } else {
      result += mask[i];
      // Only advance the valueIndex if the character matches
      if (digitsOnly[valueIndex] === mask[i]) {
        valueIndex++;
      }
    }
  }
  
  // Important: Append any remaining digits that don't fit the mask
  // This ensures we don't block input when user exceeds mask length
  if (valueIndex < digitsOnly.length) {
    result += digitsOnly.substring(valueIndex);
  }
  
  return result;
};

/**
 * Clean a phone number by removing all non-digit characters
 * 
 * @param {string} value - The phone number to clean
 * @returns {string} - The cleaned phone number (digits only)
 */
export const cleanPhoneNumber = (value) => {
  if (!value) return '';
  return value.replace(/\D/g, '');
};

/**
 * Format a phone number with a country code
 * 
 * @param {string} value - The phone number to format
 * @param {string} countryCode - The country code
 * @returns {string} - The formatted phone number with country code
 */
export const formatWithCountryCode = (value, countryCode) => {
  if (!value) return '';
  
  const cleanedNumber = cleanPhoneNumber(value);
  
  // Check if the number already starts with the country code
  if (cleanedNumber.startsWith(countryCode)) {
    return cleanedNumber;
  }
  
  return `${countryCode}${cleanedNumber}`;
};

/**
 * Strip country code from the beginning of a phone number if present
 * 
 * @param {string} value - The phone number to process
 * @param {string} countryCode - The country code to remove
 * @returns {string} - The phone number without country code
 */
export const stripCountryCode = (value, countryCode) => {
  if (!value || !countryCode) return value;
  
  const cleanedNumber = cleanPhoneNumber(value);
  
  // If the number starts with the country code, remove it
  if (cleanedNumber.startsWith(countryCode)) {
    return cleanedNumber.substring(countryCode.length);
  }
  
  return cleanedNumber;
};

/**
 * Determine if a phone number is potentially valid
 * 
 * @param {string} value - The phone number to check
 * @returns {boolean} - Whether the phone number is potentially valid
 */
export const isPotentiallyValidPhoneNumber = (value) => {
  if (!value) return false;
  
  const digitsOnly = cleanPhoneNumber(value);
  
  // Most valid phone numbers have at least 7 digits
  return digitsOnly.length >= 7;
};

/**
 * Create a placeholder based on a mask pattern
 * 
 * @param {string} mask - The mask pattern
 * @param {string} placeholder - Character to use for digit placeholders
 * @returns {string} - A user-friendly placeholder
 */
export const createPlaceholderFromMask = (mask, placeholder = '_') => {
  if (!mask) return '';
  return mask.replace(/X/g, placeholder);
};

export default {
  formatPhoneNumber,
  cleanPhoneNumber,
  formatWithCountryCode,
  stripCountryCode,
  isPotentiallyValidPhoneNumber,
  createPlaceholderFromMask
};