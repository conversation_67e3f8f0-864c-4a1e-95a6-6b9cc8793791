import React from 'react';

interface CheckoutSummaryProps {
  totalAmount: number;
  totalQuantity: number;
  discount?: number;
  bonusEarned?: number;
  bonusUsed?: number;
}

export const CheckoutSummary: React.FC<CheckoutSummaryProps> = ({
  totalAmount,
  totalQuantity,
  discount = 0,
  bonusEarned = 0,
  bonusUsed = 0
}) => {
  // Format price with spaces as thousand separators
  const formatPrice = (price: number) => {
    return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ");
  };

  // Calculate final price
  const finalAmount = totalAmount - discount - bonusUsed;

  // Correctly format Russian plurals for products
  const formatProductsLabel = (count: number): string => {
    // Last digit determines the form for Russian plurals
    const lastDigit = count % 10;
    const lastTwoDigits = count % 100;
    
    // Exception for numbers ending in 11-14
    if (lastTwoDigits >= 11 && lastTwoDigits <= 14) return 'товаров';
    
    if (lastDigit === 1) return 'товар';
    if (lastDigit >= 2 && lastDigit <= 4) return 'товара';
    return 'товаров';
  };

  return (
    <div style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}>
      <h3 className="font-semibold text-2xl mb-4">Ваш заказ</h3>
      
      {/* Order summary items */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2 ">
          <span>Количество товаров:</span>
          <span>{totalQuantity} {formatProductsLabel(totalQuantity)}</span>
        </div>
        
        {/* <div className="flex justify-between items-center mb-2 text-sm">
          <span>Товары:</span>
          <span className="font-medium">{formatPrice(totalAmount)} ₽</span>
        </div> */}
        
        {discount > 0 && (
          <div className="flex justify-between items-center mb-2 text-sm text-[#15765B]">
            <span>Скидка:</span>
            <span>− {formatPrice(discount)} ₽</span>
          </div>
        )}
        
        {bonusUsed > 0 && (
          <div className="flex justify-between items-center mb-2 text-sm text-[#15765B]">
            <span>Оплачено бонусами:</span>
            <span>− {formatPrice(bonusUsed)} ₽</span>
          </div>
        )}
      </div>
      
      {/* Earned bonus points if applicable */}
      {bonusEarned > 0 && (
        <div className="bg-[#E8F5F1] p-3 rounded-lg mb-4 text-sm text-[#2A6B5A]">
          <div className="flex justify-between items-center">
            <span>Начислим бонусов:</span>
            <span className="font-medium">+ {formatPrice(bonusEarned)}</span>
          </div>
        </div>
      )}
      
      {/* Final total */}
      <div className="flex justify-between items-center mt-4 text-2xl">
        <span className="font-medium text-[#222222]">Итого:</span>
        <span className="font-bold text-[#15765B]">{formatPrice(finalAmount)} ₽</span>
      </div>
    </div>
  );
};
