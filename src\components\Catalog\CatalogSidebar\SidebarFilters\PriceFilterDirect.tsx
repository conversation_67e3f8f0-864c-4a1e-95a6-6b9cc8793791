// src/components/CatalogSidebar/SidebarFilters/PriceFilterDirect.tsx
// This is an experimental implementation that calls setFilters directly instead of using useEffect

import React, { useEffect, useRef, useState } from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Slider,
} from "@mui/material";

import { ArrowIcon } from "@/shared/ui";
import { CTextField } from "@/shared/ui";

function PriceFilterDirect({ filters, setFilters, trigger, setTrigger }) {
  // Reference to track previous values to avoid unnecessary updates
  const previousValues = useRef({
    min: 0,
    max: 0,
  });

  // Local state for UI display
  const [inputValues, setInputValues] = useState({
    min: filters?.basics?.price?.current_values?.min || filters?.basics?.price?.min || 0,
    max: filters?.basics?.price?.current_values?.max || filters?.basics?.price?.max || 0,
  });
  
  // Local state for slider display
  const [sliderValues, setSliderValues] = useState({
    min: filters?.basics?.price?.current_values?.min || filters?.basics?.price?.min || 0,
    max: filters?.basics?.price?.current_values?.max || filters?.basics?.price?.max || 0,
  });

  // When filters change from outside, update our local state
  useEffect(() => {
    const newMin = filters?.basics?.price?.current_values?.min || filters?.basics?.price?.min || 0;
    const newMax = filters?.basics?.price?.current_values?.max || filters?.basics?.price?.max || 0;
    
    setInputValues({
      min: newMin,
      max: newMax,
    });
    
    setSliderValues({
      min: newMin,
      max: newMax,
    });
    
    // Update our reference of previous values
    previousValues.current = {
      min: newMin,
      max: newMax,
    };
  }, [
    filters?.basics?.price?.min,
    filters?.basics?.price?.max,
    filters?.basics?.price?.current_values?.min,
    filters?.basics?.price?.current_values?.max,
    trigger
  ]);

  // Handle slider changes
  const handleSliderChange = (event, newValues) => {
    const [newMin, newMax] = newValues;
    
    setSliderValues({
      min: newMin,
      max: newMax,
    });
    
    setInputValues({
      min: newMin,
      max: newMax,
    });
  };

  // When slider movement ends, update the filters
  const handleSliderChangeCommitted = (event, newValues) => {
    const [newMin, newMax] = newValues;
    
    // // Don't update if these are the initial values being set or if they haven't changed
    // if (
    //   trigger === "categoryId" || 
    //   trigger === "tags" || 
    //   trigger === "brands" ||
    //   (previousValues.current.min === newMin && previousValues.current.max === newMax)
    // ) {
    //   return;
    // }
    
    updateFilters(newMin, newMax);
  };

  // Input field handlers
  const handleMinInputChange = (event) => {
    setInputValues(prev => ({
      ...prev,
      min: event.target.value,
    }));
  };

  const handleMaxInputChange = (event) => {
    setInputValues(prev => ({
      ...prev,
      max: event.target.value,
    }));
  };
  
  // Validate and apply min price on blur
  const validateAndSetMin = () => {
    let min = inputValues.min === "" 
      ? filters?.basics?.price?.min
      : Number(inputValues.min);
      
    // Ensure min is within valid range
    min = Math.max(min, filters?.basics?.price?.min || 0);
    
    // Ensure min doesn't exceed max
    if (min > sliderValues.max) {
      min = sliderValues.max;
    }
    
    // Update local states
    setInputValues(prev => ({ ...prev, min }));
    setSliderValues(prev => ({ ...prev, min }));
    
    // Update filters if different from previous values
    if (min !== previousValues.current.min) {
      updateFilters(min, sliderValues.max);
    }
  };
  
  // Validate and apply max price on blur
  const validateAndSetMax = () => {
    let max = inputValues.max === "" 
      ? filters?.basics?.price?.max
      : Number(inputValues.max);
      
    // Ensure max is within valid range
    max = Math.min(max, filters?.basics?.price?.max || 100000);
    
    // Ensure max isn't less than min
    if (max < sliderValues.min) {
      max = sliderValues.min;
    }
    
    // Update local states
    setInputValues(prev => ({ ...prev, max }));
    setSliderValues(prev => ({ ...prev, max }));
    
    // Update filters if different from previous values
    if (max !== previousValues.current.max) {
      updateFilters(sliderValues.min, max);
    }
  };
  
  // Central function to update the filters
  const updateFilters = (min, max) => {
    // Skip if there's no actual change
    if (previousValues.current.min === min && previousValues.current.max === max) {
      return;
    }
    
    // Create a deep copy of filters to avoid side effects
    const currentState = JSON.parse(JSON.stringify(filters));
    
    // Update the price filter values
    currentState.basics.price.current_values = {
      min,
      max,
    };
    
    // Set lastChanged property to indicate what changed
    currentState.lastChanged = {
      type: "basics",
      filter: "price",
    };
    
    // Update our reference to prevent duplicate updates
    previousValues.current = {
      min,
      max,
    };
    
    // Update the filters
    setFilters(currentState);
  };

  return (
    <Accordion
      sx={{
        boxShadow: "none",
        padding: 0,
        margin: 0,
        border: "none",
        "&:before": {
          display: "none",
        },
        "&.Mui-expanded": {
          margin: 0,
        },
      }}
      defaultExpanded
      disableGutters
    >
      <AccordionSummary
        sx={{ padding: 0, flexDirection: "row-reverse", gap: "8px" }}
        style={{ minHeight: 0 }}
        expandIcon={<ArrowIcon className="!w-4 !h-4 rotate-[180deg]" />}
      >
        <span className="font-semibold text-colBlack">Цена, ₽</span>
      </AccordionSummary>
      <AccordionDetails sx={{ padding: 0 }}>
        <Slider
          sx={{ color: "#15765B" }}
          size="small"
          getAriaLabel={() => "Price range"}
          value={[Number(sliderValues.min) || 0, Number(sliderValues.max) || 0]}
          min={Number(filters?.basics?.price?.min) || 0}
          max={Number(filters?.basics?.price?.max) || 100000}
          onChange={handleSliderChange}
          onChangeCommitted={handleSliderChangeCommitted}
          valueLabelDisplay="auto"
        />
        <Box>
          <div className="grid grid-cols-2 gap-3 pb-3">
            <CTextField
              label={`от ${filters?.basics?.price?.min}`}
              name="min_price"
              type="number"
              value={inputValues.min}
              onChange={handleMinInputChange}
              onBlur={validateAndSetMin}
            />
            <CTextField
              label={`до ${filters?.basics?.price?.max}`}
              name="max_price"
              type="number"
              value={inputValues.max}
              onChange={handleMaxInputChange}
              onBlur={validateAndSetMax}
            />
          </div>
        </Box>
      </AccordionDetails>
    </Accordion>
  );
}

export default PriceFilterDirect;
