// src/features/recent-items/model/hooks/useRecentItemsSync.ts
import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useAuthContext } from '@/entities/user/model/AuthContext';
import {
  useGetRecentItemsQuery,
  setRecentItems,
} from '@/features/recent-items';
import {
  saveToSessionStorage,
  getFromSessionStorage,
} from '@/features/storage/lib';

import type { RootState } from '@/app/providers/store';
import type { RecentItemsProduct } from '@/features/recent-items/model/types';

/**
 * Enhanced hook for recent items synchronization
 * Provides methods for loading, saving, syncing, and clearing recent items data
 * Note: Recent items are read-only from server (no sync to server needed)
 */
export const useRecentItemsSync = () => {
  const { isAuthenticated } = useAuthContext();
  const recentItems = useSelector((state: RootState) => state.recentItems.recentItems);
  const dispatch = useDispatch();

  // RTK Query hooks
  const {
    isLoading,
    error,
    refetch: refetchRecentItems,
  } = useGetRecentItemsQuery(undefined, {
    skip: !isAuthenticated,
    refetchOnMountOrArgChange: false,
    refetchOnFocus: false,
    refetchOnReconnect: false,
  });

  // Load recent items from sessionStorage
  const loadFromStorage = useCallback(() => {
    try {
      const storedRecentItems = getFromSessionStorage('recentItems') as RecentItemsProduct[] | null;
      if (storedRecentItems && Array.isArray(storedRecentItems)) {
        dispatch(setRecentItems(storedRecentItems));
      }
    } catch (error) {
      console.error('[useRecentItemsSync] Error loading from storage:', error);
    }
  }, [dispatch]);

  // Save recent items to sessionStorage
  const saveToStorage = useCallback(
    (recentItemsData = recentItems) => {
      try {
        if (!isAuthenticated && recentItemsData) {
          saveToSessionStorage('recentItems', recentItemsData);
        }
      } catch (error) {
        console.error('[useRecentItemsSync] Error saving to storage:', error);
      }
    },
    [isAuthenticated, recentItems]
  );

  // Refresh recent items from server
  const refreshFromServer = useCallback(async () => {
    if (isAuthenticated) {
      try {
        const result = await refetchRecentItems();
        
        if (result.data?.data) {
          dispatch(setRecentItems(result.data.data));
        }
        
        return result;
      } catch (error) {
        console.error('[useRecentItemsSync] Error fetching from server:', error);
        throw error;
      }
    }
    return Promise.resolve({ data: recentItems });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated, refetchRecentItems]);

  // Recent items don't sync to server (read-only from server)
  const syncToServer = useCallback(async () => {
    // Recent items are managed by server based on user activity
    // No need to sync local data to server
    return Promise.resolve();
  }, []);

  // Clear recent items data
  const clearData = useCallback(() => {
    dispatch(setRecentItems([]));
    sessionStorage.removeItem('recentItems');
  }, [dispatch]);

  return {
    recentItems,
    isLoading,
    isError: !!error,
    loadFromStorage,
    saveToStorage,
    refreshFromServer,
    syncToServer, // No-op for recent items
    clearData,
  };
};
