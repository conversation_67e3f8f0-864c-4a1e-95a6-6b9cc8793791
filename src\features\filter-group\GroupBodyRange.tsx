import { Range } from '../range';
import { useGroupBodyRange } from './hook/useGroupBodyRange';

interface GroupBodyRange {
  children?: React.ReactNode;
}

export const GroupBodyRange = () => {
  const { range, setRange, max, min } = useGroupBodyRange();

  return (
    <div className=''>
      <Range min={min} max={max} defaultValue={range} onValueCommit={setRange}>
        <Range.Inputs />
        <Range.Sliders />
      </Range>
    </div>
  );
};
