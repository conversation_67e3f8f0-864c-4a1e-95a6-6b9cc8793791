import { useEffect, useRef, useState } from 'react';
import { useRange } from '../hook/useRange';

export const InputRange = () => {
  const { value, setValue, min, max, onValueCommit, disabled } = useRange();
  const uuid = useRef(Math.random().toString().split('.')[1]);

  const [range, setRange] = useState<[number, number]>(value);

  const clamp = (val: number) => Math.max(min, Math.min(max, val));

  const handleBlur = (index: number, event: React.FocusEvent<HTMLInputElement>) => {
    let val = Number(event.target.value.replace(/\D/g, ''));
    val = clamp(val);
    if (index === 0) {
      if (val > value[1]) {
        val = value[1];
      }

      const updated = [val, value[1]] as [number, number];
      event.target.value = String(val);
      setValue(updated);
      if (updated[0] === min && updated[1] === max) {
        onValueCommit(null);
      } else {
        onValueCommit(updated);
      }
    }

    if (index === 1) {
      if (val < value[0]) {
        val = value[0];
      }

      const updated = [value[0], val] as [number, number];
      event.target.value = String(val);
      setValue(updated);
      if (updated[0] === min && updated[1] === max) {
        onValueCommit(null);
      } else {
        onValueCommit(updated);
      }
    }
  };

  const handleChange = (index: number, event: React.ChangeEvent<HTMLInputElement>) => {
    if (index === 0) {
      setRange([Number(event.target.value), range[1]]);
    } else {
      setRange([range[0], Number(event.target.value)]);
    }
  };

  useEffect(() => {
    if (value === null) {
      setRange([min, max]);
    } else {
      setRange(value);
    }
  }, [value]);

  return (
    <div className='flex gap-2 w-full items-center'>
      <label htmlFor={`${uuid.current}-min`} className='flex flex-col w-[calc(50%_-_4px)]'>
        <span>От</span>
        <input
          type='number'
          value={range === null ? '' : range[0]}
          min={min}
          max={max}
          id={`${uuid.current}-min`}
          onChange={(e) => handleChange(0, e)}
          onBlur={(e) => handleBlur(0, e)}
          className='border rounded-[8px] focus:outline-[#15765B] bg-[#E6E9E8] p-[8px] leading-[16px] lining-nums'
          disabled={disabled}
        />
      </label>
      <label htmlFor={`${uuid.current}-max`} className='flex flex-col w-[calc(50%_-_4px)]'>
        <span>До</span>
        <input
          type='number'
          value={range === null ? max : range[1]}
          min={min}
          max={max}
          id={`${uuid.current}-max`}
          onBlur={(e) => handleBlur(1, e)}
          onChange={(e) => handleChange(1, e)}
          className='border rounded-[8px] focus:outline-[#15765B] bg-[#E6E9E8] p-[8px] leading-[16px] lining-nums'
          disabled={disabled}
        />
      </label>
    </div>
  );
};
