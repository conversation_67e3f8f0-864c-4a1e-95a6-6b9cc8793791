# Layer: `features`

## Purpose

The `features` layer contains the implementation of specific user scenarios, actions, or business logic within the application. Each feature represents a distinct piece of functionality that delivers value to the user, often involving interaction with one or more business entities.

## Responsibilities

* **Handling User Interactions:** Implementing the logic triggered by user actions (e.g., clicking "Add to Car<PERSON>", submitting a login form, toggling a favorite).
* **Orchestrating Entities:** Interacting with `entities` (fetching data via entity hooks, updating entity state) to fulfill the feature's goal.
* **Business Logic:** Containing logic specific to the user scenario (e.g., validation rules for a form, calculation logic specific to a feature).
* **Feature-Specific State:** Managing state relevant only to the feature's operation, often using Redux Toolkit slices within the `model` segment.
* **API Calls:** Defining and triggering API calls (mutations or specific queries) related to the feature's action using RTK Query endpoints within the `api` segment.
* **UI (Optional but Common):** Providing UI components directly related to the feature's interaction point (e.g., `AddToCartButton`, `LoginForm`, `SearchSuggestions`).

## Structure

Features are organized into slices based on the functionality they provide. Each feature slice typically follows this structure:

```
src/features/
├── auth/                        # Authentication feature slice
│   ├── ui/                      # UI components
│   │   ├── LoginForm.tsx
│   │   ├── RegistrationForm.tsx
│   │   └── PhoneVerification.tsx
│   ├── model/                   # State and business logic
│   │   ├── types.ts
│   │   └── validators.ts
│   ├── api/                     # API endpoints
│   │   ├── authApi.ts
│   │   └── types.ts
│   ├── lib/                     # Helper functions
│   │   ├── utils.ts
│   │   └── hooks/
│   │       └── useAuthForm.ts
│   ├── index.ts                 # Public API exports
│   └── README.md                # Documentation
└── ...
```

## Dependencies

* Features can depend on `entities` and `shared`.
* Features **cannot** depend on `widgets`, `pages`, or `app`.
* Features should generally **not** depend directly on other features. Communication between features usually occurs indirectly via:
  * Shared entity state (e.g., auth feature updates user state, cart feature reads it).
  * Events orchestrated by higher layers (widgets or pages).
  * Shared utility functions (`shared/lib`).

## Examples in Furnica

* `src/features/auth/`: User authentication flows (login, registration, password reset).
* `src/features/cart/`: Core shopping cart actions (adding, removing, updating quantity), related state management, and UI controls.
* `src/features/search/`: Search bar interaction logic, suggestion fetching, and related UI.
* `src/features/favorite/`: Toggling items in favorites, state management, and UI controls.
* `src/features/comparison/`: Toggling items in comparison, state management, and UI controls.
* `src/features/fast-order/`: "Buy in 1 Click" modal and API interaction.
* `src/features/modals/`: The generic modal management system.
* `src/features/recent-items/`: Logic for tracking recently viewed items.
* `src/features/cart-share/`: Functionality for sharing the cart contents.

## Key Characteristics

1. **Use Case Focus:** A feature implements a specific user scenario or use case.
2. **Self-Contained:** A feature should encapsulate its own logic, state, and UI components.
3. **Entity Orchestration:** Features typically work with one or more entities but don't own the entity data models.
4. **Focused API:** Features expose a clean public API through their `index.ts` file.

## Best Practices

1. **Single Responsibility:** Each feature should focus on a specific piece of functionality.
2. **Isolation:** Features shouldn't directly depend on each other to avoid tight coupling.
3. **State Management:** Use Redux Toolkit slices for feature-specific state and RTK Query for API interactions.
4. **Clear API Boundaries:** Define a clear public API through the `index.ts` file, exporting only what's needed by higher layers.
5. **Co-location:** Keep all related parts of a feature (UI, state, API) together in the same slice.
6. **Consistent Naming:**
   * Feature folders should be named using kebab-case (`cart-share`)
   * Component files should use PascalCase (`LoginForm.tsx`)
   * Hooks should start with `use` (`useAuth.ts`)
7. **Document Clearly:** Each feature should have its own README.md explaining its purpose, functionality, and usage.

## Common Segments

* **`ui/`**: React components specific to this feature. These components should focus on presenting the feature's interface and delegating logic to hooks or functions from other segments.
* **`model/`**: Contains state management (Redux slices), business logic, and types related to the feature.
* **`api/`**: Contains RTK Query endpoint definitions related to the feature's actions.
* **`lib/`**: Utility functions and hooks specific to the feature. Commonly includes validation logic, formatters, or complex hooks encapsulating feature behavior.

## Migration Notes

When refactoring legacy code:
* Look for code that implements specific user scenarios or actions
* Identify the business domain it belongs to
* Extract it into a properly structured feature slice
* Ensure it properly interacts with entities rather than directly manipulating state or API
