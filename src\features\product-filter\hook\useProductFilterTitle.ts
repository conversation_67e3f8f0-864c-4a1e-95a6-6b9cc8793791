import { useEffect, useState } from 'react';
import { useProductFilterContext } from './useProductFIlterContext';

export const useProductFilterTitle = () => {
  const { data, isOpen, setIsOpen, disabled = false } = useProductFilterContext();

  const [isDisabled, setIsDisabled] = useState(disabled);

  const [is_show_selected, setIs_show_selected] = useState(false);

  const checkIsShowSelected = () => {
    if (data.input_type === 'range') return;

    if (data.input_type === 'multiple') {
      const isAnySelected = data.values.some((item) => item.is_selected);
      setIs_show_selected(isAnySelected);
    }
  };

  useEffect(() => {
    checkIsShowSelected();
  }, [data]);

  const toggle = () => {
    if (isDisabled) return;
    setIsDisabled(true);
    setIsOpen(!isOpen);
    setTimeout(() => {
      setIsDisabled(false);
    }, 200);
  };

  return {
    data,
    isOpen,
    setIsOpen,
    isDisabled,
    toggle,
    is_show_selected
  };
};
