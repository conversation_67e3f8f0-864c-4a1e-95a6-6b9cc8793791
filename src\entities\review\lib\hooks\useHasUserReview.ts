import { useSelector } from 'react-redux';
import { useGetSelfReviewsQuery } from '../../api/reviewApi';
import type { RootState } from '@/store';

export const useHasUserReview = (productSlug: string) => {
  const isAuthenticated = useSelector(
    (state: RootState) => state.user.isAuthenticated
  );
  const isInitialized = useSelector(
    (state: RootState) => state.user.isInitialized
  );

  const { data } = useGetSelfReviewsQuery(undefined, {
    skip: !isInitialized || !isAuthenticated,
    selectFromResult: ({ data }) => ({
      data: data?.comments.find(review => review.item.slug === productSlug),
    }),
  });

  return {
    hasReview: Boolean(data),
    isLoading: !isInitialized || (isAuthenticated && !data),
  };
};
