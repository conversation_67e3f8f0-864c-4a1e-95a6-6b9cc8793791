/* 
 * GX Phone Widget React Implementation Styles
 * These styles are designed to closely resemble the original GX phone widget UI
 */

/* Container styles */
.gx-phone-react-container {
  position: relative;
  max-width: 100%;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Phone input styles */
.gx-phone-input-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  overflow: hidden;
  transition: all 0.2s ease;
  background-color: #fff;
  height: 42px; /* Set fixed height to prevent size changes */
}

.gx-phone-input-wrapper:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
}

.gx-phone-input-wrapper.invalid {
  border-color: #ef4444;
}

.gx-phone-input-wrapper.verified {
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.25);
}

/* Country selector button */
.gx-country-selector-button {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: rgba(226, 232, 240, 0.3);
  border: none;
  border-right: 1px solid #e2e8f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
  height: 100%;
}

.gx-country-selector-button:hover {
  background: rgba(226, 232, 240, 0.5);
}

.gx-country-selector-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.gx-country-flag {
  width: 1.5rem;
  height: 1rem;
  margin-right: 0.5rem;
  object-fit: cover;
  border-radius: 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.gx-country-code {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e293b;
}

/* Phone input field */
.gx-phone-input-field {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: none;
  outline: none;
  font-size: 1rem;
  color: #1e293b;
  background: transparent;
  height: 100%;
}

.gx-phone-input-field::placeholder {
  color: #94a3b8;
}

.gx-phone-input-field:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Action buttons */
.gx-action-button {
  padding: 0 0.75rem;
  background: transparent;
  border: none;
  cursor: pointer;
  color: #64748b;
  transition: color 0.2s ease;
}

.gx-action-button:hover {
  color: #1e293b;
}

.gx-action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Inline send code button */
.gx-inline-button {
  width: 2.5rem;
  height: 34px; /* Adjusted height to match input */
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #16A34A !important; /* Important to override Tailwind defaults */
  color: white;
  border: none;
  border-radius: 0.25rem;
  margin: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  appearance: none !important; /* Override browser default styles */
}

.gx-inline-button:hover {
  background-color: #15803D !important;
}

.gx-inline-button:disabled {
  background-color: #cbd5e1 !important;
  cursor: not-allowed;
}

/* Add a class for the arrow icon inside the button to ensure it stays white */
.gx-inline-button svg {
  color: white !important;
}

/* Spinner animation */
.gx-spinner {
  animation: spin 1s linear infinite;
  width: 1.25rem;
  height: 1.25rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.gx-spinner circle {
  opacity: 0.25;
}

.gx-spinner circle:nth-child(1) {
  opacity: 0.75;
}

/* Verification input styles */
.gx-verification-input {
  margin-top: 1rem;
}

.gx-verification-digits {
  display: flex;
  justify-content: center;
  gap: 0.375rem;
  margin-bottom: 0.75rem;
}

.gx-verification-digit-input {
  width: 2.75rem;
  height: 3rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  text-align: center;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  transition: all 0.2s ease;
}

.gx-verification-digit-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
  outline: none;
}

.gx-verification-input.invalid .gx-verification-digit-input {
  animation: shake 0.4s ease-in-out;
  border-color: #ef4444;
}

.gx-verification-hint {
  font-size: 0.875rem;
  color: #64748b;
  text-align: center;
  margin: 0;
}

/* Verification success state */
.gx-verification-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
}

.gx-verification-success-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background-color: #ecfdf5;
  border-radius: 9999px;
  color: #10b981;
}

.gx-verification-success-text {
  font-size: 1rem;
  font-weight: 600;
  color: #10b981;
}

.gx-verification-edit-button {
  margin-top: 0.5rem;
  padding: 0.25rem 0.75rem;
  background: transparent;
  border: 1px solid #e2e8f0;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.gx-verification-edit-button:hover {
  background-color: #f8fafc;
  color: #1e293b;
}

/* Send code button */
.gx-send-code-button {
  display: block;
  width: 100%;
  margin-top: 1rem;
  padding: 0.75rem 1rem;
  background-color: #16A34A;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.gx-send-code-button:hover {
  background-color: #15803D;
}

.gx-send-code-button:disabled {
  background-color: #cbd5e1;
  cursor: not-allowed;
}

/* Animations */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  20%, 60% { transform: translateX(-5px); }
  40%, 80% { transform: translateX(5px); }
}

/* Country dropdown */
.gx-country-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 10;
  width: 100%;
  max-height: 20rem;
  display: flex;
  flex-direction: column;
  background-color: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  margin-top: 0.25rem;
  overflow: hidden;
}

.gx-country-search-container {
  padding: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f8fafc;
  position: sticky;
  top: 0;
  z-index: 1;
}

.gx-country-search-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  outline: none;
}

.gx-country-search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.gx-country-list {
  overflow-y: auto;
  max-height: 15rem;
}

.gx-country-dropdown-item {
  display: flex;
  align-items: center;
  width: 100%;
  text-align: left;
  padding: 0.5rem 0.75rem;
  border: none;
  background: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.gx-country-dropdown-item:hover, 
.gx-country-dropdown-item:focus {
  background-color: #f8fafc;
  outline: none;
}

.gx-country-dropdown-item.selected {
  background-color: #eff6ff;
}

.gx-country-name {
  font-size: 0.875rem;
  color: #1e293b;
  margin-left: 0.5rem;
}

.gx-country-empty {
  padding: 1rem;
  text-align: center;
  color: #64748b;
  font-size: 0.875rem;
}

/* Message/error display */
.gx-message {
  margin-top: 0.5rem;
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

.gx-message.error {
  color: #b91c1c;
  background-color: #fee2e2;
}

.gx-message.success {
  color: #047857;
  background-color: #d1fae5;
}

.gx-message.info {
  color: #1e40af;
  background-color: #dbeafe;
}

/* Message action button for country switching suggestion */
.gx-message-action-button {
  margin-left: 8px;
  padding: 4px 8px;
  background-color: white;
  border: 1px solid currentColor;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.gx-message.info .gx-message-action-button {
  color: #1e40af;
}

.gx-message.info .gx-message-action-button:hover {
  background-color: #1e40af;
  color: white;
}

.gx-message.error .gx-message-action-button {
  color: #b91c1c;
}

.gx-message.error .gx-message-action-button:hover {
  background-color: #b91c1c;
  color: white;
}

.gx-message.success .gx-message-action-button {
  color: #047857;
}

.gx-message.success .gx-message-action-button:hover {
  background-color: #047857;
  color: white;
}