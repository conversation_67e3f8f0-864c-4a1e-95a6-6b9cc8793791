import { memo } from 'react';
import { NavLink } from 'react-router-dom';
import noImg from '@/shared/assets/images/no-image.png';

export interface SearchSuggestionsProps {
  suggestions: any;
  onSelect: () => void;
}

/**
 * Displays search suggestions dropdown with categories, products, and history
 */
export const SearchSuggestions = memo(({ suggestions, onSelect }: SearchSuggestionsProps) => {
  const hasResults = (
    (suggestions?.variants && suggestions.variants.length > 0) ||
    (suggestions?.categories && suggestions.categories.length > 0) ||
    (suggestions?.history && suggestions.history.length > 0)
  );

  // Helper to prevent event propagation
  const handleItemClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  if (!hasResults) {
    return null;
  }

  return (
    <div className="absolute bg-white w-full h-full box-content bottom-0 right-0 rounded-lg ">
      <div className="relative min-w-full min-h-full bg-white rounded-lg bottom-0"></div>
      <div
        className={`relative min-w-full min-h-0 max-h-[50vh] bg-white rounded-lg bottom-0 mt-2 overflow-auto proportional-nums lining-nums ${
          hasResults ? 'p-1' : ''
        }`}
      >
        {/* Search history items */}
        {suggestions?.history?.map((result, index) => (
          <li
            onClick={handleItemClick}
            key={`history-${index}`}
            className="p-2 hover:bg-gray-200"
          >
            <NavLink
              to={`/search-results?search=${result.text}`}
              onClick={onSelect}
            >
              <div className="font-bold">{result.text}</div>
            </NavLink>
          </li>
        ))}

        {/* Product suggestions */}
        {suggestions?.variants?.map((result, index) => (
          <li
            onClick={handleItemClick}
            key={`variant-${index}`}
            className="p-2 hover:bg-gray-200"
          >
            <NavLink
              to={`/catalog/${result.categorySlug}/${result.slug}`}
              onClick={onSelect}
            >
              <div className="flex items-center gap-3">
                <img
                  src={result.files?.small || noImg}
                  alt=""
                  className="w-10 h-10 rounded-md object-contain"
                />
                <div>
                  <div>
                    {result.groupName} {result.name}
                  </div>
                  <div className="text-colDarkGray text-xs">
                    {result.article}
                  </div>
                </div>
              </div>
            </NavLink>
          </li>
        ))}

        {/* Category suggestions */}
        {suggestions?.categories?.map((result, index) => (
          <li
            onClick={handleItemClick}
            key={`category-${index}`}
            className="p-2 hover:bg-gray-200"
          >
            <NavLink to={`/catalog/${result.slug}`} onClick={onSelect}>
              <div className="flex items-center gap-3">
                <div>
                  <div className="text-xs text-colDarkGray">
                    {result.chain.slice(0, -1).map((cat) => `${cat.name}/`)}
                    <div className="text-sm text-colBlack">
                      {result.name}
                    </div>
                  </div>
                  <div className="text-colDarkGray text-xs">
                    Категория
                  </div>
                </div>
              </div>
            </NavLink>
          </li>
        ))}
      </div>
    </div>
  );
});

SearchSuggestions.displayName = 'SearchSuggestions';
