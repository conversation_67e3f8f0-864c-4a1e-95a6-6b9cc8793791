// src/components/ProductPage/Mobile/MobileProductInfo/MobileReviews.tsx
import React from 'react';
import { NavLink } from 'react-router-dom';
import { useSelector } from 'react-redux';

import { Review, ReviewItemData } from '@/entities/review'; // Assuming ReviewItemData is exported or define it here
import { RatingStars } from '@/entities/review/ui/RatingStars';
import { useModal } from '@/features/modals/model/context';

// Define a basic Product type, similar to other components
interface Product {
  id: number | string;
  // Add other necessary product properties that might be used by the 'review' modal
  name?: string; // Example property
  [key: string]: any; // Allow other properties
}

interface ReviewsData {
  rating?: number | string; // Can be a number or formatted string like "4.5"
  total_count_text?: string;
  list?: ReviewItemData[]; // Array of review items
}

interface MobileReviewsProps {
  reviews?: ReviewsData | null;
  product?: Product | null; // Product can be null or undefined
}

// Define the shape of the Redux state for user authentication
interface RootState {
  user: {
    isAuthenticated?: boolean;
    // other user state properties
  };
  // other top-level state slices
}

const MobileReviews: React.FC<MobileReviewsProps> = ({ reviews, product }) => {
  const { showModal } = useModal();
  const isAuthenticated = useSelector((state: RootState) => state?.user?.isAuthenticated);

  return (
    <>
      <h3 className="text-2xl mt-5 font-semibold" id="reviews">
        Отзывы
      </h3>
      {reviews && (reviews.rating || reviews.total_count_text) && (
        <div className="flex justify-between items-center my-5"> {/* Added items-center */}
          <div className="flex items-center ">
            {reviews.rating && <div className="text-2xl font-semibold mr-2"> {reviews.rating}</div>}
            {typeof reviews.rating === 'number' && (
                <div className="flex items-center mr-2">
                    <RatingStars totalStars={5} initialRating={reviews.rating} />
                </div>
            )}
            {reviews.total_count_text && <div className="text-lg text-colDarkGray"> {reviews.total_count_text}</div>}
          </div>
          <button
            onClick={() => {
              if (isAuthenticated) {
                // Ensure product is not null/undefined before passing
                if (product) {
                    showModal({ type: 'review', payload: { product } });
                } else {
                    // Handle case where product is not available, maybe show a generic review modal or log an error
                    console.warn('Product data is not available for review modal.');
                    // Optionally, still show a review modal if it can function without specific product context
                    // showModal({ type: 'review' }); 
                }
              } else {
                showModal({ type: 'auth' });
              }
            }}
            className="bg-colGreen font-semibold rounded text-white py-[10px] px-[30px] hover:bg-colGreenHover transition-colors duration-200" // Added hover effect
          >
            Оставить отзыв
          </button>
        </div>
      )}
      <div className="flex flex-col gap-5">
        {reviews?.list?.map((review, i) => {
          // Assuming Review component expects a prop named 'review' of type ReviewItemData
          return <Review key={review.id || i} review={review} />; // Use review.id if available, otherwise index
        })}
      </div>
      {reviews?.list && reviews.list.length > 0 && (
        <NavLink to="reviews" state={{ reviews: reviews, product: product }} className="inline-block"> {/* Added inline-block */}
          <div className="text-colGreen font-semibold underline underline-offset-8 cursor-pointer mt-5 hover:text-colGreenHover transition-colors duration-200"> {/* Added hover effect */}
            Читать все отзывы
          </div>
        </NavLink>
      )}
    </>
  );
};

export default MobileReviews;
