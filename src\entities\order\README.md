# Entity: Order

## Purpose

This entity represents a customer's **Order** within the e-commerce system. It defines the structure of an order, its items, status, and totals, and provides API endpoints for retrieving and managing order data.

## Key Data Points

*   Order identification (`order_number`)
*   Date of creation (`date`)
*   Current order status (`status`: name, colors)
*   List of items (`items`: which are `Product` entities with added `quantity`)
*   Order totals (`total`: amounts, quantity, discount)

## Structure

*   **`model/types.ts`:** Defines the core interfaces: `Order`, `OrderStatus`, `OrderProduct` (extends `Product`), `OrderTotals`. *(Currently in `src/entities/order/types.ts`)*
*   **`api/orderApi.ts`:** Defines RTK Query endpoints using `api.injectEndpoints`:
    *   `getUserOrders`: Query to fetch the list of orders for the currently authenticated user. Uses `providesTags`.
    *   `getOrdersFilters`: Query to fetch available filters for the orders list (e.g., statuses). Uses `providesTags`.
    *   `sendOrder`: Mutation to submit a new order (used by the Checkout process). Invalidates the order list tag.
    *   `cancelOrder`: Mutation to cancel an existing order. Invalidates order list and filter tags.
    *   `repeatOrder`: Mutation to create a new cart based on a previous order. Invalidates order list, filter, and cart tags.
    *   `createPDFOrder`: Mutation to generate and download a PDF representation of an order.
    *   `sendFeedback`: Mutation for submitting order-related feedback (potentially misplaced, might belong in a `feedback` feature/entity).
    *   `getCitiesAndRegions`: Query to fetch location data (potentially misplaced, might belong in a `location` entity or `shared`).
*   **`api/types.ts`:** Defines request/response types specifically for the order API endpoints (e.g., `GetUserOrdersResponse`).
*   **`index.ts`:** Public API for the entity, exporting types and API hooks.
*   **`ui/`:** (Currently empty) Could potentially hold simple UI components for displaying order status badges or order summaries if needed across multiple features/widgets.

## Usage

*   **Fetching Orders:** The `pages/Profile/MyOrders.tsx` page uses `useGetUserOrdersQuery` to display the user's order history.
*   **Placing Orders:** The checkout process (`pages/Checkout/`) uses the `useSendOrderMutation` to submit the order.
*   **Order Actions:** Components within the order list (`src/components/Profile/MyOrders/`) use mutations like `useCancelOrderMutation`, `useRepeatOrderMutation`, etc., often encapsulated within custom hooks like `useOrderActions`.
*   **Type Usage:** The `Order` type and related interfaces are used when displaying order details.

## Related Features/Entities

*   `features/cart`: The cart contents are used to create an order.
*   `entities/product`: Orders contain lists of products (`OrderProduct`).
*   `features/checkout`: The process that culminates in calling `sendOrder`.

## Migration Notes

*   The `getCitiesAndRegions` query seems related to location/delivery rather than the core Order entity and might be better placed elsewhere (e.g., a `location` entity or within a `delivery` feature).
*   The `sendFeedback` mutation might also be better suited for a dedicated `feedback` feature or entity.
*   Ensure UI components displaying order information (currently in `src/components/Profile/MyOrders/`) are refactored into appropriate `widgets` or potentially simple `entities/order/ui` components if reusable.