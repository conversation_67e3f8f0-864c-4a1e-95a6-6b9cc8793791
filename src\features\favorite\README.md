# Feature: Favorite

## Purpose

This feature allows users to mark products as favorites, view their list of favorite items, and manage this list.

## Key Functionality

*   **Toggling Favorites:** Provides logic (`useFavorites`) and UI (`FavoriteButton`) to add or remove products from the favorites list. Handles both anonymous and logged-in users.
*   **State Management:**
    *   Maintains the list of favorite products in Redux (`model/favoriteSlice.ts`).
    *   Uses `sessionStorage` for persistence for anonymous users.
    *   Synchronizes with the server via RTK Query for logged-in users.
*   **Data Transformation:** Includes logic (`parseCategories` within `favoriteSlice.ts`) to derive a list of categories present in the favorites list for filtering purposes.
*   **Display:** Integrates with the Favorites page (`/favorites`) to display the list of favorited items.

## Components (`ui/`)

*   **Controls:** `FavoriteButton.tsx` - The heart icon button used on product cards and pages.

## State & Logic (`model/`)

*   **`favoriteSlice.ts`:** Redux slice managing the `FavoriteState` (list of favorite products and derived categories). Includes reducers for setting, adding, and removing favorites. Handles persistence for anonymous users.
*   **Hooks:**
    *   `useFavorites.ts`: Encapsulates the logic for checking if an item is favorited and handling the add/remove action, including optimistic UI updates and server synchronization.
*   **Types (`model/types.ts`):** Defines `FavoriteState`.

## API Integration (`api/`)

*   **`favoritesEndpoints.ts`:** Defines RTK Query endpoints:
    *   `getFavorites`: Fetches the user's favorite products from the server.
    *   `sendFavorites`: Adds one or more products to the server-side favorites list.
    *   `removeFromFavorites`: Removes one or more products from the server-side favorites list.

## Usage

*   `FavoriteButton` is used within product display components like `ProductCard` (`widgets/product-card`) and on the product detail page (`pages/ProductPage`).
*   The favorites list state is consumed by the Favorites page (`pages/Favorites/Favorites.tsx`) to display the items and category filters.
*   The count of favorite items is often displayed in the main header (`widgets/header`).