# Entity: Organization

## Purpose

This entity represents a **Legal Entity** or **Organization** associated with a user account, typically used for business-to-business (B2B) purchasing or separate billing profiles.

## Key Data Points (`model/Organization.ts`)

*   `id` (Implied from API, used for update/delete)
*   `inn`: Taxpayer Identification Number (Primary identifier).
*   `kpp`: Tax Registration Reason Code.
*   `name`: Full organization name.
*   `u_address` / `yurAddress`: Legal address.
*   `f_address` / `faqAddress`: Actual/Mailing address.
*   `ogrn`: Primary State Registration Number.
*   `bank_account`: Bank account number.
*   `bank_bik`: Bank Identification Code.
*   `correspondent_account`: Correspondent bank account number.
*   `bank_name`: Name of the bank.

## Structure

*   **`model/Organization.ts`:** Defines the `Organization` interface.
*   **`model/organizationsSlice.ts`:** Redux Toolkit slice for managing organization state (currently seems focused on client-side updates, likely superseded by RTK Query for fetching). *(Note: Might be legacy or partially used)*.
*   **`model/OrganizationsState.ts`:** Defines the shape of the Redux state for this slice.
*   **`api/organizationEndpoints.ts`:** Defines RTK Query endpoints using `api.injectEndpoints`:
    *   `getOrganizations`: Query to fetch the list of organizations associated with the current user. Uses `providesTags`.
    *   `addOrganization`: Mutation to add a new organization. Invalidates the list tag.
    *   `deleteOrganization`: Mutation to delete an organization by ID. Invalidates list and specific ID tags.
    *   `editOrganization`: Mutation to update an organization's details. Invalidates list and specific ID tags.
    *   `getOrgSuggestions`: Mutation (used like a query) to fetch organization suggestions based on an INN or name query (likely from an external service like DaData).
*   **`ui/`:** (Currently empty) Could potentially hold a component to display basic organization info if needed elsewhere.
*   **`index.ts`:** Public API (currently missing, should be added).

## Usage

*   **Profile Management:** The "My Organizations" section of the user profile (`src/components/Profile/Organizations/Organizations.jsx` - *legacy location*) uses `useGetOrganizationsQuery` to list organizations and `useAddOrganizationMutation`, `useDeleteOrganizationMutation`, `useEditOrganizationMutation` for management via modals (`AddOrganizationModal`, `UpdateOrganizationModal`, `DeleteOrganizationModal` - located in `features/modals`).
*   **Checkout:** The checkout form (`src/components/Checkout/`) allows selecting an organization as the buyer/payer, potentially influencing payment methods or document generation.
*   **Organization Suggestions:** The `AddOrganizationModal` uses `useGetOrgSuggestionsMutation` to help users find and pre-fill organization details.

## Related Features/Entities

*   `entities/user`: Organizations are linked to user accounts.
*   `features/checkout`: Organization selection impacts the checkout process.
*   `features/modals`: Modals for adding, editing, and deleting organizations are managed here.

## Migration Notes

*   The UI components for displaying and managing organizations (`Organizations.jsx`, `OrgCard.jsx`) are in `src/components/Profile/`. These should be refactored:
    *   `Organizations.jsx` likely becomes a `pages/Profile/OrganizationsPage.tsx`.
    *   `OrgCard.jsx` could become a widget (`widgets/org-card/`) or potentially a simple entity UI component (`entities/organization/ui/OrgCard.tsx`).
*   The Redux slice (`organizationsSlice.ts`) might be redundant if all state is managed via RTK Query caching. Evaluate if it's still needed for any client-side-only state.
*   Add an `index.ts` file to export the entity's public API (types, hooks).
