# Feature: Comparison

## Purpose

This feature enables users to select multiple products and view them side-by-side in a detailed comparison view, highlighting differences in attributes.

## Key Functionality

*   **Adding/Removing Items:** Provides logic (`useComparison`) and UI (`ComparisonButton`) for adding or removing products from the comparison list. Supports both anonymous and logged-in users.
*   **State Management:**
    *   Manages the list of products currently in comparison using Redux (`model/comparisonSlice.ts`).
    *   Persists the comparison list to `sessionStorage` for anonymous users.
    *   Synchronizes the list with the server using RTK Query for logged-in users.
*   **Data Transformation:** Includes logic (`parseCategories` within `comparisonSlice.ts`) to group comparison items by category for filtering on the comparison page.
*   **Display:** Powers the Comparison page (`/comparison`) where selected products are displayed in a detailed table (`src/components/Comparison/ComparisonDetail.tsx`).

## Components (`ui/`)

*   **Controls:** `ComparisonButton.tsx` - The comparison icon button typically found on product cards and potentially product pages.

## State & Logic (`model/`)

*   **`comparisonSlice.ts`:** Redux slice managing the `ComparisonState` (list of products in comparison and derived categories). Includes reducers for setting, adding, and removing items. Handles persistence for anonymous users.
*   **Hooks:**
    *   `useComparison.ts`: Encapsulates the logic for checking if a product is in the comparison list and handling the add/remove action, including optimistic UI updates and server synchronization.
*   **Types (`model/types.ts`):** Defines `ComparisonState`.

## API Integration (`api/`)

*   **`comparisonApi.ts`:** Defines RTK Query endpoints:
    *   `getComparison`: Fetches the user's comparison list from the server.
    *   `sendComparison`: Adds one or more products to the server-side comparison list.
    *   `removeFromComparison`: Removes one or more products from the server-side comparison list.

## Usage

*   `ComparisonButton` is integrated into product display components like `ProductCard` (`widgets/product-card`).
*   The comparison state is primarily consumed by the Comparison page (`pages/Comparison/Comparison.tsx`) to fetch and display the comparison table.
*   The count of items in comparison may be displayed in the main application header (`widgets/header`).