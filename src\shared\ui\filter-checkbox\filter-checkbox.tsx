import { Check } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Label } from '../label';

interface FilterCheckboxProps {
  children: React.ReactNode;
  id: number | string;
  is_selected: boolean;
  onChange: (isChecked: boolean) => void;
  className?: string;
  disabled?: boolean;
  is_show_checkbox?: boolean;
}

export const FilterCheckbox = ({
  children,
  id,
  className,
  is_selected,
  onChange,
  disabled,
  is_show_checkbox = true
}: FilterCheckboxProps) => {
  const [bool, setBool] = useState(is_selected);

  const handleChange = (bool: boolean) => {
    onChange(bool);
    setBool(bool);
  };

  useEffect(() => {
    setBool(is_selected);
  }, [is_selected]);

  return (
    <div className={`flex items-center space-x-2`}>
      <Label
        htmlFor={id.toString()}
        className={`text-[#222] flex items-center gap-[6px] flex-row cursor-pointer ${className} ${disabled ? 'text-[#AAAFAE]' : ''}`}
      >
        {is_show_checkbox ? (
          <span
            className={`bg-[#E6F0EE] flex justify-center items-center text-transparent has-[:checked]:text-[white]  border-none rounded-[4px] w-[20px] h-[20px] shadow-[inset_0px_1px_4px_0px_#CBD4D2] has-[:checked]:bg-[#15765B] has-[:checked]:border-transparent has-[:checked]:shadow-none ${is_show_checkbox ? '' : 'hidden'}`}
          >
            <Check className='h-4 w-4' />
            <input
              type='checkbox'
              id={id.toString()}
              className='absolute opacity-[0] hidden'
              onChange={(e) => handleChange(e.target.checked)}
              disabled={disabled}
              checked={bool}
            />
          </span>
        ) : (
          <input
            type='checkbox'
            id={id.toString()}
            className='absolute opacity-[0] hidden'
            onChange={(e) => handleChange(e.target.checked)}
            disabled={disabled}
            checked={bool}
          />
        )}
        {children}
      </Label>
    </div>
  );
};
