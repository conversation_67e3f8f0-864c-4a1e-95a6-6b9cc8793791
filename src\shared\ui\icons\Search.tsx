import React from 'react';

interface SvgIconProps {
  size?: number;
  color?: string;
  stroke?: number;
}

export const Search: React.FC<SvgIconProps> = ({
  size = 24,
  color = 'currentColor',
  stroke = 1.5
}) => {
  return (
    <svg
      width={size + 'px'}
      height={size + 'px'}
      viewBox='0 0 24 24'
      fill='none'
      color={color}
      xmlns='http://www.w3.org/2000/svg'
    >
      <circle cx='11.5' cy='11.5' r='9.5' stroke='currentColor' strokeWidth={stroke} />
      <path d='M18.5 18.5L22 22' stroke='currentColor' strokeWidth={stroke} strokeLinecap='round' />
    </svg>
  );
};
