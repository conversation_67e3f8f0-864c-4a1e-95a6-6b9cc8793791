import { api } from '@/shared/api/api';
import type {
  ReviewResponse,
  ReviewSetRequest,
  ReviewSetResponse,
  ReviewSelfListResponse,
  ReviewUpdateRequest,
  ReviewDeleteRequest,
  ReviewDetail,
  ReviewDetailResponse,
} from './types';
import { RootState } from '@/store';

export const reviewApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getReviews: builder.query<ReviewResponse, number>({
      query: (variant_id) => ({
        url: '/api/ProdComments/getItems',
        method: 'GET',
        params: { variant_id },
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.comments.map(({ id }) => ({ type: 'Review' as const, id })),
              { type: 'Review', id: 'LIST' },
            ]
          : [{ type: 'Review', id: 'LIST' }],
    }),

    setReview: builder.mutation<ReviewSetResponse, ReviewSetRequest>({
      query: (data) => ({
        url: '/api/ProdComments/setItem',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'Review', id: 'LIST' }, { type: 'UserReviews', id: 'LIST' }],
    }),

    getSelfReviews: builder.query<ReviewSelfListResponse, void>({
      query: () => ({
        url: '/api/ProdComments/getSelfItems',
        method: 'GET',
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.comments.map(({ id }) => ({ type: 'UserReviews' as const, id })),
              { type: 'UserReviews', id: 'LIST' },
            ]
          : [{ type: 'UserReviews', id: 'LIST' }],
    }),

    getSelfReview: builder.query<ReviewDetailResponse, ReviewDeleteRequest>({
      query: ({ variant_id, comment_id }: ReviewDeleteRequest) => ({
        url: '/api/ProdComments/getSelfItem',
        method: 'GET',
        params: { variant_id, comment_id },
      }),
      providesTags: (result, error, { comment_id }) => [{ type: 'UserReviews', id: comment_id }],
    }),

    updateSelfReview: builder.mutation<ReviewSetResponse, ReviewUpdateRequest>({
      query: (data) => ({
        url: '/api/ProdComments/updSelfItem',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { comment_id }) => [
        { type: 'Review', id: comment_id },
        { type: 'UserReviews', id: comment_id },
      ],
    }),

    deleteSelfReview: builder.mutation<ReviewSetResponse, ReviewDeleteRequest>({
      query: (data) => ({
        url: '/api/ProdComments/delSelfItem',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { comment_id }) => [
        { type: 'Review', id: comment_id },
        { type: 'UserReviews', id: comment_id },
      ],
    }),
  }),
});

// Export hooks for review endpoints
export const {
  useGetReviewsQuery,
  useSetReviewMutation,
  useGetSelfReviewsQuery,
  useGetSelfReviewQuery,
  useUpdateSelfReviewMutation,
  useDeleteSelfReviewMutation,
} = reviewApi;
