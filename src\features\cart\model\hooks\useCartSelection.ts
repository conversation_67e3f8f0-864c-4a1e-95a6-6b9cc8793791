// src/features/cart/model/hooks/useCartSelection.ts
import { useEffect, useState } from 'react';

import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';

import { getTokenFromCookies } from '@/entities/user';
import { useSendCartMutation } from '@/features/cart';

import { selectItem, unselectItem } from '../cartSlice';

import type { AppDispatch, RootState } from '@/app/providers/store';
import type { CartProduct, SendCartPayload } from '@/features/cart';

export const useCartSelection = () => {
  const token = getTokenFromCookies();
  const dispatch: AppDispatch = useDispatch();
  const [sendCart] = useSendCartMutation();
  const [isUpdating, setIsUpdating] = useState(false);

  const cart = useSelector((state: RootState) => state.cart);
  const selectedItems = cart?.cart?.filter(
    (item) => item.selected === true || item.selected.toString() === '1'
  );

  // Add logging when selected items change
  useEffect(() => {}, [selectedItems, cart?.cart]);

  // Handle selection for single item
  const handleItemSelection = async (product: CartProduct, selected: boolean) => {
    if (isUpdating) {
      console.log('[Cart Selection] Skipping selection change due to ongoing update');
      return;
    }

    setIsUpdating(true);
    console.log('[Cart Selection] Processing selection change:', {
      productId: product.id,
      productName: product.name,
      currentState: product.selected ? 'selected' : 'not selected',
      newState: selected ? 'selected' : 'not selected'
    });

    try {
      // Optimistic update
      console.log(
        '[Cart Selection] Dispatching Redux action:',
        selected ? 'selectItem' : 'unselectItem'
      );
      dispatch(selected ? selectItem(product) : unselectItem(product));

      // Server sync for logged in users
      if (token) {
        console.log('[Cart Selection] Sending selection to server for product:', product);
        const result = await sendCart({
          id: product.id,
          quantity: product.quantity,
          selected: selected ? true : false
        });

        if ('error' in result) {
          // Revert on error
          console.error('[Cart Selection] Server error, reverting selection:', result.error);
          dispatch(selected ? unselectItem(product) : selectItem(product));
          toast.error('Failed to update selection');
        } else {
          console.log('[Cart Selection] Server update successful for product:', product.id);
        }
      } else {
        console.log('[Cart Selection] No token, skipping server update');
      }
    } catch (error) {
      // Revert on error
      console.error('[Cart Selection] Error during selection update:', error);
      dispatch(selected ? unselectItem(product) : selectItem(product));
      toast.error('Failed to update selection');
    } finally {
      setIsUpdating(false);
      console.log('[Cart Selection] Selection update complete for product:', product.id);
    }
  };

  // Handle batch selection for multiple items
  const handleBatchSelection = async (items: CartProduct[], selected: boolean) => {
    if (isUpdating) {
      console.log('[Cart Selection] Skipping batch selection due to ongoing update');
      return;
    }

    setIsUpdating(true);
    console.log('[Cart Selection] Processing batch selection:', {
      itemCount: items.length,
      itemIds: items.map((item) => item.id),
      newState: selected ? 'selected' : 'not selected'
    });

    try {
      // Optimistic update
      console.log('[Cart Selection] Dispatching batch Redux actions');
      items.forEach((item) => dispatch(selected ? selectItem(item) : unselectItem(item)));

      // Server sync for logged in users
      if (token) {
        console.log('[Cart Selection] Sending batch selection to server');
        const payload: SendCartPayload[] = items.map((item) => ({
          id: item.id,
          quantity: item.quantity,
          selected: selected ? true : false
        }));

        const result = await sendCart({ items: payload });

        if ('error' in result) {
          // Revert on error
          console.error('[Cart Selection] Server error on batch update, reverting:', result.error);
          items.forEach((item) => dispatch(selected ? unselectItem(item) : selectItem(item)));
          toast.error('Failed to update selection');
        } else {
          console.log('[Cart Selection] Batch server update successful');
        }
      } else {
        console.log('[Cart Selection] No token, skipping batch server update');
      }
    } catch (error) {
      // Revert on error
      console.error('[Cart Selection] Error during batch selection:', error);
      items.forEach((item) => dispatch(selected ? unselectItem(item) : selectItem(item)));
      toast.error('Failed to update selection');
    } finally {
      setIsUpdating(false);
      console.log('[Cart Selection] Batch selection update complete');
    }
  };

  // Handle select/deselect all
  const handleSelectAll = async (selected: boolean) => {
    console.log('[Cart Selection] Select all toggled:', {
      newState: selected ? 'selected' : 'not selected'
    });

    const itemsToUpdate = selected
      ? cart?.cart?.filter((item) => !item.selected) || []
      : selectedItems;

    console.log('[Cart Selection] Items to update in select all:', {
      count: itemsToUpdate.length,
      ids: itemsToUpdate.map((item) => item.id)
    });

    await handleBatchSelection(itemsToUpdate, selected);
  };

  const isAllSelected = cart?.cart?.length > 0 && cart?.cart?.length === selectedItems?.length;

  return {
    selectedItems,
    isUpdating,
    isAllSelected,
    handleItemSelection,
    handleBatchSelection,
    handleSelectAll
  };
};
