import { useState, useRef, useEffect, useCallback } from 'react';
import { useGetVariantsMutation } from '@/entities/product/api/productApi';
import { scrollToTop } from '@/shared/lib/scrollToTop';

/**
 * Custom hook for managing catalog products, pagination, and sorting
 * 
 * @param {Function} getSendFiltersObject - Function to get filter parameters
 * @returns {Object} Object containing product state and operations
 */
export const useCatalogProducts = (getSendFiltersObject) => {
  // Product state
  const [products, setProducts] = useState([]);
  const [productsLoading, setProductsLoading] = useState(false);
  const [productsError, setProductsError] = useState(null);
  
  // Pagination state
  const [page, setPage] = useState(1);
  
  // Sorting state
  const [sort, setSort] = useState({
    sortBy: 'popularity',
    sortOrder: 'desc',
  });
  
  // Reference to previous sort state
  const sortPrevious = useRef(sort);
  
  // Get products API mutation
  const [getVariantsMutation] = useGetVariantsMutation();
  
  // Reference to cancel in-flight requests
  const getProductsRef = useRef(null);
  
  /**
   * Fetches products based on filters, sorting, and pagination
   * 
   * @param {Object} sendObject - Parameters for API request
   */
  const getProducts = useCallback(async (sendObject) => {
    // Cancel any in-flight request
    if (getProductsRef.current) {
      getProductsRef.current.abort();
    }
    
    // Create new abort controller
    const abortController = new AbortController();
    getProductsRef.current = abortController;
    
    // Reset error state
    setProductsError(null);
    setProductsLoading(true);
    
    try {
      // Add signal to request
      const requestObject = {
        ...sendObject,
        signal: abortController.signal,
      };
      
      // Call API
      const productsResponse = await getVariantsMutation(requestObject);
      
      // Process response if not aborted
      if (productsResponse.data?.success === 'ok') {
        setProducts(productsResponse.data);
      } else if (productsResponse.error) {
        console.error('Failed to fetch products:', productsResponse.error);
        setProductsError(productsResponse.error.message || 'Failed to load products');
      }
    } catch (error) {
      // Only set error if not aborted
      if (error.name !== 'AbortError') {
        console.error('Error fetching products:', error);
        setProductsError(error.message || 'Failed to load products');
      }
    } finally {
      setProductsLoading(false);
      
      // Clear abort controller reference if it matches
      if (getProductsRef.current === abortController) {
        getProductsRef.current = null;
      }
    }
  }, [getVariantsMutation]);
  
  /**
   * Handles pagination changes
   * 
   * @param {Event} event - Event object (unused)
   * @param {number} newPage - New page number
   */
  const handlePagination = useCallback((event, newPage) => {
    setPage(newPage);
    
    getProducts({
      ...getSendFiltersObject(),
      page: newPage,
      limit: 20,
      orderBy: sort.sortBy,
      sortOrder: sort.sortOrder,
    });
    
    scrollToTop();
  }, [getSendFiltersObject, sort, getProducts]);
  
  /**
   * Effect to fetch products when sort changes
   */
  useEffect(() => {
    if (JSON.stringify(sortPrevious.current) !== JSON.stringify(sort)) {
      getProducts({
        ...getSendFiltersObject(),
        page: page,
        limit: 20,
        orderBy: sort.sortBy,
        sortOrder: sort.sortOrder,
      });
      
      sortPrevious.current = sort;
    }
  }, [sort, page, getSendFiltersObject, getProducts]);
  
  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (getProductsRef.current) {
        getProductsRef.current.abort();
        getProductsRef.current = null;
      }
    };
  }, []);
  
  return {
    products,
    productsLoading,
    productsError,
    page,
    setPage,
    sort,
    setSort,
    getProducts,
    handlePagination,
  };
};
