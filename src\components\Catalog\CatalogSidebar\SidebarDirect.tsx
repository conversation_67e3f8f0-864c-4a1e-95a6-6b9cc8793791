import React, { useEffect, useState } from 'react';
import { useGetCategoryTreeQuery } from '@/entities/category';
import { useParams } from 'react-router-dom';

import SidebarCategoryTree from './SidebarCategoryTree';
import SidebarFilters from './SidebarFilters/SidebarFilters';
import PriceFilterDirect from './SidebarFilters/PriceFilterDirect';
import SidebarFiltersSkeleton from './SidebarFiltersSkeleton';

const CatalogSidebarDirect = ({ 
  setFiltersModalOpen, 
  filters, 
  setFilters, 
  trigger, 
  setTrigger, 
  resetFilters, 
  filtersIsLoading, 
  filtersBlock
}) => {
  const { categoryId } = useParams();
  const [expanded, setExpanded] = useState({});

  const {
    data: categoryTree,
    isSuccess: categoryTreeIsSuccess,
    isError: categoryTreeIsError,
    isLoading: categoryTreeIsLoading,
  } = useGetCategoryTreeQuery(categoryId);

  useEffect(() => {
    // To expand the parent category to the current category
    if (categoryTree?.category_chain && categoryTree?.category_chain.length > 0) {
      const expandState = {};
      categoryTree?.category_chain.forEach(cat => {
        expandState[cat.id] = true;
      });
      setExpanded(expandState);
    }
  }, [categoryTree]);

  return (
    <div>
      <div className=" border-b border-colOrange/20 pb-4 mb-4">
        <p className="mb-4 font-semibold text-lg text-colBlack">Категории</p>

        {categoryTreeIsSuccess && categoryTree ? (
          <SidebarCategoryTree
            tree={categoryTree.tree}
            category_chain={categoryTree.category_chain}
            expanded={expanded}
            setExpanded={setExpanded}
          />
        ) : null}

        {categoryTreeIsLoading ? (
          <>
            <SidebarCategoryTree
              tree={[]}
              category_chain={[]}
              expanded={expanded}
              setExpanded={setExpanded}
              isLoading
            />
          </>
        ) : null}
      </div>

      <div>
        {/* Actual filters */}
        {filtersIsLoading ? (
          <SidebarFiltersSkeleton />
        ) : (
          <div className={`${filtersBlock ? 'opacity-60 cursor-not-allowed ' : ''}`}>
            <div className="relative mb-4">
              {/* Using the direct price filter implementation instead */}
              <PriceFilterDirect
                filters={filters}
                setFilters={setFilters}
                trigger={trigger}
                setTrigger={setTrigger}
              />
            </div>

            {/* Standard filter implementation for other filters */}
            <SidebarFilters
              filters={filters}
              setFilters={setFilters}
              trigger={trigger}
            />

            <button
              onClick={resetFilters}
              type="button"
              className="bg-colGreen text-white rounded-md p-2 mt-4"
            >
              <span className="text-center font-medium text-sm w-full block">
                Сбросить все
              </span>
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CatalogSidebarDirect;
