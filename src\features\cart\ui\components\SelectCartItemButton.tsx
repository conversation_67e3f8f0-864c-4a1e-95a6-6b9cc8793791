// src/features/cart/ui/components/SelectCartItemButton.tsx

import type React from 'react';

import { useCartSelection } from '@/features/cart/model/hooks/useCartSelection';

import type { CartProduct } from '@/features/cart';

interface SelectCartItemButtonProps {
  product: CartProduct;
  children: (props: {
    isLoading: boolean;
    isSelected: boolean;
    handleSelectClick: (e: React.MouseEvent<HTMLButtonElement>) => void;
  }) => React.ReactNode;
}

export const SelectCartItemButton: React.FC<SelectCartItemButtonProps> = ({
  product,
  children,
}) => {
  const { handleItemSelection, isUpdating } = useCartSelection();
  
  // Standardized selection check
  const isSelected = product.selected === true || product.selected.toString() === '1';

  const handleSelectClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();
    
    handleItemSelection(product, !isSelected);
  };

  return children({ 
    isLoading: isUpdating, 
    isSelected, 
    handleSelectClick 
  });
};
