import React, { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

import { Advantages } from '@/components/Home/Advantages';
import { Brands } from '@/components/Home/Brands';
import { Promotions } from '@/components/Home/Promotions';
import { useGetCategoryTreeQuery } from '@/entities/category';
import { useGetFiltersMutation } from '@/entities/filter';
import { useGetVariantsMutation } from '@/entities/product/api/productApi';
import { AllFiltersModal } from '@/features/modals';
import { scrollToTop } from '@/shared/lib/scrollToTop';
import { Breadcrumbs } from '@/widgets/breadcrumbs';

import CatalogContent from './CatalogContent/CatalogContent';
import SidebarDirect from './CatalogSidebar/SidebarDirect';

import type { FiltersState } from '@/types/Filters/FiltersState';

// This is a test implementation with direct filter updates
const CatalogDirect = () => {
  const [filtersModalOpen, setFiltersModalOpen] = useState(false);
  const { categoryId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  
  const {
    data: categoryTree,
    isSuccess: categoryTreeIsSuccess,
    isError: categoryTreeIsError,
    isLoading: categoryTreeIsLoading,
  } = useGetCategoryTreeQuery(categoryId);

  // Filters logic
  const [filters, setFilters] = useState<FiltersState>({
    basics: {
      price: {
        min: 0,
        max: 0,
      },
      tags: [],
      brands: [],
      rating: [],
    },
    dynamics: [],
    more: [],
    category_chain: [],
  });
  
  const [filtersLoading, setFiltersLoading] = useState(false);
  const [filtersBlock, setFiltersBlock] = useState(false);
  const [trigger, setTrigger] = useState('');
  const previousFilters = useRef({});
  const isFirstLoad = useRef(true);

  // API mutations
  const [getFilters] = useGetFiltersMutation();
  const [getVariants, { isLoading: getVariantsIsLoading }] = useGetVariantsMutation();

  // Products logic
  const [products, setProducts] = useState([]);
  const [productsLoading, setProductsLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [sort, setSort] = useState({
    sortBy: 'popularity',
    sortOrder: 'desc',
  });
  const sortPrevious = useRef(sort);

  // Main functions to get data
  const getNewFiltersList = async (sendObject, triggerType) => {
    if (
      triggerType === 'categoryId' ||
      triggerType === 'tags' ||
      triggerType === 'brands'
    ) {
      setFiltersLoading(true);
    } else if (triggerType === 'filters') {
      setFiltersBlock(true);
    }

    try {
      const newFilters = await getFilters(sendObject);

      // Type guard to check if response has data
      if ('data' in newFilters) {
        const more = newFilters.data.more.map((obj) => ({
          ...obj,
          additional_filter: true,
        }));

        const newDynamics = newFilters.data.dynamics.concat(more);

        const newFiltersState = {
          ...filters,
          basics: newFilters.data.basics,
          dynamics: newDynamics,
        };

        // Update URL
        navigate(
          `?${buildQueryParams(getSendFiltersObject2(newFiltersState), sort, page)}`,
          { replace: true }
        );

        previousFilters.current = newFiltersState;
        setFilters(newFiltersState);
        setTrigger(triggerType);
      }
    } catch (error) {
      console.error('Failed to fetch filters:', error);
    } finally {
      // Reset loading states
      if (
        triggerType === 'categoryId' ||
        triggerType === 'tags' ||
        triggerType === 'brands'
      ) {
        setFiltersLoading(false);
      } else if (triggerType === 'filters') {
        setFiltersBlock(false);
      }
    }
  };

  const getProducts = async (sendObject) => {
    setProductsLoading(true);

    try {
      const products = await getVariants(sendObject);
      if (products.data?.success === 'ok') {
        setProducts(products.data);
      }
    } catch (error) {
      console.error('Failed to fetch products:', error);
    } finally {
      setProductsLoading(false);
    }
  };

  // Sync with URL parameters on initial load
  useEffect(() => {
    const queryParams = parseQueryParams(location.search);

    if (categoryId === 'tags' || categoryId === 'brands') return;

    if (isFirstLoad.current && queryParams) {
      getNewFiltersList(
        { ...queryParams.filtersObject, category_id: categoryId },
        'categoryId'
      );
      
      setPage(queryParams.page || 1);
      
      getProducts({
        ...queryParams.filtersObject,
        orderBy: queryParams.sortObject.sortBy || 'popularity',
        sortOrder: queryParams.sortObject.sortOrder || 'desc',
        page: queryParams.page || 1,
        limit: 20,
        category_id: categoryId,
      });
      
      isFirstLoad.current = false;
    } else {
      getNewFiltersList(
        {
          category_id: categoryId,
          min_price: null,
          max_price: null,
          brands: [],
          tags: [],
          filters: {},
          last_changed: {},
        },
        'categoryId'
      );

      getProducts({
        page: 1,
        limit: 20,
        orderBy: sort.sortBy,
        sortOrder: sort.sortOrder,
        category_id: categoryId,
        min_price: null,
        max_price: null,
        brands: [],
        tags: [],
        filters: {},
        last_changed: {},
      });

      setPage(1);
    }

    scrollToTop();
  }, [categoryId]);

  // Handle filter changes
  useEffect(() => {
    // Only process changes when filters have actually changed
    if (JSON.stringify(filters) !== JSON.stringify(previousFilters.current)) {
      console.log("Filters changed, getting new data");
      getNewFiltersList(
        {
          ...getSendFiltersObject(),
        },
        'filters'
      );
      
      getProducts({
        ...getSendFiltersObject(),
        page: 1,
        limit: 20,
        orderBy: sort.sortBy || 'popularity',
        sortOrder: sort.sortOrder || 'desc',
      });
    }
  }, [filters]);

  // Handle sorting changes
  useEffect(() => {
    if (JSON.stringify(sortPrevious.current) !== JSON.stringify(sort)) {
      getProducts({
        ...getSendFiltersObject(),
        page: page,
        limit: 20,
        orderBy: sort.sortBy,
        sortOrder: sort.sortOrder,
      });
      
      sortPrevious.current = sort;
    }
  }, [sort]);

  // Handle pagination
  const handlePagination = (e, newPage) => {
    setPage(newPage);
    
    getProducts({
      ...getSendFiltersObject(),
      page: newPage,
      limit: 20,
      orderBy: sort.sortBy,
      sortOrder: sort.sortOrder,
    });
    
    scrollToTop();
  };

  // Handle filter reset
  const resetFilters = async () => {
    getNewFiltersList(
      {
        category_id:
          categoryId === 'tags' || categoryId === 'brands' ? null : categoryId,
        min_price: null,
        max_price: null,
        brands: [],
        tags: [],
        filters: {},
        last_changed: {},
      },
      'categoryId'
    );

    getProducts({
      page: page,
      limit: 20,
      orderBy: sort.sortBy,
      sortOrder: sort.sortOrder,
      category_id:
        categoryId === 'tags' || categoryId === 'brands' ? null : categoryId,
      min_price: null,
      max_price: null,
      brands: [],
      tags: [],
      filters: {},
      last_changed: {},
    });
    
    scrollToTop();
  };

  // Utility functions
  const getSendFiltersObject2 = (filters) => {
    const brands = filters?.basics?.brands?.reduce((acc, brand) => {
      if (brand.is_selected) {
        acc.push(brand.id);
      }
      return acc;
    }, []);

    const tags = filters?.basics?.tags?.reduce((acc, tag) => {
      if (tag.is_selected) {
        acc.push(tag.tag);
      }
      return acc;
    }, []);

    const dynamicFilters = filters?.dynamics
      ?.filter((filter) => filter.values.some((value) => value.is_selected))
      .reduce((acc, filter) => {
        acc[filter.id] = filter.values
          .filter((value) => value.is_selected)
          .map((value) => value.id);
        return acc;
      }, {});

    return {
      category_id:
        categoryId === 'tags' || categoryId === 'brands' ? null : categoryId,

      min_price: filters?.basics?.price?.current_values?.min || null,
      max_price: filters?.basics?.price?.current_values?.max || null,
      brands: brands || [],
      tags: tags || [],
      filters: dynamicFilters || {},
      last_changed: filters?.lastChanged || {},
    };
  };

  const getSendFiltersObject = () => {
    const brands = filters?.basics?.brands?.reduce((acc, brand) => {
      if (brand.is_selected) {
        acc.push(brand.id);
      }
      return acc;
    }, []);

    const tags = filters?.basics?.tags?.reduce((acc, tag) => {
      if (tag.is_selected) {
        acc.push(tag.tag);
      }
      return acc;
    }, []);

    const dynamicFilters = filters?.dynamics
      ?.filter((filter) => filter.values.some((value) => value.is_selected))
      .reduce((acc, filter) => {
        acc[filter.id] = filter.values
          .filter((value) => value.is_selected)
          .map((value) => value.id);
        return acc;
      }, {});

    return {
      category_id:
        categoryId === 'tags' || categoryId === 'brands' ? null : categoryId,
      min_price: filters?.basics?.price?.current_values?.min || null,
      max_price: filters?.basics?.price?.current_values?.max || null,
      brands: brands || [],
      tags: tags || [],
      filters: dynamicFilters || {},
      last_changed: filters?.lastChanged || {},
    };
  };

  const parseQueryParams = (queryString) => {
    const params = new URLSearchParams(queryString);
    const filtersObject = {
      min_price: null,
      max_price: null,
      brands: [],
      tags: [],
      filters: {},
      last_changed: {},
    };
    const sortObject = {
      sortBy: undefined,
      sortOrder: undefined,
    };
    let page = undefined;

    // Parse min_price and max_price
    if (params.has('min_price')) {
      filtersObject.min_price =
        params.get('min_price') === 'null'
          ? null
          : parseInt(params.get('min_price'), 10);
    }
    if (params.has('max_price')) {
      filtersObject.max_price =
        params.get('max_price') === 'null'
          ? null
          : parseInt(params.get('max_price'), 10);
    }

    // Parse brands
    if (params.has('brands')) {
      filtersObject.brands = params.get('brands').split(',').map(Number);
    }

    // Parse tags
    if (params.has('tags')) {
      filtersObject.tags = params.get('tags').split(',');
    }

    // Parse dynamic filters
    params.forEach((value, key) => {
      if (key.startsWith('filter_')) {
        const filterId = key.replace('filter_', '');
        filtersObject.filters[filterId] = value.split(',').map(Number);
      }
    });

    // Parse last_changed
    if (params.has('last_changed_type')) {
      filtersObject.last_changed.type = params.get('last_changed_type');
    }
    if (params.has('last_changed_filter')) {
      filtersObject.last_changed.filter = parseInt(
        params.get('last_changed_filter'),
        10
      );
    }

    // Parse sortBy and sortOrder
    if (params.has('sort_by')) {
      sortObject.sortBy = params.get('sort_by');
    }
    if (params.has('sort_order')) {
      sortObject.sortOrder = params.get('sort_order');
    }

    // Parse page
    if (params.has('page')) {
      page = parseInt(params.get('page'), 10);
    }

    return {
      filtersObject,
      sortObject,
      page,
    };
  };

  const buildQueryParams = (filtersObject, sortObject, page) => {
    const params = new URLSearchParams();

    // Price range
    if (filtersObject.min_price !== undefined)
      params.set('min_price', filtersObject.min_price);
    if (filtersObject.max_price !== undefined)
      params.set('max_price', filtersObject.max_price);

    // Brands
    if (filtersObject.brands?.length) {
      params.set('brands', filtersObject.brands.join(','));
    }

    // Tags
    if (filtersObject.tags?.length) {
      params.set('tags', filtersObject.tags.join(','));
    }

    // Dynamic Filters
    if (
      filtersObject.filters &&
      Object.keys(filtersObject.filters).length > 0
    ) {
      for (const key in filtersObject.filters) {
        params.set(`filter_${key}`, filtersObject.filters[key].join(','));
      }
    }

    // Last Changed
    if (filtersObject.last_changed.filter !== undefined) {
      params.set('last_changed_type', filtersObject.last_changed.type);
      params.set('last_changed_filter', filtersObject.last_changed.filter);
    }

    // Sorting
    if (sortObject.sortBy !== undefined)
      params.set('sort_by', sortObject.sortBy);
    if (sortObject.sortOrder !== undefined)
      params.set('sort_order', sortObject.sortOrder);

    // Pagination
    if (page) params.set('page', page);

    return params.toString();
  };

  return (
    <div className="content lining-nums proportional-nums">
      <Breadcrumbs />
      <div className="flex gap-3">
        <h3 className="font-semibold text-xl mm:text-2xl lg:text-4xl text-colBlack pb-5">
          {!categoryTreeIsLoading && categoryTreeIsSuccess
            ? categoryTree?.category?.name
            : null}{' '}
          <span className="text-colDarkGray font-normal text-sm ">
            {categoryTree?.category?.product_count}{' '}
          </span>
        </h3>
        
      </div>

      <div className="flex pb-10 min-h-[420px]">
        <div className="md:block hidden basis-1/4 mr-5">
          <SidebarDirect
            setFiltersModalOpen={setFiltersModalOpen}
            filters={filters}
            setFilters={setFilters}
            trigger={trigger}
            setTrigger={setTrigger}
            resetFilters={resetFilters}
            filtersIsLoading={filtersLoading}
            filtersBlock={filtersBlock}
          />
        </div>
        <CatalogContent
          setFiltersModalOpen={setFiltersModalOpen}
          products={products}
          getVariantsIsLoading={productsLoading}
          page={page}
          handlePagination={handlePagination}
          sort={sort}
          setSort={setSort}
        />
      </div>
      <Promotions />
      <Brands />
      <Advantages />
      <AllFiltersModal
        categoryTree={categoryTree}
        open={filtersModalOpen}
        setOpen={setFiltersModalOpen}
        filters={filters}
        setFilters={setFilters}
        trigger={trigger}
        setTrigger={setTrigger}
        resetFilters={resetFilters}
        filtersIsLoading={filtersLoading}
        filtersBlock={filtersBlock}
      />
    </div>
  );
};

export default CatalogDirect;
