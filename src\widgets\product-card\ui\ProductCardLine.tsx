import { NavLink } from 'react-router-dom';

import { ProductTags } from '@/entities/product';
import { AddToCartButton, QuantityControl } from '@/features/cart';
import {
  PreviewGallery,
  ProductAttributesDisplay,
  useProductCard,
  DiscountBadge,
  StockStatus,
  ProductName
} from '@/widgets/product-card';
import { PriceDisplay } from './PriceDisplay';

import type { Product } from '@/entities/product';

interface ProductCardLineProps {
  product: Product;
}

export const ProductCardLine = ({ product }: ProductCardLineProps): JSX.Element => {
  const { productInCart, productPrice } = useProductCard(product);

  return (
    <div className="lg:flex justify-between bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 p-3">
      <div className="mm:flex lg:pr-4 lg:max-w-[800px] w-full">
        {/* Product Image with Gallery */}
        <div className="relative">
          <NavLink to={`/catalog/${product?.category?.slug}/${product?.slug}`}>
            <PreviewGallery
              product={product}
              showTags={false}
              showButtons={true}
              className="max-w-[220px] min-w-[220px] h-[220px] overflow-hidden relative bg-gray-100 rounded-lg"
            />
          </NavLink>
        </div>

        {/* Product Details */}
        <div className="mm:pl-5 pt-3 mm:pt-0 flex flex-col justify-between">
          <div>
            {/* Discount Badge and Stock Status above product name */}
            <div className="flex flex-wrap gap-2 mb-2">
              <DiscountBadge product={product} />
              <StockStatus product={product} />
            </div>

            {/* Product Name */}
            <ProductName
              product={product}
              maxLines={2}
              size="md"
              weight="bold"
              className="pb-1"
            />

            {/* Product Tags */}
            {product?.tags && Array.isArray(product.tags) && product.tags.length > 0 && (
              <div className="my-1">
                <ProductTags product={product} variant="default" limit={3} />
              </div>
            )}

            {/* Product Description */}
            <p className="font-medium text-sm text-colDarkGray leading-5 break-all line-clamp-3 pb-2 mt-1">
              {product?.description}
            </p>

            {/* Product Attributes */}
            <div className="bg-colSuperLight p-2 rounded-md mt-1">
              <ProductAttributesDisplay product={product} showSKU={true} />
            </div>
          </div>
        </div>
      </div>

      {/* Price and Cart Controls */}
      <div className="lg:max-w-xs w-full mt-4 lg:mt-0 lg:border-l lg:pl-4 lg:flex lg:flex-col lg:justify-between">
        <div className="flex justify-end">
          {/* Price Display */}
          <div className="flex flex-col items-end">
            <PriceDisplay
              price={productPrice}
              variant="total-product-card"
              size="xl"
              showDiscount={true}
            />

            {/* Per Unit Price - Only show if product is in cart */}
            {productInCart && productInCart.price && typeof productInCart.price.final === 'number' && (
              <div className="text-xs text-colDarkGray mt-1 text-right">
                {`${productInCart.price.final} ${productInCart.price.currency?.symbol || ''}/${productInCart.price.unit || 'шт'}`}
              </div>
            )}
          </div>
        </div>

        {/* Cart Controls */}
        <div className="flex justify-between space-x-3 pt-3 lg:pt-5 mt-auto">
          {!productInCart ? (
            <AddToCartButton
              product={product}
              className="transition-all flex justify-center items-center min-h-12 text-white rounded-md p-2 font-semibold w-full duration-200"
            />
          ) : (
            <div className="w-full">
              <QuantityControl
                product={productInCart}
                enableRemove
                className="w-full"
                showMessage={true}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
