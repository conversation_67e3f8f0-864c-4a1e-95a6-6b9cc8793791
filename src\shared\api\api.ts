// src/shared/api/api.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { toast } from 'sonner';

import { getTokenFromCookies } from '@/entities/user/lib/cookies';

import type { RootState } from '@/app/providers/store';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;
interface ApiResponse<T = any> {
  success: boolean | string;
  err?: string;
  err_code?: string;
  data?: T;
  notification?: {
    message: string;
    type: 'success' | 'error' | 'warning' | 'info';
  };
}

// Define error codes that should NOT trigger a toast notification
// This is useful for expected errors that are handled in UI components
const SILENT_ERROR_CODES = [
  // Add specific error codes here that shouldn't show toasts
  // 'auth_required',
  // 'session_expired',
  // 'token_expired',
  // 'invalid_token',
];

/**
 * Base query setup with consistent token handling approach
 * Always uses token from cookies for reliability
 */
const baseQuery = fetchBaseQuery({
  baseUrl: API_BASE_URL,
  prepareHeaders: (headers, { getState }) => {
    // Always use direct cookie access for consistent auth behavior
    // const token = getTokenFromCookies();

    // if (token) {
    //   headers.set('Authorization', `Bearer ${token}`);

    //   // Log token in development
    //   if (process.env.NODE_ENV === 'development') {
    //     console.debug(
    //       '[API] Using token:',
    //       token ? `${token.substring(0, 5)}...` : 'none'
    //     );
    //   }
    // }
    // Ensure credentials: 'include' is active for cookie-based auth
    // (It's already set below in the baseQuery config)

    return headers;
  },
  credentials: 'include', // Include cookies in cross-domain requests
});

/**
 * Enhanced base query with logging and error handling
 */
const loggingBaseQuery = async (args, api, extraOptions) => {
  const startTime = Date.now();

  try {
    const result = await baseQuery(args, api, extraOptions);

    // Log details in development only
    if (process.env.NODE_ENV === 'development') {
      // console.log('API Request:', args);
      // console.log('API Response:', result);
    }

    // Check if response exists and has data
    if (result?.data) {
      const response = result.data as ApiResponse;

      // Check for notifications from the API
      if (
        response.notification &&
        (response.success === true || response.success === 'ok')
      ) {
        const { message, type } = response.notification;
        // Display the toast with the appropriate type
        if (type === 'success') toast.success(message);
        else if (type === 'error') toast.error(message);
        else if (type === 'warning') toast.warning(message);
        else if (type === 'info') toast.info(message);
      }

      // Handle API-level errors (where success: false)
      if (response.success === false) {
        // Transform to RTK Query error format without showing toast
        // (comment out toast as requested by the user)
        // const shouldShowToast = response.err &&
        //   (!response.err_code || !SILENT_ERROR_CODES.includes(response.err_code));
        //
        // if (shouldShowToast) {
        //   toast.error(response.err);
        // }

        return {
          error: {
            status: 200, // HTTP status was ok, but API indicates error
            data: response,
          },
        };
      }
    }

    // Handle HTTP errors (non-2xx responses)
    if (result.error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('API Error:', result.error);
      }

      // Check if there's a notification in the error response
      const errorData = result.error.data as ApiResponse | undefined;

      if (errorData?.notification) {
        const { message, type } = errorData.notification;
        // Display toast based on notification type
        if (type === 'success') toast.success(message);
        else if (type === 'error') toast.error(message);
        else if (type === 'warning') toast.warning(message);
        else if (type === 'info') toast.info(message);
      }
      // Comment out standard error toasts as requested
      // else {
      //   // Show appropriate toast messages based on error status
      //   if (result.error.status >= 500) {
      //     toast.error('Произошла ошибка сервера. Пожалуйста, попробуйте позже.');
      //   } else if (result.error.status >= 400) {
      //     // Check for auth errors to handle specially
      //     if (result.error.status === 401) {
      //       // This would be handled by the app's auth system separately
      //       // Don't show a toast for 401 errors
      //     }
      //     // Only show generic client error if there's no specific message
      //     else if (!result.error.data?.err) {
      //       toast.error('Произошла ошибка. Пожалуйста, проверьте введенные данные.');
      //     }
      //   }
      // }
    }

    return result;
  } catch (error) {
    // Comment out toast for unexpected errors as per request
    // toast.error('Произошла непредвиденная ошибка. Пожалуйста, попробуйте позже.');

    return {
      error: {
        status: 'CUSTOM_ERROR',
        error: error.message || 'Unexpected error occurred',
      },
    };
  } finally {
    // Log request timing in development
    const endTime = Date.now();
    if (process.env.NODE_ENV === 'development') {
      // console.log(`API Request took ${endTime - startTime}ms`);
    }
  }
};

/**
 * RTK Query API configuration
 */
export const api = createApi({
  reducerPath: 'api',
  baseQuery: loggingBaseQuery,
  tagTypes: [
    'Favorite',
    'Order',
    'Comparison',
    'User',
    'Cart',
    'Organization',
    'Review',
    'UserReviews',
    'Product',
    'RecentItems',
  ],
  // Global configuration for all endpoints
  keepUnusedDataFor: 30, // Default cache lifetime of 30 seconds
  refetchOnMountOrArgChange: false, // Don't refetch on component mount by default
  refetchOnFocus: false, // Don't refetch when window regains focus
  refetchOnReconnect: true, // Do refetch when reconnecting

  // Define endpoints
  endpoints: (builder) => ({}),
});
