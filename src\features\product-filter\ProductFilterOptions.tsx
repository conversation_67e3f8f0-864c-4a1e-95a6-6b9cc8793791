import { Search } from '@/shared/ui';
import { useProductFilterOption } from './hook/useProductFilterOption';
import { FilterOptionColorSquare } from './ui/Options/FilterOptionColorSquare';
import { FilterOptionRange } from './ui/Options/FilterOptionRange';
import { FilterOptionText } from './ui/Options/FilterOptionText';
import { FilterOptionVisual } from './ui/Options/FilterOptionVisual';

export const ProductFilterOptions = () => {
  const { filtered, bodyRef, visible, showSearch, handleFilter, bodyItemsRef, onChange, disabled } =
    useProductFilterOption();

  const multiple = () => {
    switch (filtered.type) {
      case 'color': {
        return <FilterOptionColorSquare data={filtered} onChange={onChange} disabled={disabled} />;
      }
      case 'text': {
        return <FilterOptionText data={filtered} onChange={onChange} disabled={disabled} />;
      }
      case 'image': {
        return <FilterOptionVisual data={filtered} onChange={onChange} disabled={disabled} />;
      }
    }
  };

  const options = () => {
    switch (filtered.input_type) {
      case 'multiple': {
        return multiple();
      }
      case 'range': {
        return <FilterOptionRange data={filtered} onChange={onChange} />;
      }
    }
  };

  return (
    <div
      className={`${visible ? 'overflow-auto' : 'overflow-hidden'} transition-all will-change-[height] lining-nums flex flex-col gap-2`}
      ref={bodyRef}
    >
      {showSearch && (
        <span className='flex items-center border-[2px] mt-2 focus-within:border-colGreen rounded-[12px] items-center px-2 py-2  gap-[4px] grow-1 bg-[#E6E9E8]'>
          <Search size={16} color='#5F6664' />
          <input
            type='text'
            onChange={handleFilter}
            placeholder='Поиск'
            className='focus:outline-none lining-nums text-[14px] bg-transparent flex items-center placeholder:text-[#5F6664]'
          />
        </span>
      )}
      <div
        className={` grow-1 ${filtered.input_type === 'multiple' ? 'scrollable2 overflow-auto' : ''} `}
        ref={bodyItemsRef}
      >
        {options()}
      </div>
    </div>
  );
};
