import React from 'react';
import { useGetSelfReviewsQuery } from '@/entities/review/api/reviewApi';
import { Review } from '@/entities/review';
import { Loading } from '@/shared/ui/Loader';
import { useAuthContext } from '@/entities/user/model/AuthContext';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { ProductPreview } from '@/entities/product';

const UserReviews = () => {
  const { isAuthenticated } = useAuthContext();
  const isInitialized = useSelector((state: RootState) => state.user.isInitialized);

  const { data, isLoading } = useGetSelfReviewsQuery(undefined, {
    // Skip the query until auth is initialized and we're authenticated
    skip: !isInitialized || !isAuthenticated
  });

  const reviews = data?.comments || [];

  if (isLoading) {
    return <Loading />;
  }

  return (
    <div className="w-full">
      <h1 className="text-2xl font-semibold mb-6">Мои отзывы</h1>

      {reviews.length > 0 ? (
        <div className="grid grid-cols-1 gap-6">
          {reviews.map((review) => (
            <div key={review.created_at} className="space-y-4">
              <ProductPreview product={review.item} />
              <Review review={review} />
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-colDarkGray">
          У вас пока нет отзывов. Оставьте отзыв о купленном товаре!
        </div>
      )}
    </div>
  );
};

export default UserReviews;
