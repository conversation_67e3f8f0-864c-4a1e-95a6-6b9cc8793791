# Checkout Flow Documentation

Last updated: 2025-04-25 09:08 UTC

## Overview

This document describes the new checkout flow implemented in the Furnica e-commerce platform. The checkout process has been redesigned to support multiple suborders from different companies in a single checkout session.

## Architecture

The checkout flow consists of three main components:

1. **Cart** - Where users select items and initiate the checkout process
2. **Checkout API** - Backend service that prepares order data
3. **Checkout Page** - UI that displays the prepared orders and collects final user information

## Flow Diagram

```
User Cart → API Call → Checkout Data → Checkout Page → Order Payment → Payment/Confirmation
```

## Implementation Details

### Cart to Checkout Transition

When a user clicks "Перейти к оформлению" (Proceed to Checkout) in the cart:

1. The cart components (`CartOrderInfo.tsx` and `MobileToCheckoutBar.tsx`) make an API call to `/api/ProductOrders/checkout`
2. The API returns structured data with all orders, items, and available payment methods
3. This data is stored in localStorage under the key `checkout_order_data`
4. The user is redirected to the checkout page

### Checkout Page

The checkout page (`CartCheckout.tsx`):

1. Retrieves the order data from localStorage
2. Displays each order separately with its items
3. Shows a pickup point selector with a map
4. Provides payment method selection
5. Collects user contact information
6. Calculates and displays order totals

### Order Submission

When the user submits the order:

1. If the user is not authenticated, the order data is stored in localStorage and the auth modal is shown
2. After authentication, the stored order is submitted automatically
3. The submission sends an API request to `/api/ProductOrders/checkoutPayment` with the array of order numbers and selected payment method:
   ```json
   {
     "order_number": ["240425-697481-440317", "240425-697481-440317"],
     "payment_id": 1
   }
   ```
4. On successful submission, the user is redirected to the payment page or order confirmation

## API Data Structure

### Checkout API Response

The checkout API returns data in the following format:

```json
{
  "data": [
    {
      "order_number": "240425-019825-766283",
      "date": "только что",
      "status": { "id": 1, "name": "Новый", ... },
      "items": [ ... array of order items ... ],
      "company_info": { ... },
      "total": { "amount": 11000, "quantity": 3, "discount": 0 }
    },
    // Additional orders...
  ],
  "variants_pay": [
    { "payment_id": 1, "name": "Оплата картой" },
    // Additional payment methods...
  ],
  "total_request_time": 0.1085,
  "api_processing_time": 0.0912,
  "sessid": "215ae58a9dea85f779c601c1d01240e2"
}
```

### Payment API Request

The payment submission sends the following format:

```json
{
  "order_number": ["240425-697481-440317", "240425-697481-440317"],
  "payment_id": 1
}
```

## User Interface

The new checkout page has a two-column layout:

1. **Left column:** Order details grouped by company, contact information form, and pickup location
2. **Right column:** Payment method selection and order summary

The design supports both desktop and mobile views and includes a sticky payment summary section.

## Authentication Flow

To handle cases where users need to authenticate during checkout:

1. Order data is stored in localStorage with a timestamp (30-minute expiry)
2. Authentication modal is shown to the user
3. After successful authentication, the pending order is processed automatically

## Future Improvements

Planned enhancements for the checkout flow:

1. Support for different delivery methods (not just pickup)
2. Address selection for delivery
3. More detailed status tracking after order submission
4. Better error handling and recovery mechanisms

## Related Components

- `src/features/cart/ui/components/CartOrderInfo.tsx`
- `src/features/cart/ui/components/MobileToCheckoutBar.tsx`
- `src/pages/Checkout/CartCheckout.tsx`
- `src/entities/order/api/orderApi.ts`

## API Endpoints

- **GET/POST /api/ProductOrders/checkout** - Prepares the checkout data with orders split by company
- **POST /api/ProductOrders/checkoutPayment** - Processes the final order payment with selected payment method

## Testing Guidelines

When testing the checkout flow, ensure:

1. Multiple orders are correctly grouped by company
2. Payment methods display correctly
3. Order totals calculate accurately
4. Authentication flow preserves order data
5. Form validation works properly
6. Error scenarios are handled gracefully
