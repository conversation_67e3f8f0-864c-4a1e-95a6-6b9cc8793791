import React from 'react';
import { useSelector } from 'react-redux';
import { NavLink } from 'react-router-dom';

import { IReview, RatingStars, Review, useGetReviewsQuery, useHasUserReview } from '@/entities/review';
import { useModal } from '@/features/modals/model/context';
import { RootState } from '@/app/store';
import { Button } from '@/shared/ui';

// Placeholder interface for the product variant passed in props
interface ProductVariant {
  id: number;
  slug: string;
  category?: {
    slug: string;
  };
}

interface ReviewsTabProps {
  current?: ProductVariant;
}

const ReviewsTab: React.FC<ReviewsTabProps> = ({ current }) => {
  const { showModal } = useModal();
  const { isAuthenticated } = useSelector((state: RootState) => state.user);

  // The hook should ideally get the slug, not the whole object.
  // For now, we'll keep it as is to avoid breaking changes.
  const { hasReview } = useHasUserReview(current?.slug);

  const { data } = useGetReviewsQuery(current?.id, {
    skip: !current?.id,
    refetchOnMountOrArgChange: true,
  });

  const reviews: IReview[] = data?.comments || [];
  const totalReviews: number = data?.total_count || 0;
  const averageRating: string = Number(data?.avg_rating || 0).toFixed(1);

  const handleWriteReview = () => {
    if (isAuthenticated) {
      showModal({ type: 'review', variantId: current?.id });
    } else {
      showModal({ type: 'auth' });
    }
  };

  return (
    <div className="py-5">
      <h3 className="text-2xl font-semibold" id="reviews">
        Отзывы
      </h3>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center my-5 gap-4">
        <div className="flex items-center">
          <div className="text-2xl font-semibold mr-2">{averageRating}</div>
          <div className="flex items-center mr-2">
            <RatingStars totalStars={5} initialRating={Number(averageRating)} />
          </div>
          <div className="text-lg text-colDarkGray">
            {totalReviews > 0
              ? `${totalReviews} ${totalReviews === 1 ? 'отзыв' : 'отзывов'}`
              : 'Нет отзывов'}
          </div>
        </div>
        {!hasReview ? (
          <Button onClick={handleWriteReview} variant="outline" size="lg">
            Оставить отзыв
          </Button>
        ) : (
          <Button variant="outline" size="lg" disabled>
            Вы уже оставили отзыв
          </Button>
        )}
      </div>
      {reviews.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          {reviews.map((review) => (
            <Review key={review.id || review.created_at} review={review} variant_id={data?.variant_id} />
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-colDarkGray">
          Пока нет отзывов. Будьте первым, кто оставит отзыв!
        </div>
      )}
      {totalReviews > 4 && (
        <div className="mt-4 text-center">
          <NavLink
            to={`/catalog/${current?.category?.slug}/${current?.slug}/reviews`}
            className="text-colGreen font-semibold underline underline-offset-8 cursor-pointer mt-5"
          >
            Смотреть все отзывы
          </NavLink>
        </div>
      )}
    </div>
  );
};

export default ReviewsTab;
