import { useEffect } from 'react';

import { FavDetail } from '@components/Favorites/FavDetail';
import { FavSidebar } from '@components/Favorites/FavSidebar';
import ErrorEmpty from '@helpers/Errors/ErrorEmpty';
import { useSelector } from 'react-redux';

import { useAuthContext } from '@/entities/user/model/AuthContext';
import { useFavorites } from '@/features/favorite';
import { scrollToTop } from '@/shared/lib/scrollToTop';
import { Breadcrumbs } from '@/widgets/breadcrumbs';
import { CategorySwitcher, useCategorySwitcher } from '@/widgets/category-switcher';

import type { RootState } from '@/app/providers/store';

const Favorites = (): JSX.Element => {
  const { isAuthenticated } = useAuthContext();
  const {
    favorites: allFavorites,
    isLoading,
    isError: favoritesIsError,
    refreshFromServer,
  } = useFavorites();

  // Always use Redux store for category data
  const { categories } = useSelector(
    (state: RootState) => state.favorite
  );

  // Use our new hook for category switching logic
  const {
    selectedCategory,
    filteredProducts: filteredFavorites,
    handleCategoryChange,
    isUpdating
  } = useCategorySwitcher(categories, allFavorites);
  
  // Update loading state to include category operations
  const isPageLoading = isLoading || isUpdating;

  useEffect(() => {
    scrollToTop();
  }, []);

  useEffect(() => {
    console.log('[Favorites.tsx] useEffect for refreshFromServer triggered. isAuthenticated:', isAuthenticated); // <--- ADD LOG
    // Only refresh from server if authenticated.
    // This effect will run once when isAuthenticated becomes true, or on mount if already true.
    if (isAuthenticated) {
      void refreshFromServer();
    }
    // For unauthenticated users, useFavorites hook should handle loading from session storage internally.
  }, [isAuthenticated, refreshFromServer]);

  // The above will still run if refreshFromServer changes identity due to 'favorites' in its own deps.
  // A more robust way to ensure it runs truly once per authenticated session might be needed
  // if refreshFromServer cannot be made stable. For now, this reduces one common cause.
  // If the goal is truly ONCE on component mount for an authenticated user:
  // const initialAuthRef = useRef(isAuthenticated);
  // useEffect(() => {
  //   if (initialAuthRef.current) {
  //     void refreshFromServer();
  //   }
  // }, []); // Empty array for mount only, checking ref for initial auth state.

  if (favoritesIsError) {
    console.error('Error loading favorites page data.');
  }

  return (
    <div className="content pb-6">
      <Breadcrumbs />
      <h1 className="block text-2xl md:text-[40px] font-semibold text-colBlack pb-5">
        Избранное
      </h1>
      {isPageLoading ? (
        <p>Loading categories...</p>
      ) : !isPageLoading && categories?.length > 0 ? (
        <CategorySwitcher
          categories={categories}
          selectedCategory={selectedCategory}
          onCategoryChange={handleCategoryChange}
        />
      ) : null}
      {isPageLoading ? (
        <p>Loading favorites...</p>
      ) : !isPageLoading &&
        isAuthenticated &&
        filteredFavorites &&
        filteredFavorites.length > 0 ? (
        <div className="md:flex">
          {/* <FavSidebar
            favorite={serverDataForCategories}
            selectedFilter={{ type: 'category', filter: selectedFilter }}
            setSelectedFilter={setSelectedFilter}
          /> */}
          <FavDetail favorite={filteredFavorites} user={isAuthenticated} />
        </div>
      ) : !isAuthenticated &&
        filteredFavorites &&
        filteredFavorites.length > 0 ? (
        <div className="md:flex">
          <FavDetail favorite={filteredFavorites} user={isAuthenticated} />
        </div>
      ) : !isPageLoading &&
        !favoritesIsError &&
        (!filteredFavorites || filteredFavorites.length === 0) ? (
        <ErrorEmpty
          title="Еще не готовы к покупке?"
          desc="Добавляйте понравившийся товар в избранное, чтобы не потерять его."
          height="420px"
        />
      ) : null}
    </div>
  );
};

export default Favorites;
