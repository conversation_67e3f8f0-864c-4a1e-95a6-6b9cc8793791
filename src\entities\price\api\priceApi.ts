import { api } from '@/shared/api/api';

import type {
  GetCartItemPriceRequest,
  GetCartItemPriceResponse,
} from './types';

export const priceApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getCartItemPrice: builder.mutation<
      GetCartItemPriceResponse,
      GetCartItemPriceRequest
    >({
      query: (data) => ({
        url: '/api/Products/price/get',
        method: 'POST',
        body: data,
      }),
      // Only invalidate User DATA, not Cart LIST
      // This prevents unnecessary cart refetching when just getting prices
      invalidatesTags: [
        { type: 'User', id: 'DATA' },
      ],
    }),
  }),
});

export const { useGetCartItemPriceMutation } = priceApi;
