import SidebarFilters from './SidebarFilters/SidebarFilters';

const CatProdSidebar = ({
  filters,
  setFiltersModalOpen,
  setFilters,
  filtersIsLoading,
  resetFilters,
  trigger,
  setTrigger,
  filtersBlock
}) => {
  return (
    <>
      <SidebarFilters
        setFiltersModalOpen={setFiltersModalOpen}
        isLoading={filtersIsLoading}
        filters={filters}
        setFilters={setFilters}
        trigger={trigger}
        setTrigger={setTrigger}
        resetFilters={resetFilters}
        filtersBlock={filtersBlock}
      />
    </>
  );
};

export default CatProdSidebar;
