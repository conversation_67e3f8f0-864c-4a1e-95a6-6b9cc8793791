/**
 * Utility functions for transforming filter data in the catalog
 */

/**
 * Transforms filters state into an object for API requests
 * Extracts selected filters from the complete filters state
 * 
 * @param {Object} filters - Filters state object
 * @param {string} categoryId - Current category ID
 * @returns {Object} Object formatted for API request
 */
export const buildFilterObject = (filters, categoryId) => {
  // Extract selected brands
  const brands = filters?.basics?.brands?.reduce((acc, brand) => {
    if (brand && brand.is_selected) {
      acc.push(brand.id);
    }
    return acc;
  }, []) || [];

  // Extract selected tags
  const tags = filters?.basics?.tags?.reduce((acc, tag) => {
    if (tag && tag.is_selected) {
      acc.push(tag.tag);
    }
    return acc;
  }, []) || [];

  // Extract selected dynamic filters
  const dynamicFilters = filters?.dynamics
    ?.filter((filter) => filter?.values?.some((value) => value?.is_selected))
    .reduce((acc, filter) => {
      if (filter && filter.id && filter.values) {
        acc[filter.id] = filter.values
          .filter((value) => value?.is_selected)
          .map((value) => value?.id)
          .filter(Boolean);
      }
      return acc;
    }, {}) || {};

  // Create the final request object
  return {
    category_id:
      categoryId === 'tags' || categoryId === 'brands' ? null : categoryId,
    min_price: filters?.basics?.price?.current_values?.min || null,
    max_price: filters?.basics?.price?.current_values?.max || null,
    brands,
    tags,
    filters: dynamicFilters,
    last_changed: filters?.lastChanged || {},
  };
};

/**
 * Parses URL query parameters into filter, sort, and pagination objects
 * 
 * @param {string} queryString - URL query string
 * @returns {Object} Object containing parsed parameters
 */
export const parseQueryParams = (queryString) => {
  const params = new URLSearchParams(queryString);
  const filtersObject = {
    min_price: null,
    max_price: null,
    brands: [],
    tags: [],
    filters: {},
    last_changed: {},
  };
  const sortObject = {
    sortBy: undefined,
    sortOrder: undefined,
  };
  let page = undefined;

  // Parse min_price and max_price
  if (params.has('min_price')) {
    filtersObject.min_price =
      params.get('min_price') === 'null'
        ? null
        : parseInt(params.get('min_price'), 10);
  }
  if (params.has('max_price')) {
    filtersObject.max_price =
      params.get('max_price') === 'null'
        ? null
        : parseInt(params.get('max_price'), 10);
  }

  // Parse brands
  if (params.has('brands')) {
    try {
      const brandsParam = params.get('brands');
      filtersObject.brands = brandsParam ? brandsParam.split(',').map(Number).filter(id => !isNaN(id)) : [];
    } catch (error) {
      console.error('Error parsing brands parameter:', error);
      filtersObject.brands = [];
    }
  }

  // Parse tags
  if (params.has('tags')) {
    try {
      const tagsParam = params.get('tags');
      filtersObject.tags = tagsParam ? tagsParam.split(',').filter(Boolean) : [];
    } catch (error) {
      console.error('Error parsing tags parameter:', error);
      filtersObject.tags = [];
    }
  }

  // Parse dynamic filters (filter_*)
  params.forEach((value, key) => {
    if (key.startsWith('filter_')) {
      try {
        const filterId = key.replace('filter_', '');
        filtersObject.filters[filterId] = value.split(',').map(Number).filter(id => !isNaN(id));
      } catch (error) {
        console.error(`Error parsing filter parameter ${key}:`, error);
      }
    }
  });

  // Parse last_changed
  if (params.has('last_changed_type')) {
    filtersObject.last_changed.type = params.get('last_changed_type');
  }
  if (params.has('last_changed_filter')) {
    try {
      filtersObject.last_changed.filter = parseInt(
        params.get('last_changed_filter'),
        10
      );
    } catch (error) {
      console.error('Error parsing last_changed_filter parameter:', error);
    }
  }

  // Parse sortBy and sortOrder
  if (params.has('sort_by')) {
    sortObject.sortBy = params.get('sort_by');
  }
  if (params.has('sort_order')) {
    sortObject.sortOrder = params.get('sort_order');
  }

  // Parse page
  if (params.has('page')) {
    try {
      const pageParam = params.get('page');
      page = pageParam ? parseInt(pageParam, 10) : undefined;
      if (isNaN(page)) page = undefined;
    } catch (error) {
      console.error('Error parsing page parameter:', error);
      page = undefined;
    }
  }

  return {
    filtersObject,
    sortObject,
    page,
  };
};

/**
 * Builds URL query parameters from filter, sort, and pagination objects
 * 
 * @param {Object} filtersObject - Filter parameters object
 * @param {Object} sortObject - Sort parameters object
 * @param {number} page - Current page number
 * @returns {string} URL query string
 */
export const buildQueryParams = (filtersObject, sortObject, page) => {
  const params = new URLSearchParams();

  // Price range
  if (filtersObject.min_price !== undefined && filtersObject.min_price !== null)
    params.set('min_price', filtersObject.min_price);
  if (filtersObject.max_price !== undefined && filtersObject.max_price !== null)
    params.set('max_price', filtersObject.max_price);

  // Brands
  if (filtersObject.brands?.length) {
    params.set('brands', filtersObject.brands.join(','));
  }

  // Tags
  if (filtersObject.tags?.length) {
    params.set('tags', filtersObject.tags.join(','));
  }

  // Dynamic Filters
  if (
    filtersObject.filters &&
    Object.keys(filtersObject.filters).length > 0
  ) {
    for (const key in filtersObject.filters) {
      if (filtersObject.filters[key]?.length) {
        params.set(`filter_${key}`, filtersObject.filters[key].join(','));
      }
    }
  }

  // Last Changed
  if (filtersObject.last_changed?.filter !== undefined) {
    params.set('last_changed_type', filtersObject.last_changed.type);
    params.set('last_changed_filter', filtersObject.last_changed.filter);
  }

  // Sort parameters
  if (sortObject?.sortBy !== undefined)
    params.set('sort_by', sortObject.sortBy);
  if (sortObject?.sortOrder !== undefined)
    params.set('sort_order', sortObject.sortOrder);

  // Pagination
  if (page) params.set('page', page);

  return params.toString();
};
