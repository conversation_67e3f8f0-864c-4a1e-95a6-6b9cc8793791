import { useGetFiltersNewMutation } from '@/entities/filter';
import { useGetVariantsTestMutation } from '@/entities/product';
import {
  buildQueryParams,
  buildServerFilter
} from '@/shared/lib/catalog-filter/catalog-filter.utils';
import { getFiltersFromUrl } from '@/shared/lib/getFiltersFromUrl';
import { modalFilterItemObserver } from '@/shared/lib/observer';
import { bodyScrollLockSingleObserver } from '@/shared/lib/observer/body.observer';
import { updateFilterState } from '@/shared/lib/queryUtils';
import { ProductFilter as IProductFilter } from '@/types/Filters/ProductFilter';
import { ProductFilterURL } from '@/types/Filters/ProductFilterURL';
import { OrderByType, SortOrderType } from '@/types/Filters/Sort';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ProductFilterURLInput } from '../CatalogRoot';
import { useSort } from './useSort';

export const useExplore = () => {
  const { handleSortChange: onSortChange, currentSort, sortBy } = useSort();

  const [filters, setFilters] = useState<IProductFilter[]>([]);
  const [filtersLoading, setFiltersLoading] = useState(false);
  const [products, setProducts] = useState<any[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [chips, setChips] = useState<ProductFilterURL[]>([]);
  const [productsPage, setProductsPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState(true);
  const [bannerData, setBannerData] = useState<any>(null);

  const pageParam = useRef<{ [keyof in string]: string[] } | null>(null);
  const observer = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  const cardView = localStorage.getItem('cardView');
  const [cardType, setTypeCard] = useState(cardView ? cardView : 'tile');

  const params = useParams();
  const navigate = useNavigate();

  const [fetchFiltersProducts] = useGetVariantsTestMutation();
  const [fetchFilters] = useGetFiltersNewMutation();

  const createProductFilterURL = (
    filterData: IProductFilter[],
    userValues: unknown
  ): ProductFilterURL[] => {
    const result: ProductFilterURL[] = [];

    for (const [key, filter] of Object.entries(userValues)) {
      if (filter.type === 'multiple') {
        const parentId = key;
        const foundFilter = filterData.find((f) => f.id == parentId);
        if (foundFilter && foundFilter.input_type === 'multiple') {
          for (const val of filter.value) {
            const matched = foundFilter.values.find((v) => v.id == val);
            if (matched) {
              result.push({
                type: 'multiple',
                parentId,
                id: matched.id,
                title: foundFilter.name,
                text: matched.text,
                value: matched.id
              });
            }
          }
        }
      }

      if (filter.type === 'range') {
        const range = filter.value;
        result.push({
          type: 'range',
          parentId: key,
          title: 'Цена',
          value: [+range.from, +range.to]
        });
      }
    }

    return result;
  };

  const onChangeFilter = async (input: ProductFilterURLInput) => {
    setFiltersLoading(true);
    setIsLoadingProducts(true);
    setProductsPage(1);

    const filtersArray = 'alone' in input ? [input.alone] : input.multiple;
    let { filterParams: queryParamsToFilterData } = getFiltersFromUrl();

    filtersArray.forEach((filter) => {
      queryParamsToFilterData = updateFilterState(queryParamsToFilterData, filter);
    });

    const dataToFilterData = queryParamsToFilterData;

    const queryParams = buildQueryParams(dataToFilterData);
    const filterDataToServerData = buildServerFilter(dataToFilterData);
    const r = await fetchFilters({
      category_id: '',
      filters: { ...filterDataToServerData, ...pageParam.current }
    });
    if ('data' in r) {
      setFilters([]);
      setFilters(r.data.data);
    }
    const p = await fetchFiltersProducts({
      filters: { ...filterDataToServerData, ...pageParam.current },
      category_id: '',
      limit: 20,
      page: 1,
      orderBy: currentSort.orderBy,
      sortOrder: currentSort.sortOrder
    });
    if ('data' in p) {
      setProducts(p.data.data);
      setIsLoadingProducts(false);
      setProductsPage((prev) => prev + 1);
    }
    navigate(`?${queryParams.join('&')}`, { replace: true });

    const { filterParams: filterData } = getFiltersFromUrl();
    if ('data' in r) {
      const items = createProductFilterURL(r.data.data, filterData);
      setChips(items);
    }

    setFiltersLoading(false);
  };

  const onResetFilters = async () => {
    setFiltersLoading(true);
    const queryParams = [];
    navigate(`?${queryParams.join('&')}`, { replace: true });

    const r = await fetchFilters({
      category_id: '',
      filters: { ...pageParam.current }
    });

    if ('data' in r) {
      setFilters(r.data.data);
    }
    const p = await fetchFiltersProducts({
      filters: { ...pageParam.current },
      categoryId: '',
      limit: 20,
      page: 1,
      orderBy: '',
      sortOrder: ''
    });
    if ('data' in p) {
      setProducts(p.data.data);
      setIsLoadingProducts(false);
    }
    setChips([]);
    setFiltersLoading(false);
  };

  const handleOpenModalAllFilters = () => {
    bodyScrollLockSingleObserver.setValue(true);

    const modalId = modalFilterItemObserver.addObserver({
      type: 'explore',
      onAccept: () => {
        modalFilterItemObserver.dissmissObserver(modalId);
        bodyScrollLockSingleObserver.setValue(false);
      },
      onCancel: () => {
        modalFilterItemObserver.dissmissObserver(modalId);
        setTimeout(() => {
          bodyScrollLockSingleObserver.setValue(false);
        }, 300);
      },
      data: filters
    });
  };

  const handleSortChange = async (value: any) => {
    onSortChange(value);
    setProductsPage(1);
    const source = 'tags' in pageParam.current ? 'tags' : 'brands';

    const [orderBy, sortOrder] = value.split('-') as [OrderByType, SortOrderType];

    const selectedSort = sortBy.find(
      (sort) => sort.orderBy === orderBy && sort.sortOrder === sortOrder
    );

    if (selectedSort) {
      setIsLoadingProducts(true);

      const { filters } = getFiltersFromUrl();

      try {
        const resultProducts = await fetchFiltersProducts({
          filters: { ...filters, ...pageParam.current },
          category_id: '',
          limit: 20,
          page: 1,
          orderBy: selectedSort.orderBy,
          sortOrder: selectedSort.sortOrder,
          source
        });
        if ('data' in resultProducts) {
          setProducts(resultProducts.data.data);
          setIsLoadingProducts(false);
        }
      } catch (error) {
        console.log(error);
      }
    }
  };

  const bannerCustom = (payload: any) => {
    if (payload.meta.name === 'tags') {
      const { layout } = payload;
      const createBanner = {};
      layout.forEach((item) => {
        if (item.type === 'text') {
          createBanner['text'] = item.content.text;
        } else {
          createBanner['image'] = item.content;
        }
      });
      setBannerData(createBanner);
    }
  };

  const firstFetch = async () => {
    const source = 'tags' in pageParam.current ? 'tags' : 'brands';
    setFiltersLoading(true);
    setIsLoadingProducts(true);
    const { filters } = getFiltersFromUrl();
    const result = await fetchFilters({
      category_id: '',
      filters: { ...filters, ...pageParam.current },
      source
    });
    const fetchProducts = await fetchFiltersProducts({
      filters: { ...filters, ...pageParam.current },
      category_id: '',
      limit: 20,
      page: 1,
      orderBy: '',
      sortOrder: '',
      source
    });

    if ('data' in result) {
      setFilters(result.data.data);
      'banner' in result.data && bannerCustom(result.data.banner);
      setFiltersLoading(false);
      const { filterParams: filterData } = getFiltersFromUrl();

      const items = createProductFilterURL(result.data.data, filterData);
      setChips(items);
    }
    if ('data' in fetchProducts) {
      setProducts(fetchProducts.data.data);
      setIsLoadingProducts(false);
    }
  };

  const loadProducts = useCallback(async () => {
    if (isLoadingProducts || !hasMore) return;

    const source = 'tags' in pageParam.current ? 'tags' : 'brands';
    setIsLoadingProducts(true);

    try {
      const { filters } = getFiltersFromUrl();

      const p = await fetchFiltersProducts({
        filters: { ...filters },
        category_id: '',
        limit: 20,
        page: productsPage,
        orderBy: currentSort.orderBy,
        sortOrder: currentSort.sortOrder,
        source
      });
      if ('data' in p) {
        setProducts((prev) => [...prev, ...p.data.data]);
        setIsLoadingProducts(false);
        setProductsPage(productsPage + 1);
        setHasMore(p.data.data.length > 0);
      }
    } catch (error) {
      console.error(error);
      setIsLoadingProducts(false);
    }
  }, [isLoadingProducts, productsPage, hasMore]);

  useEffect(() => {
    if ('tagId' in params) {
      pageParam.current = { tags: [params.tagId] };
      firstFetch();
    } else if ('brandId' in params) {
      pageParam.current = { brands: [params.brandId] };
      firstFetch();
    }
  }, []);

  useEffect(() => {
    observer.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoadingProducts) {
          loadProducts();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observer.current.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current && observer.current) {
        observer.current.unobserve(loadMoreRef.current);
      }
    };
  }, [loadProducts, hasMore, isLoadingProducts]);

  return {
    bannerData,
    pageParam,
    filters,
    filtersLoading,
    onResetFilters,
    handleOpenModalAllFilters,
    onChangeFilter,
    cardType,
    products,
    isLoadingProducts,
    setTypeCard,
    currentSort,
    sortBy,
    handleSortChange,
    chips,
    productsPage
  };
};
