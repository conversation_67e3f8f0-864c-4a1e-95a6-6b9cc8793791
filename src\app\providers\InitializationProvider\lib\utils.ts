
// src/app/providers/InitializationProvider/lib/utils.ts
import { setCart } from '@/features/cart';
import { setComparison } from '@/features/comparison';
import { setFavorite } from '@/features/favorite';
import { setRecentItems } from '@/features/recent-items';

import type { AppDispatch } from '@/app/providers/store';

export const syncLocalStorageWithState = (dispatch: AppDispatch) => {
  const comparison = JSON.parse(sessionStorage.getItem('comparison') ?? '[]');
  const favorite = JSON.parse(sessionStorage.getItem('favorite') ?? '[]');
  const recentItems = JSON.parse(sessionStorage.getItem('recentItems') ?? '[]');
  const cart = JSON.parse(sessionStorage.getItem('cart') ?? 'null');

  dispatch(setComparison(comparison));
  dispatch(setFavorite(favorite));
  dispatch(setRecentItems(recentItems));
  dispatch(setCart(cart));
};