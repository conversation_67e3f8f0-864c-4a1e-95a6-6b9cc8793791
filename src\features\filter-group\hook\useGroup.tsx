import { useState } from 'react';
import { ProductFilterProps } from '../Group';
import {
  GroupContextType,
  GroupFilterColor,
  GroupFilterMultiple,
  GroupFilterRange
} from '../model/context';

export const useGroup = ({}: ProductFilterProps): GroupContextType => {
  const [isOpen, setIsOpen] = useState(false);

  const value: GroupFilterColor = {
    type: 'color',
    id: 1,
    is_active: true,
    name: 'Цвет',
    values: [
      {
        color: '#ff0000',
        id: 1,
        is_active: true,
        is_selected: false,
        second_color: null,
        text: 'красный'
      },
      {
        color: 'black',
        id: 2,
        is_active: true,
        is_selected: false,
        second_color: 'aquamarine',
        text: 'зеленый'
      },
      {
        color: null,
        id: 3,
        is_active: true,
        is_selected: false,
        second_color: null,
        text: 'Альшпачи'
      }
    ]
  };

  const valueMultiple: GroupFilterMultiple = {
    type: 'multiple',
    id: 2,
    is_active: true,
    name: 'Производитель',
    values: []
  };

  const valueRange: GroupFilterRange = {
    current_values: {
      max: 100,
      min: 10
    },
    type: 'range',
    id: 3,
    is_active: true,
    name: 'Цена',
    max: 120,
    min: 0
  };

  return { isOpen, setIsOpen, data: valueRange };
};
