import { useState } from 'react';

import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Checkbox,
  FormControlLabel,
  InputAdornment,
  TextField
} from '@mui/material';

import { checkCloseToWhite } from '@/shared/lib/checkCloseToWhite';
import { ArrowIcon } from '@/shared/ui/icons';

/**
 * CheckboxFilter component that renders a group of checkboxes for filtering
 * Includes a search input to filter the values by text
 *
 * @param {Object} filter - The filter object containing name, type, and values
 * @param {Object} filters - The complete filters state
 * @param {Function} setFilters - Function to update the filters state
 */
function CheckboxFilter({ filter, filters, setFilters }) {
  // State for search/filter input
  const [searchTerm, setSearchTerm] = useState('');

  /**
   * Handle checkbox selection change
   * Updates the filters state with the new selection
   *
   * @param {number|string} filterId - ID of the filter
   * @param {number|string} valueId - ID of the selected value
   */
  const handleCheckboxChange = (filterId, valueId) => {
    const currentState = JSON.parse(JSON.stringify(filters));

    const filter = currentState.dynamics.find((filter) => filter.id === filterId);
    const value = filter.values.find((value) => value.id === valueId);
    value.is_selected = !value.is_selected;

    currentState.lastChanged = {
      type: 'dynamics',
      filter: filterId
    };
    setFilters(currentState);
  };

  /**
   * Handle search input change
   * Updates the searchTerm state
   *
   * @param {Event} event - The input change event
   */
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  /**
   * Filter the values based on the search term
   * Returns values that include the search term (case insensitive)
   */
  const filteredValues = filter?.values?.filter((val) =>
    val?.text?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div key={filter?.id}>
      <Accordion
        sx={{
          boxShadow: 'none',
          padding: 0
        }}
        defaultExpanded
        disableGutters
      >
        <AccordionSummary
          sx={{ padding: 0, flexDirection: 'row-reverse', gap: '8px' }}
          style={{ minHeight: 0 }}
          expandIcon={<ArrowIcon className='!w-4 !h-4 rotate-[180deg]' />}
        >
          <p className='font-semibold text-colBlack line-clamp-2 break-words leading-[120%]'>
            {filter?.name}
          </p>
        </AccordionSummary>
        <AccordionDetails sx={{ padding: 0, marginLeft: '-8px' }}>
          {/* Search input for filtering values */}
          {filter?.values?.length > 5 && (
            <TextField
              size='small'
              placeholder='Поиск'
              value={searchTerm}
              onChange={handleSearchChange}
              margin='dense'
              variant='outlined'
              fullWidth
              sx={{
                mb: 1,
                '& .MuiOutlinedInput-root': {
                  borderRadius: '4px',
                  '& fieldset': {
                    borderColor: '#E5E7EB'
                  },
                  '&:hover fieldset': {
                    borderColor: '#15765B'
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#15765B'
                  }
                },
                '& .MuiInputBase-input': {
                  padding: '8px 12px',
                  fontSize: '14px'
                }
              }}
              InputProps={{
                endAdornment: searchTerm ? (
                  <InputAdornment position='end'>
                    <span
                      className='cursor-pointer text-gray-400 hover:text-gray-600'
                      onClick={() => setSearchTerm('')}
                    >
                      ✕
                    </span>
                  </InputAdornment>
                ) : null
              }}
            />
          )}

          {/* Scrollable area with filter values */}
          <div className='max-h-40 overflow-hidden overflow-y-scroll scrollable2 flex flex-col gap-1'>
            {filteredValues?.length === 0 ? (
              <div className='text-center text-sm text-gray-500 py-2'>Ничего не найдено</div>
            ) : (
              filteredValues?.map((val) => {
                // Fix for className issue - use ternary instead of && operator
                const opacityClass = !val?.is_active ? "opacity-40" : "";
                
                return (

                  <div className={!val?.is_active && 'opacity-40'} key={val?.id}>
                    <FormControlLabel
                      sx={{ margin: '0', display: 'flex', alignItems: 'start' }}
                      control={
                        <Checkbox
                          style={{
                            color: '#15765B',
                            padding: '0 5px'
                          }}
                          name={filter?.name}
                          checked={
                            filters?.dynamics
                              ?.find((el) => el?.id === filter?.id)
                              ?.values?.find((el) => el?.id === val?.id)?.is_selected || false
                          }
                          disabled={
                            !filters?.dynamics
                              ?.find((el) => el?.id === filter?.id)
                              ?.values?.find((el) => el?.id === val?.id)?.is_active || false
                          }
                          onChange={() => handleCheckboxChange(filter?.id, val?.id)}
                        />
                      }
                      label={
                        <div className='flex items-center' data-title={val?.text}>
                          {filter?.type === 'color' && val?.second_color && (
                            <>
                              <span
                                style={{
                                  backgroundColor: val?.color
                                }}
                                className={`min-w-[10px] min-h-[20px] rounded-tl-full rounded-bl-full ${
                                  checkCloseToWhite(val?.color) ? ' border-l border-colGray' : ''
                                }`}
                              ></span>
                              <span
                                style={{
                                  backgroundColor: val?.second_color
                                }}
                                className={`min-w-[10px] min-h-[20px] rounded-tr-full rounded-br-full ${
                                  checkCloseToWhite(val?.second_color)
                                    ? ' border-r border-colGray'
                                    : ''
                                }`}
                              ></span>
                            </>
                          )}
                          {filter?.type === 'color' && !val?.second_color && (
                            <span
                              style={{
                                backgroundColor: val?.color
                              }}
                              className={`min-w-[20px] min-h-[20px] rounded-full ${
                                checkCloseToWhite(val?.color) ? 'border border-colGray' : ''
                              }`}
                            ></span>
                          )}
                          <p className='text-sm font-medium text-colBlack line-clamp-2 break-words ml-1'>
                            {val?.text}
                          </p>
                        </div>
                      }
                    />
                  </div>
                );
              })
            )}
          </div>
        </AccordionDetails>
      </Accordion>
    </div>
  );
}

export default CheckboxFilter;
