// src/pages/ReviewsPage.tsx
// NOTE: Temporary location. Ideally, this would be in src/pages/reviews-page/ui/ReviewsPage.tsx
// NOTE: This file was converted from .jsx. Types are inferred or placeholders.
// TODO: Define RootState and ensure all types match actual data structures.
// TODO: Define or import a proper ReviewType for individual review objects
// TODO: Define ProductVariant, ProductData, ReviewsData based on actual API responses or existing types
import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useParams, useNavigate, Link } from 'react-router-dom';

import { useGetProductQuery, ProductPreview } from '@/entities/product';
import { RatingStars, Review } from '@/entities/review';
import { useGetReviewsQuery } from '@/entities/review/api';
type ReviewType = any; // Placeholder for individual review object type
import { useHasUserReview } from '@/entities/review/lib/hooks';
import { useModal } from '@/features/modals/model/context';
import { scrollToTop } from '@/shared/lib/scrollToTop';
import { Loading } from '@/shared/ui/Loader';
import { Button } from '@/shared/ui';

interface ProductVariant {
  id: string | number;
  slug: string;
  category?: { slug: string };
  [key: string]: any;
}

interface ProductData {
  data?: {
    variants?: ProductVariant[];
  };
}

interface ReviewsData {
  comments?: ReviewType[];
  total_count?: number;
  avg_rating?: string | number;
}

interface RootState {
  user: {
    isAuthenticated: boolean;
  };
  [key: string]: any;
}

export const ReviewsPage: React.FC = () => {
  const { showModal } = useModal();
  const { isAuthenticated } = useSelector((state: RootState) => state.user);
  const params = useParams<{ productId: string }>();
  const navigate = useNavigate();
  const { hasReview } = useHasUserReview(params.productId);

  useEffect(() => {
    scrollToTop();
  }, []);

  const { data: productDataResponse, isLoading: isProductLoading } = useGetProductQuery(
    params.productId
  );
  const productData = productDataResponse as ProductData | undefined;

  const product = productData?.data?.variants?.find(
    (variant: ProductVariant) => variant.slug === params.productId
  );

  const { data: reviewsDataResponse, isLoading: isReviewsLoading } = useGetReviewsQuery(
    product?.id,
    { skip: !product?.id }
  );
  const reviewsData = reviewsDataResponse as ReviewsData | undefined;

  const reviews: ReviewType[] = reviewsData?.comments || [];
  const totalReviews: number = reviewsData?.total_count || 0;
  const averageRating: string = Number(reviewsData?.avg_rating || 0).toFixed(1);

  if (isProductLoading || isReviewsLoading) {
    return <Loading />;
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center text-colDarkGray">
          Товар не найден.{' '}
          <button
            onClick={() => navigate(-1)}
            className="text-colGreen hover:underline"
          >
            Вернуться назад
          </button>
        </div>
      </div>
    );
  }
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="">
        <Link
          to={`/catalog/${product?.category?.slug}/${product?.slug}`}
          className="text-colGreen font-semibold hover:underline"
        >
          К товару
        </Link>
      </div>
      <div className="mb-8">
        <h1 className="text-3xl font-semibold mb-2">Отзывы о товаре</h1>
        <ProductPreview product={product} />
      </div>

      <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <div className="text-2xl font-semibold mr-3 lining-nums proportional-nums">
              {averageRating}
            </div>
            <div className="flex items-center mr-3">
              <RatingStars totalStars={5} initialRating={parseFloat(averageRating)} />
            </div>
            <div className="text-lg text-colDarkGray">
              {totalReviews > 0
                ? `${totalReviews} ${totalReviews === 1 ? 'отзыв' : (totalReviews >= 2 && totalReviews <= 4) ? 'отзыва' : 'отзывов'}`
                : 'Нет отзывов'}
            </div>
          </div>
          {!hasReview && (
            <Button
              onClick={() => {
                if (isAuthenticated) {
                  showModal({ type: 'review', product });
                } else {
                  showModal({ type: 'auth' });
                }
              }}
              variant={'outline'}
              size={'lg'}
              className="py-3 px-6 text-colGreen font-semibold bg-white border-colGreen border rounded cursor-pointer hover:bg-colGreen hover:text-white transition-colors"
            >
              Оставить отзыв
            </Button>
          )}
          {hasReview && (
            <Button
              onClick={() => {}}
              variant={'outline'}
              size={'lg'}
              disabled
            >
              Вы уже оставили отзыв об этом товаре
            </Button>
          )}
        </div>

        {reviews?.length > 0 ? (
          <div className="grid grid-cols-1 gap-6">
            {reviews.map((reviewItem: ReviewType) => (
              <Review key={(reviewItem as any)?.created_at || Math.random()} review={reviewItem} />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-colDarkGray">
            Пока нет отзывов. Будьте первым, кто оставит отзыв!
          </div>
        )}
      </div>

      <button
        onClick={() => navigate(-1)}
        className="text-colGreen hover:underline flex items-center"
      >
        ← Вернуться к товару
      </button>
    </div>
  );
};

export default ReviewsPage;
