import React from 'react';

import {File} from './File';

// Placeholder type for a product file, assuming this structure from the backend.
interface ProductFile {
  id: number | string;
  name: string;
  file: string; // URL to the downloadable file
}

// Placeholder type for the product, focusing only on the 'files' property.
interface Product {
  files?: ProductFile[];
}

interface FilesTabProps {
  product?: Product;
}

const FilesTab: React.FC<FilesTabProps> = ({ product }) => {
  const files = product?.files;

  return (
    <div className="py-5">
      <h3 className="text-2xl font-semibold">Файлы для скачивания</h3>
      {files && files.length > 0 ? (
        <div className="flex flex-col gap-4 mt-4">
          {files.map((file) => (
            <File key={file.id} name={file.name} url={file.file} />
          ))}
        </div>
      ) : (
        <div className="text-lg mt-5 text-gray-500">
          У данного товара нет файлов для скачивания
        </div>
      )}
    </div>
  );
};

export default FilesTab;
