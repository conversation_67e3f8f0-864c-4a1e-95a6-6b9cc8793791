# Widget: Footer

## Purpose

This widget represents the main application footer displayed at the bottom of all pages. It typically contains copyright information, contact details, secondary navigation links, social media links, and potentially a newsletter signup form.

## Composition

*   **Entities:** May use data from `entities/filter` (`useGetBasicFiltersQuery`) to dynamically display links to tag pages.
*   **Features:** Could potentially integrate a newsletter signup feature (`features/newsletter`). Uses the `QuestionForm` helper (which could be part of `features/feedback` or similar) when triggering a question modal.
*   **Shared UI:** Uses `NavLink`, icons, layout utilities.

## Key UI Components (`ui/`)

*   **`Footer.tsx`:** *(Legacy location: `src/components/Footer/Footer.tsx`)* The main component orchestrating the footer layout.
*   **Sub-components (`ui/components/` - *Legacy location: `src/components/Footer/components/`*):**
    *   `FooterLogo.tsx`: Displays the site logo and tagline.
    *   `FooterContacts.tsx`: Shows phone, email, and social media links.
    *   `FooterDesktopMenu.tsx`: Renders the multi-column navigation links for desktop view.
    *   `FooterAbout.tsx`, `FooterBuyer.tsx`, `FooterInfo.tsx`: Accordion-style navigation sections for mobile view.
    *   `FooterForm.tsx`: Newsletter signup form (functionality might need implementation/integration).
    *   `FooterCopyright.tsx`: Displays copyright text and potentially policy links.
    *   `FooterSocials.tsx`: Renders social media icons/links.

## Logic & State

*   The footer component itself is largely presentational.
*   It uses `useState` internally to manage the open/closed state of the mobile accordion menus.
*   It fetches basic filter data (`useGetBasicFiltersQuery`) to dynamically generate links to tag pages in the desktop menu.
*   It interacts with `features/modals` (`useModal`) to trigger the "Ask a Question" modal.

## Usage

*   The `Footer` widget is rendered once within the main application `Layout` component (`src/app/layouts/Layout.tsx`).

## Migration Notes

*   The entire Footer implementation currently resides in the legacy `src/components/Footer/` directory.
*   **Action:** Move the `Footer.tsx` component and its sub-components (`src/components/Footer/components/`) to `src/widgets/footer/ui/` and `src/widgets/footer/ui/components/` respectively.
*   Update imports accordingly.
*   Add an `index.ts` file in `src/widgets/footer/` to export the main `Footer` component.
*   Consider refactoring the newsletter form (`FooterForm.tsx`) into a dedicated feature (`features/newsletter`) if it involves API calls or complex state.
*   Ensure the "Ask a Question" functionality properly uses the `useModal` hook.
