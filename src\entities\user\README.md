# Entity: User

## Purpose

This entity represents the **User** within the application. It manages user data, authentication state (token, login status), and provides API endpoints related to user information.

## Key Data Points

*   User profile information (name, email, phone, etc.) - defined in `model/types.ts` (`User` interface).
*   Authentication token (`token`).
*   Authentication status (`isAuthenticated`, `isInitialized`).

## Structure

*   **`model/types.ts`:** Defines the `User` interface (profile data) and the `UserState` interface (Redux state shape).
*   **`model/userSlice.ts`:** Redux Toolkit slice (`createSlice`) responsible for managing the `UserState`.
    *   **Reducers:** Includes reducers for `initializeAuth` (checks cookies on load), `setToken` (handles login, saves/removes token), `logout` (clears state and token), `setUserData` (updates user profile info).
    *   **Initial State:** Defines the default user state.
*   **`api/userApi.ts`:** Defines RTK Query endpoints related to user data:
    *   `getUserData`: Query to fetch detailed user profile information along with counts for cart, favorites, and comparison (often called after login or on profile page load). Uses `providesTags`.
    *   `changeUserData`: Mutation to update user profile information on the server. Uses `invalidatesTags` to refetch user data.
*   **`api/types.ts`:** Defines request/response types for the user API endpoints (e.g., `GetUserDataResponse`).
*   **`lib/`:** Contains utility functions and hooks related to the user entity:
    *   `cookies.ts`: Helper functions (`getTokenFromCookies`, `saveTokenToCookies`, `removeTokenFromCookies`) for interacting with the authentication token cookie. Defines `COOKIE_TOKEN_KEY`.
    *   `hooks/useAuth.ts`: A convenient hook (`useAuth`) to access authentication status (`isAuthenticated`) and user data (`user`) from the Redux store.
*   **`index.ts`:** Public API for the entity, exporting types, the slice/actions, API hooks, and utility hooks/functions.

## State Management

*   The `userSlice` is the single source of truth for the user's authentication status and profile data within the application's client state.
*   Authentication state (`token`, `isAuthenticated`) is persisted via HTTP-only cookies managed by `lib/cookies.ts` and synchronized with the Redux state by the slice reducers.
*   `isInitialized` flag indicates whether the initial auth check (reading cookies) has completed on app load.

## Usage

*   **Checking Authentication:** Use the `useAuth` hook (`lib/hooks/useAuth.ts`) or select `isAuthenticated` directly from the Redux store.
*   **Accessing User Data:** Use the `useAuth` hook or select `data` from the Redux store. Fetch fresh data using `useGetUserDataQuery`.
*   **Login/Logout:** Authentication logic (calling login/register mutations) resides in the `features/auth` layer. Successful login/logout dispatches actions from `userSlice` (`setToken`, `logout`).
*   **Protected Routes:** Components like `ProtectedRoute` check `isAuthenticated` state (often via `getTokenFromCookies` or `useAuth`).
*   **API Calls:** RTK Query's `prepareHeaders` function (in `shared/api/api.ts`) reads the `token` from the `userSlice` state to automatically add the Authorization header to requests.

## Related Features

*   `features/auth`: Handles the processes of login, registration, logout, etc., and updates the state managed by this entity.