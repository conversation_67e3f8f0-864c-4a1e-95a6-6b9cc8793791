import { createSelector } from '@reduxjs/toolkit';
import type { RootState } from '@/app/providers/store';

export const selectSearchState = (state: RootState) => state.search;

export const selectSearchTerm = createSelector(
  [selectSearchState],
  (search) => search.searchTerm
);

// This is for backward compatibility with the old code
// that uses isSearching instead of isLoading
export const selectIsSearching = createSelector(
  [selectSearchState],
  (search) => search.isLoading
);

export const selectRecentSearches = createSelector(
  [selectSearchState],
  (search) => search.recentSearches
);

export const selectSuggestions = createSelector(
  [selectSearchState],
  (search) => search.suggestions
);

export const selectIsSuggestionsOpen = createSelector(
  [selectSearchState],
  (search) => search.isSuggestionsOpen
);

export const selectIsSearchLoading = createSelector(
  [selectSearchState],
  (search) => search.isLoading
);

export const selectSearchError = createSelector(
  [selectSearchState],
  (search) => search.error
);

export const selectRecentProductIds = createSelector(
  [selectSearchState],
  (search) => search.recentProductIds
);

export const selectSelectedCategoryId = createSelector(
  [selectSearchState],
  (search) => search.selectedCategoryId
);

export const selectHasRecentSearches = createSelector(
  [selectRecentSearches],
  (recentSearches) => recentSearches.length > 0
);

export const selectHasSuggestions = createSelector(
  [selectSuggestions],
  (suggestions) => (
    suggestions && (
      (Array.isArray(suggestions.products) && suggestions.products.length > 0) ||
      (Array.isArray(suggestions.categories) && suggestions.categories.length > 0)
    )
  )
);

export const selectShouldShowSuggestions = createSelector(
  [selectIsSuggestionsOpen, selectHasSuggestions],
  (isOpen, hasSuggestions) => isOpen && hasSuggestions
);