// src/shared/ui/ErrorBoundary/ErrorBoundary.tsx
import type { ErrorInfo, ReactNode } from 'react';
import type React from 'react';
import { Component } from 'react';

import { useSelector } from 'react-redux';
import { toast } from 'sonner';

import { errorService } from '@/shared/lib/services/errorService';
import { RootState } from '@/app/providers/store';

interface Props {
  children: ReactNode;
  fallback?: ReactNode | ((error: Error, reset: () => void) => ReactNode);
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  resetCondition?: any;
  showToast?: boolean;
  logError?: boolean;
  additionalInfo?: Record<string, any>;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * Error boundary component that catches JS errors in its child component tree
 * and displays a fallback UI
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Update state with error details
    this.setState({
      error,
      errorInfo,
    });

    // Get user info when available
    let userId: string | number | undefined;
    let isAuthenticated = false;
    
    try {
      // Try to get Redux state from window - must be made available in App.tsx
      const state = window.__REDUX_STATE__ as RootState | undefined;
      if (state?.user) {
        isAuthenticated = state.user.isAuthenticated;
        userId = state.user.data?.id;
      }
    } catch (e) {
      // Silently fail if Redux state is not available
    }

    // Only log errors that are not API errors (those are already handled by the API layer)
    const isApiError = error instanceof Error && 
      (error.message.includes('err_code') || error.message.includes('status:'));
    
    if (!isApiError) {
      // Log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Error caught by ErrorBoundary:', error);
        console.error('Component stack:', errorInfo.componentStack);
      }

      // Log to server if enabled
      if (this.props.logError !== false) {
        errorService.logError(
          error, 
          errorInfo, 
          { 
            ...this.props.additionalInfo,
            componentName: this.getComponentNameFromStack(errorInfo.componentStack),
            userId,
            isAuthenticated
          }
        );
      }

      // Call onError callback if provided
      if (this.props.onError) {
        this.props.onError(error, errorInfo);
      }

      // Show toast if enabled and not an API error
      if (this.props.showToast !== false) {
        toast.error('Произошла ошибка', {
          description: this.getErrorMessage(error),
          duration: 5000,
        });
      }
    }
  }

  componentDidUpdate(prevProps: Props): void {
    // Reset error boundary if children or resetCondition change
    if (
      (prevProps.children !== this.props.children || 
       prevProps.resetCondition !== this.props.resetCondition) && 
      this.state.hasError
    ) {
      this.reset();
    }
  }

  /**
   * Extract component name from the component stack trace
   */
  getComponentNameFromStack(componentStack?: string): string {
    if (!componentStack) return 'Unknown';
    
    // Match the first component in the stack
    const match = componentStack.match(/\n\s+at\s+([A-Za-z0-9$_]+)/);
    return match ? match[1] : 'Unknown';
  }

  /**
   * Get user-friendly error message based on error type
   */
  getErrorMessage(error: Error): string {
    if (error instanceof TypeError) {
      return 'Произошла техническая ошибка. Попробуйте обновить страницу.';
    }
    if (error instanceof ReferenceError) {
      return 'Произошла ошибка в работе приложения. Мы уже работаем над её исправлением.';
    }
    if (error instanceof SyntaxError) {
      return 'Произошла ошибка в коде приложения. Наша команда уже работает над исправлением.';
    }
    
    // Use custom message when available, fallback to generic message
    return error.message || 'Произошла неизвестная ошибка';
  }

  /**
   * Reset the error boundary state
   */
  reset = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render(): ReactNode {
    const { hasError, error, errorInfo } = this.state;
    const { children, fallback } = this.props;

    if (hasError && error) {
      // Use custom fallback if provided
      if (typeof fallback === 'function') {
        return fallback(error, this.reset);
      }
      
      // Use provided fallback element
      if (fallback) {
        return fallback;
      }

      // Default fallback UI
      return (
        <div className="p-4 rounded-lg border border-colGreen bg-colSuperLight">
          <h2 className="text-lg font-semibold text-colGreen mb-2">
            Что-то пошло не так
          </h2>
          <div className="text-sm text-colDarkGray mb-4">
            {this.getErrorMessage(error)}
          </div>
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-4">
              <details className="whitespace-pre-wrap font-mono text-xs text-colDarkGray">
                <summary>Детали ошибки</summary>
                <div className="mt-2 p-2 bg-white rounded">
                  <p className="mb-2">Message: {error.message}</p>
                  <p className="mb-2">Stack: {error.stack}</p>
                  <p>Component Stack: {errorInfo?.componentStack}</p>
                </div>
              </details>
            </div>
          )}
          <button
            onClick={this.reset}
            className="mt-4 px-4 py-2 bg-colGreen text-white rounded hover:opacity-90 transition-opacity"
          >
            Попробовать снова
          </button>
        </div>
      );
    }

    return children;
  }
}

/**
 * Make Redux state accessible for error logging
 */
declare global {
  interface Window {
    __REDUX_STATE__?: any;
  }
}

export default ErrorBoundary;
