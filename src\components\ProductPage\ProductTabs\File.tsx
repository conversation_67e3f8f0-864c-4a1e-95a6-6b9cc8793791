import React, { useState } from 'react';

import downloaddoc from '@/shared/assets/icons/doc-download.svg';
import pdficon from '@/shared/assets/icons/pdf-file-icon.png';

interface FileProps {
  name: string;
  url: string;
}

export const File: React.FC<FileProps> = ({ name, url }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <a
      href={url}
      download
      target="_blank"
      rel="noopener noreferrer"
      className="flex items-center cursor-pointer gap-3 p-2 rounded-md hover:bg-gray-100 transition-colors duration-200"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <img src={pdficon} alt="PDF icon" className="w-8 h-8" />
      <span className="text-base font-medium text-gray-800">{name}</span>
      <div className={`transition-opacity duration-200 ${isHovered ? 'opacity-100' : 'opacity-0'}`}>
        <img className="ml-4" src={downloaddoc} alt="Download document" />
      </div>
    </a>
  );
};
