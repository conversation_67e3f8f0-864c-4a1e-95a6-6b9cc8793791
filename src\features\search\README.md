# Feature: Search

## Purpose

This feature provides the primary product and category search functionality for the application, including the search bar UI, type-ahead suggestions, and interaction with search history.

## Key Functionality

*   **Search Input:** Provides a UI element (`SearchBar`) for users to enter search queries.
*   **Suggestions:** Fetches and displays real-time suggestions (products, categories, search history) as the user types (`useSearchSuggestions`, `SearchSuggestions`).
*   **History Management:** Interacts with API endpoints to fetch, add, and clear user search history.
*   **Navigation:** Initiates navigation to the search results page (`/search`) upon submission.

## Components (`ui/`)

*   **`SearchBar` (`ui/SearchBar/SearchBar.tsx`):** The main component combining input and suggestions.
*   **`SearchInput` (`ui/SearchBar/SearchInput.tsx`):** The input field and submit button component (used within SearchBar).
*   **`SearchSuggestions` (`ui/SearchBar/SearchSuggestions.tsx`):** The dropdown displaying suggestions component (used within SearchBar).

## State & Logic (`model/`, `lib/`)

*   **`slice.ts`:** Redux Toolkit slice (`searchSlice`) managing the `SearchState` including `searchTerm`, `suggestions`, `isLoading`, `error`, `recentSearches`, and `isSuggestionsOpen`.
*   **`selectors.ts`:** Selectors for accessing specific parts of the search state from the Redux store.
*   **`types.ts`:** Defines the `SearchState` interface and related types.
*   **`hooks/useSearchSuggestions.ts`:** Custom hook encapsulating the logic for fetching and managing search suggestions via the `getSearchSuggestions` mutation, including debouncing user input.

## API Integration (`api/`)

Integrates with backend endpoints via RTK Query (`searchApi.ts`):

*   **`getSearchSuggestions` (Mutation):** Fetches product, category, and history suggestions based on the current search term.
*   **`getSearchHistory` (Query):** Retrieves the user's recent search terms.
*   **`addToSearchHistory` (Mutation):** Adds a search term to the user's history.
*   **`clearSearchHistory` (Mutation):** Clears the user's search history.
*   **`searchProducts` (Mutation):** Fetches product variants based on search criteria (primarily used by the search results page).
*   **`getSearchFilters` (Mutation):** Fetches available filters based on search criteria (primarily used by the search results page).

## Usage

*   The `SearchBar` component is the primary UI element for this feature and is typically included in the main application header (`widgets/header`).
*   The `useSearchSuggestions` hook powers the live suggestions within the `SearchBar`.
*   State from `searchSlice` controls the display and behavior of the search bar and suggestions.

## Relationship to Search Results Page

The `Search` feature (specifically the `SearchBar` and related hooks/state) focuses on the **input and suggestion** part of the search experience. It captures the user's query and navigates them to the search results page (`/search`).

The **display of search results** and the application of **filters** to those results are handled by the dedicated search results page (`pages/SearchPage.tsx` - *Note: currently implemented in `src/components/Catalog/Search.tsx`, needs moving*) which reads the `q` parameter from the URL and uses the `searchProducts` and `getSearchFilters` mutations.