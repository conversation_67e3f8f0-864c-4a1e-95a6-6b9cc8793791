// src/features/auth/ui/modals/AuthModal.tsx
import React, { useState, useEffect, useRef } from 'react';
import { Modal, Box } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

import { useModal } from '@/features/modals/model/context';
import GxPhoneInput from '@/shared/ui/gx-widget-react/components/GxPhoneInput/GxPhoneInput';
import modalLogo from '@/shared/assets/images/modal-logo.svg';
import { useAuthContext } from '@/entities/user/model/AuthContext';

// Define type for verification result
interface VerificationResult {
  success: boolean;
  sessionId: string;
  error?: string;
}

/**
 * Modal component for user authentication via phone number
 * Provides phone input, verification, and handles success states
 */
export const AuthModal = () => {
  const { hideModal, modalContent, isModalVisible } = useModal();
  const [authSuccessful, setAuthSuccessful] = useState(false);
  const navigate = useNavigate();
  const { authenticateWithPhone } = useAuthContext();

  // Refs for status tracking and component references
  const phoneInputReactRef = useRef(null);
  const hasNavigatedRef = useRef(false);

  /**
   * Handler for when verification is complete with session ID
   * Uses the authenticateWithPhone method from useAuth
   */
  const handleVerificationSuccess = async (verificationResult: { sessionId: string }): Promise<void> => {
    try {
      // Get the redirect path from modal content or use default
      const redirectPath = modalContent?.redirect || '/profile/orders';

      // Use the authenticateWithPhone method to authenticate the user
      const authResult = await authenticateWithPhone(
        verificationResult.sessionId,
        {} // No longer passing redirect_page
      );

      if (authResult.success) {
        // Set auth successful state to trigger success UI
        setAuthSuccessful(true);

        // Immediately hide modal and navigate after a brief delay
        // This improves perceived performance
        setTimeout(() => {
          hideModal();

          if (!hasNavigatedRef.current) {
            hasNavigatedRef.current = true;
            navigate(redirectPath); // Use local redirectPath directly

            // Show success message
            toast.success('Авторизация выполнена успешно', {
              duration: 3000,
              position: 'top-center',
            });
          }
        }, 800);
      } else {
        toast.error(authResult.error || 'Ошибка при входе');
      }
    } catch (error) {
      console.error('[AuthModal] Error handling verification:', error);
      toast.error('Ошибка при входе');
    }
  };

  // Reset state when modal closes
  useEffect(() => {
    if (!isModalVisible) {
      setAuthSuccessful(false);
      hasNavigatedRef.current = false;

      // Reset the phone input component
      if (phoneInputReactRef.current) {
        phoneInputReactRef.current.reset();
      }
    }
  }, [isModalVisible]);

  // Don't render anything if modal isn't visible
  if (!isModalVisible || modalContent?.type !== 'auth') return null;

  return (
    <Modal
      open={true}
      onClose={hideModal}
    >
      <Box className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 lining-nums proportional-nums bg-white rounded-lg border-none outline-none pt-10 pb-8 px-5 mm:py-10 mm:px-8 max-w-[500px] w-[95%] mm:w-full shadow-xl">
        <span
          onClick={hideModal}
          className="absolute top-2 right-2 text-3xl text-colGray font-light cursor-pointer hover:text-colBlack transition-colors p-2"
        >
          &times;
        </span>

        <div className="flex flex-col items-center">
          <img className="w-[130px] mb-6 mx-auto" src={modalLogo} alt="Logo" />

          <h1 className="text-2xl mm:text-3xl text-colBlack text-center font-semibold mb-8">
            Вход или Регистрация
          </h1>

          <div className="w-full mb-6">
            <label
              htmlFor="gx-phone-input"
              className="block text-sm font-medium text-colBlack mb-3"
            >
              Введите телефон для входа или регистрации
            </label>

            <div className="relative">
              <GxPhoneInput
                ref={phoneInputReactRef}
                onVerificationSuccess={handleVerificationSuccess}
                requiredVerification={true}
                className="w-full"
                placeholder="Ваш номер телефона"
              />

              {authSuccessful && (
                <div className="absolute -right-2 -top-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-md animate-fade-in">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </div>
          </div>

          <div className="text-xs text-colDarkGray text-center mt-6 px-4">
            Подтвердив номер телефона, вы соглашаетесь с{' '}
            <a href="/terms" className="text-colGreen hover:underline">
              условиями использования
            </a>
            {' '}и{' '}
            <a href="/privacy" className="text-colGreen hover:underline">
              политикой конфиденциальности
            </a>
          </div>
        </div>
      </Box>
    </Modal>
  );
};

export default AuthModal;
