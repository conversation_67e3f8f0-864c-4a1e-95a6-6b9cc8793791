# Routing Strategy

Client-side routing in this application is handled by **React Router v6**.

## Core Setup

*   **Router Definition:** The main router configuration is defined in `src/app/routing/router.tsx` using `createBrowserRouter` and `createRoutesFromElements`.
*   **Layout:** A primary `Layout` component (`src/app/layouts/Layout.tsx`) wraps most routes, providing the consistent application structure (<PERSON><PERSON>, <PERSON>er, Main Content Area). It includes the main `<Outlet />` where page content is rendered.
*   **Providers:** The `RouterProvider` is wrapped within necessary context providers (Redux `Provider`, `ModalProvider`, `InitializationProvider`) in `src/app/App.tsx`.

## Route Structure

The routing is designed to be intuitive and reflect the application structure:

*   **`/`**: Home page (`pages/Home/Home.tsx`).
*   **`/catalog`**:
    *   `/catalog`: Root catalog page showing top-level categories (`components/Catalog/CatalogRoot.tsx` - *Note: Should be moved to `pages`*).
    *   `/catalog/:categoryId`: Displays products for a specific category (`components/Catalog/Catalog.tsx` - *Note: Should be moved to `pages` and potentially renamed*).
    *   `/catalog/:categoryId/:productId`: Product detail page (`pages/ProductPage/ProductPage.tsx`).
    *   `/catalog/:categoryId/:productId/reviews`: Product reviews page (`pages/Reviews/ReviewsPage.jsx` - *Note: Should likely be integrated into ProductPage or a dedicated reviews widget/feature*).
*   **`/search`**: Search results page (`components/Catalog/Search.tsx` - *Note: Should be moved to `pages`*). Uses query parameter `q`.
*   **`/brands`**: Brand listing or brand-specific products page (`components/Catalog/Brands.tsx` - *Note: Should be moved to `pages`*). Uses query parameter `brands`.
*   **`/tags`**: Tag-specific products page (`components/Catalog/Tags.tsx` - *Note: Should be moved to `pages`*). Uses query parameter `tags`.
*   **`/shopping-cart`**: Cart page (`pages/cart/CartPage.tsx`).
*   **`/checkout`**: Checkout page (`pages/Checkout/CartCheckout.tsx`).
*   **`/favorites`**: Favorites page (`pages/Favorites/Favorites.tsx`).
*   **`/comparison`**: Comparison page (`pages/Comparison/Comparison.tsx`).
*   **`/profile`**: User profile section wrapper (`pages/Profile/Profile.tsx`). Uses nested routes.
    *   `/profile/personal-data`, `/profile/organizations`, etc. render specific content within the profile layout via `<Outlet />`.
*   **`/payment-delivery`, `/warranty`, `/wholesale`, `/contacts`, `/about`, `/faq`**: Static/informational pages.
*   **`*`**: Page Not Found (`pages/PageNotFound/PageNotFound.jsx`).

## Protected Routes

*   The `ProtectedRoute` component (`src/components/ProtectedRoute/ProtectedRoute.jsx` - *Note: Should be moved, likely to `features/auth` or `shared/ui`*) is used to guard routes requiring authentication (like `/profile`).
*   It checks for the presence of an authentication token (via `getTokenFromCookies`).
*   If the user is not authenticated, it redirects to the home page (`/`) and triggers the authentication modal (`AuthModal`) via the `useModal` hook, passing the original intended location (`from`) so the user can be redirected back after successful login.

## Navigation

*   Use the `NavLink` component from `react-router-dom` for navigation links where active styling is desired.
*   Use the `Link` component for standard links.
*   Use the `useNavigate` hook for programmatic navigation within components or logic handlers.

## URL Parameters and State

*   **Route Parameters:** Used for identifying specific resources (e.g., `:categoryId`, `:productId`). Accessed via the `useParams` hook.
*   **Query Parameters:** Used for filtering, sorting, pagination, and search terms (e.g., `/search?q=term`, `/catalog/category?sort_by=price`). Accessed via the `useSearchParams` hook or `useLocation().search`.
*   **Location State:** Used occasionally to pass simple state between routes during navigation (e.g., passing category data to a product list page). Accessed via `useLocation().state`. *Use sparingly, prefer URL parameters for shareable/bookmarkable state.*

## Refactoring Notes

*   Several components currently acting as pages reside in `src/components/Catalog/`. These should be moved to the `src/pages/` directory for proper FSD alignment.
*   The `ProtectedRoute` component should be relocated, potentially to `src/features/auth/ui/` or `src/shared/ui/` depending on whether it contains auth-specific logic beyond just checking the token.
