import type React from 'react';

import { ReviewModal } from '@/entities/review';
import { AuthModal, LogoutModal } from '@/features/auth/ui/modals';
import { ShareCartModal, ShowSharedCartModal } from '@/features/cart-share';
import { FastOrderModal } from '@/features/fast-order';

import {
  ConfirmationModal,
  ModificationAttributesModal,
  ShareModal
} from './modals';

export const ModalManager: React.FC = () => {
  return (
    <>
      <LogoutModal />
      <AuthModal />
      <ShareModal />
      {/* <QuestionModal /> */}
      <ConfirmationModal />
      <ModificationAttributesModal />
      <ShareCartModal />
      <ShowSharedCartModal />
      <FastOrderModal />
      <ReviewModal />
    </>
  );
};
