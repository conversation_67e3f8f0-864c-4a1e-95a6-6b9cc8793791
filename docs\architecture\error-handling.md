# Error Handling Strategy

The application employs a multi-layered approach to error handling, combining global boundaries, API-level interception, and component-specific handling where necessary.

## 1. Global Error Boundary

*   **Component:** `src/shared/ui/ErrorBoundary/ErrorBoundary.tsx` (React Class Component)
*   **Implementation:** Wraps major parts of the application (typically within the main `Layout`). It uses `componentDidCatch` to catch rendering errors in its child component tree.
*   **Functionality:**
    *   Catches JavaScript errors during rendering, in lifecycle methods, and constructors of the component tree below it.
    *   Displays a fallback UI (either a default one or a custom component/function passed via the `fallback` prop).
    *   Provides a `reset` function to the fallback UI to attempt re-rendering the children.
    *   Logs caught errors to the console (in development).
    *   Sends error details (error message, stack trace, component stack, URL, timestamp) to a backend logging endpoint (`/api/Logs/front/error`) via the dedicated `errorService` (`src/shared/lib/services/errorService.ts`), unless `logError={false}` is passed.
    *   Optionally shows a generic error toast notification using `sonner` (controlled by `showToast` prop, defaults to true), *unless* the error is identified as an API error originating from RTK Query (to avoid duplicate notifications).
*   **Wrapper:** `src/shared/ui/ErrorBoundary/ErrorBoundaryWrapper.tsx` provides a simple functional component wrapper to easily apply the `ErrorBoundary` with key dependencies (like `location.pathname` for automatic reset on navigation). It also sets up global error listeners for unhandled rejections and errors.

## 2. API Error Handling (RTK Query)

*   **Location:** Primarily handled within the custom base query wrapper (`loggingBaseQuery`) in `src/shared/api/api.ts`.
*   **Mechanism:**
    *   **Backend-Indicated Errors:** Intercepts successful HTTP responses where the body indicates an error (e.g., `{ success: false, err: "...", err_code: "..." }`). It transforms these into RTK Query error states.
    *   **HTTP Errors:** Catches standard HTTP errors (4xx, 5xx).
*   **Notifications:**
    *   The `loggingBaseQuery` automatically shows error toasts via `sonner` for most API errors (backend-indicated or HTTP 5xx/4xx).
    *   Specific backend error codes listed in `SILENT_ERROR_CODES` within `api.ts` are *excluded* from automatic toasting, allowing components to handle them silently or with custom UI feedback.
*   **Component Level:** Components using RTK Query hooks (`useSomeQuery`, `useSomeMutation`) receive error information (`isError`, `error` properties) which can be used for conditional rendering or displaying specific error messages if needed (beyond the global toast).
*   **`transformErrorResponse`:** Individual mutations can use this option to parse specific backend error structures and return a more standardized or user-friendly error object/message.

## 3. Component-Level Handling

*   **Try/Catch:** Standard `try...catch` blocks can be used within event handlers or asynchronous functions (e.g., inside `onSubmit` handlers) to catch errors specific to that operation.
*   **Form Validation:** React Hook Form handles validation errors, displaying messages next to the relevant form fields. This is configured within the form setup (e.g., using `zodResolver` or validation rules).
*   **Conditional Rendering:** Components can check `isLoading`, `isError`, and `data` states from RTK Query hooks to render loading indicators, error messages, or empty states appropriately.

## Error Logging Service

*   **`errorService` (`src/shared/lib/services/errorService.ts`):** A comprehensive service for error logging that:
    * Captures detailed error information including user context, system information, and route data
    * Logs errors to the backend endpoint `/api/Logs/front/error`
    * Sets up global event listeners for unhandled promise rejections and errors
    * Provides methods for logging custom errors and component errors
    * Maintains a session ID for tracking related errors across page views
*   Used by the global `ErrorBoundary` to report uncaught rendering errors.
*   Can be used elsewhere in the application for logging specific caught errors when needed (`errorService.logCustomError`).

## Global Error Listeners

The `errorService.setupGlobalErrorListeners()` method (called in `ErrorBoundaryWrapper`) sets up:
* Listeners for unhandled promise rejections (`unhandledrejection` event)
* Listeners for uncaught exceptions (`error` event)
* Logic to filter out browser extension errors and third-party script errors

## Strategy Summary

1.  **Catch Unexpected Rendering Errors:** `ErrorBoundary` acts as a safety net for UI crashes, logs them, shows a fallback, and optionally notifies the user via toast.
2.  **Handle API Errors Globally:** RTK Query's base query wrapper intercepts API responses, identifies errors, and generally shows informative toasts, reducing boilerplate in components. Specific API errors can be silenced for custom handling.
3.  **Handle Expected Errors Locally:** Form validation errors, specific API error codes needing unique UI feedback, or errors within specific asynchronous operations are handled within the relevant component or hook using standard `try...catch` or state checks (`isError` from hooks).
4.  **Monitor Global Errors:** Global error listeners catch errors that might otherwise be missed, ensuring comprehensive error tracking.

Last updated: 2025-04-08 12:00 UTC