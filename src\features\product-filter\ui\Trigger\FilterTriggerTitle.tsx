import { ChevronLeft } from 'lucide-react';
import { useProductFilterTitle } from '../../hook/useProductFilterTitle';

export const FilterTriggerTitle = () => {
  const { data, isOpen, toggle, is_show_selected } = useProductFilterTitle();

  return (
    <div
      className='flex justify-between items-center text-[#222] group/filterName cursor-pointer '
      onClick={toggle}
    >
      <span className='font-semibold group-hover/filterName:text-colGreen select-none'>
        {data.name}
      </span>
      <span
        className={`size-2 rounded-full ml-auto mr-2 ${is_show_selected && !isOpen ? 'bg-colGreen' : ''}`}
      />
      <span
        className={`transition-all ${isOpen && 'rotate-[-90deg]'} group-hover/filterName:text-colGreen text-[#d1d1d1]`}
      >
        <ChevronLeft size={20} />
      </span>
    </div>
  );
};
