import { Icon } from '@iconify-icon/react';
import { useDispatch } from 'react-redux';
import { NavLink } from 'react-router-dom';
import { toast } from 'sonner';

import {
  useCheckoutItemDelMutation,
  useCheckoutItemReturnCardMutation,
} from '@/entities/order/api/orderApi';
import { AddToCartButton, QuantityControl } from '@/features/cart';
import { addToCart } from '@/features/cart/model/cartSlice';
import { ComparisonButton } from '@/features/comparison';
import { FavoriteButton } from '@/features/favorite';
import noImg from '@/shared/assets/images/no-image.png';
import {
  PreviewGallery,
  ProductAttributesDisplay,
  useProductCard,
  DeliveryInfo,
} from '@/widgets/product-card';

import { PriceDisplay } from './PriceDisplay';

import type { DeliveryInfo as DeliveryInfoType } from '@/entities/order/api/types';
import type { Product } from '@/entities/product';

interface ProductCardOrderProps {
  product: Product & {
    quantity?: number;
    delivery?: DeliveryInfoType;
  };
  order_number?: string;
  showOrderControls?: boolean;
}

export const ProductCardOrder = ({
  product,
  order_number,
  showOrderControls,
}: ProductCardOrderProps) => {
  const { productInCart, productPrice } = useProductCard(product);
  const dispatch = useDispatch();

  const [removeItem, { isLoading: isRemoving }] = useCheckoutItemDelMutation();
  const [returnToCart, { isLoading: isReturning }] =
    useCheckoutItemReturnCardMutation();

  const handleRemove = async () => {
    if (!order_number) return toast.error('Не удалось определить номер заказа');
    try {
      await removeItem({ order_number, item_id: product.id }).unwrap();
      toast.success('Товар удалён из заказа');
      // Cache invalidation and refetch are now handled by RTK Query tags
    } catch (e) {
      toast.error('Ошибка при удалении товара из заказа');
    }
  };

  const handleReturnToCart = async () => {
    if (!order_number) return toast.error('Не удалось определить номер заказа');
    try {
      await returnToCart({ order_number, item_id: product.id }).unwrap();
      // Add to Redux cart with correct quantity
      dispatch(addToCart({ ...product, quantity: product.quantity }));
      toast.success('Товар возвращён в корзину');
      // Cache invalidation and refetch are now handled by RTK Query tags
    } catch (e) {
      toast.error('Ошибка при возврате товара в корзину');
    }
  };

  return (
    <div className="lg:flex justify-between shadow-md p-2 rounded-md">
      <div className="basis-2/5 mm:flex lg:pr-4 lg:max-w-[800px] w-full">
        {/* <NavLink to={`/catalog/${product?.category?.slug}/${product?.slug}`}>
          <div className="mm:max-w-[180px] min-w-[180px] w-full h-[180px] mm:h-[180px] overflow-hidden rounded-xl relative bg-gray-100">
            <img
              src={product?.files[0]?.medium || noImg}
              className="w-full h-full object-contain"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.onerror = null;
                target.src = noImg;
              }}
              alt={product.name}
            />
            {product?.tags?.length > 0 ? (
              <div className="absolute top-2 w-full px-2 z-10">
                <span
                  style={{
                    color: product.tags[0].text_color,
                    backgroundColor: product.tags[0].background_color,
                  }}
                  className="py-[3px] lg:py-1 px-1.5 lg:px-2 uppercase text-[8px] lg:text-xs font-semibold lg:font-bold rounded-xl"
                >
                  {product.tags[0].text}
                </span>
              </div>
            ) : null}
          </div>
        </NavLink> */}
        <PreviewGallery
          product={product}
          showTags={false}
          showButtons={false}
          className="max-w-[90px] min-w-[90px] max-h-[90px] min-h-[90px] overflow-hidden rounded-xl relative bg-gray-100"
        />

        <div className="mm:pl-5 pt-2 mm:pt-0">
          <p className="text-xs text-colDarkGray flex items-center space-x-2">
            <span>Код товара:</span>
            <span>{product?.sku}</span>
          </p>
          <NavLink
            to={`/catalog/${product?.category?.slug}/${product?.slug}`}
            className="font-bold text-sm break-all hover:underline line-clamp-3 pb-1"
          >
            {product?.groupName} {product?.name}
          </NavLink>

          <ProductAttributesDisplay product={product} showSKU={false} />

          {/* Delivery Information */}
          {product.delivery ? (
            <DeliveryInfo delivery={product.delivery} className="mt-2" />
          ) : null}
        </div>
      </div>
      <div className="basis-1/5 flex justify-start items-start shrink-0 font-bold text-xl">
        {/* <QuantityControl
              product={productInCart}
              enableRemove
              className="w-full"
            /> */}
        {product.quantity % 1 !== 0
          ? product.quantity
          : Math.floor(product.quantity)}{' '}
        {product.price.unit}
      </div>
      <div className="basis-1/5 flex justify-start items-start shrink-0">
        <PriceDisplay variant="default" price={productPrice} />
      </div>
      <div className="flex basis-1/5 justify-between  flex-col items-end">
        <div className="flex justify-between items-center">
          <PriceDisplay variant="total" price={productPrice} />
        </div>
        {showOrderControls ? (
          <div className="flex gap-2">
            <button
              className="z-10 cursor-pointer w-10 h-10 rounded-md bg-colSuperLight flex items-center justify-center transition-all duration-200 hover:scale-110"
              onClick={handleReturnToCart}
              disabled={isReturning}
              title="Вернуть в корзину"
            >
              {isReturning ? (
                <Icon
                  icon="svg-spinners:90-ring-with-bg"
                  width="24"
                  height="24"
                  className="text-colGreen"
                />
              ) : (
                <Icon
                  icon="solar:cart-large-minimalistic-linear"
                  width="24"
                  height="24"
                  className="text-colGreen"
                />
              )}
            </button>
            <button
              className="z-10 cursor-pointer w-10 h-10 rounded-md bg-colSuperLight flex items-center justify-center transition-all duration-200 hover:scale-110"
              onClick={handleRemove}
              disabled={isRemoving}
              title="Удалить из заказа"
            >
              {isRemoving ? (
                <Icon
                  icon="svg-spinners:90-ring-with-bg"
                  width="24"
                  height="24"
                  className="text-colGreen"
                />
              ) : (
                <Icon
                  icon="solar:trash-bin-minimalistic-linear"
                  width="24"
                  height="24"
                  className="text-colGreen"
                />
              )}
            </button>
          </div>
        ) : null}
      </div>
    </div>
  );
};
