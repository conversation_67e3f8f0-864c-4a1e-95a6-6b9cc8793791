// NOTE: Ideally, this would be in a 'ui' subfolder (src/widgets/footer/ui/Footer.tsx)
import React, { useState } from 'react';

import { NavLink } from 'react-router-dom';

import { useGetBasicFiltersQuery } from '@/entities/filter';
// import arrowDown from '@/shared/assets/icons/arrow-black.svg'; // No longer used directly
// import logo from '@/shared/assets/images/logo.svg'; // No longer used directly
// import telegram from '@/shared/assets/images/telegram.svg'; // No longer used directly
// import vk from '@/shared/assets/images/vk.svg'; // No longer used directly
// import whatsapp from '@/shared/assets/images/whatsapp.svg'; // No longer used directly

// TODO: Update import paths if these components are moved to a 'ui' subfolder later
import { FooterLogo } from './FooterLogo';
import { FooterNewsletter } from './FooterNewsletter';
import { FooterMobileNav } from './FooterMobileNav';
import { FooterDesktopNav } from './FooterDesktopNav';
import { FooterCopyrightAndSocials } from './FooterCopyrightAndSocials';

const Footer = () => {
  // const [privacyPolicy, setPrivacyPolicy] = useState(true); // Removed, logic moved to FooterNewsletter
  // const [menus, setMenus] = useState(...); // Removed, logic moved to FooterMobileNav
  const { data: basicFilters } = useGetBasicFiltersQuery();

  return (
    <footer className="pt-10 pb-24 lg:pb-4 md:pt-14 bg-colSuperLight">
      <div className="content">
        <div className="lg:flex justify-between lg:space-x-5 border-b border-colGray pb-10 md:pb-20">
          <div className="lg:max-w-[380px] xl:max-w-[580px] w-full">
            <FooterLogo />
            <FooterMobileNav />
            <FooterNewsletter />
          </div>
          <FooterDesktopNav basicFilters={basicFilters} />
        </div>
        <FooterCopyrightAndSocials />
      </div>
    </footer>
  );
};

export default Footer;
