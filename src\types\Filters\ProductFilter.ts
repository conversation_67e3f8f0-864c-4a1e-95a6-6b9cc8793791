export type ProductFilterType = 'color' | 'text' | 'range' | 'multiple' | 'image';
export type ProductFilterInputType =
  | 'single'
  | 'multiple'
  | 'range'
  | 'toggle'
  | 'text'
  | 'dropdown';

export interface BaseProductFilter {
  type: ProductFilterType;
  input_type: ProductFilterInputType;
  id: number | string;
  is_active: boolean;
  is_hide: boolean;
  name: string;
}

export interface ProductFilterBaseOption {
  id: number | string;
  is_active: boolean;
  is_selected: boolean;
  text: string;
}

export interface ProductFilterColorOption extends ProductFilterBaseOption {
  color: string | null;
  second_color: string | null;
}

export interface ProductFilterColor extends BaseProductFilter {
  type: 'color';
  input_type: 'multiple';
  values: ProductFilterColorOption[];
}

export interface ProductFilterTextOption extends ProductFilterBaseOption {}

export interface ProductFilterText extends BaseProductFilter {
  type: 'text';
  input_type: 'multiple';
  values: ProductFilterTextOption[];
}

export interface ProductFilterVisualOption extends ProductFilterBaseOption {
  image: {
    name: string;
    url: string;
    tiny: string;
    small: string;
    compact: string;
    medium: string;
    large: string;
    width: number;
    height: number;
    alt_text: string;
    type: string;
    date: string;
  };
}

export interface ProductFilterVisual extends BaseProductFilter {
  type: 'image';

  input_type: 'multiple';
  values: ProductFilterVisualOption[];
}

export interface ProductFilterRange extends BaseProductFilter {
  type: 'range';
  input_type: 'range';
  current: {
    to: number;
    from: number;
  };
  limits: {
    min: number;
    max: number;
    step: number;
  };
  max: number;
  min: number;
}

type ProductFilterMap = {
  multiple: ProductFilterColor | ProductFilterText | ProductFilterVisual;
  range: ProductFilterRange;
};

export type ProductFilter = ProductFilterMap[keyof ProductFilterMap];
export type ProductFilterMultiple = ProductFilterMap['multiple'];
