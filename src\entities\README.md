# Layer: `entities`

## Purpose

The `entities` layer represents the core business domain objects of the application. It defines the shape of the data, how to fetch and manipulate that data via APIs, and potentially simple UI representations of these entities.

## Responsibilities

* **Data Modeling:** Defining the structure of core business entities using TypeScript interfaces or types (e.g., `Product`, `User`, `Order`, `Category`).
* **API Interaction:** Defining RTK Query endpoints (primarily `queries` for fetching entity data, but also mutations for basic CRUD operations) related to specific entities.
* **State Management (Optional):** Managing state directly related to the entity itself (e.g., the current user's data in `userSlice`).
* **UI (Simple Representations):** Providing basic, reusable UI components that directly visualize an entity or a part of it (e.g., `UserAvatar`, `ProductPreview`, `RatingStars`). These components should be display-focused with minimal logic.

## Structure

Entities are organized into slices based on the business domain object they represent. Each entity slice typically follows this structure:

```
src/entities/
├── product/                  # Product entity slice
│   ├── model/                # Data model and state
│   │   ├── types.ts
│   │   └── productSlice.ts   # (if entity needs state management)
│   ├── api/                  # API endpoints
│   │   ├── productApi.ts
│   │   └── types.ts
│   ├── ui/                   # Simple entity representations
│   │   └── ProductPreview.tsx
│   ├── lib/                  # Helper functions
│   │   └── formatters.ts
│   ├── index.ts              # Public API exports
│   └── README.md             # Documentation
└── ...
```

## Dependencies

* Entities can depend only on `shared`.
* Entities **cannot** depend on `app`, `pages`, `widgets`, `features`, or other `entities`.
  * *Exception:* An entity might reference another entity's *type* (e.g., a `Product` entity might have a `Category` type), but it should not import hooks, components, or API logic from another entity slice directly. Relationships are typically handled by the backend API responses or orchestrated in higher layers (`features`, `widgets`).

## Examples in Furnica

* `src/entities/product/`: Defines the `Product` type, `productApi.ts` (for `getProduct`, `getVariants`), and potentially UI like `ProductPreview.tsx`.
* `src/entities/user/`: Defines the `User` type, `userSlice.ts` (for auth state), `userApi.ts` (for `getUserData`).
* `src/entities/order/`: Defines the `Order` type, `orderApi.ts` (for fetching/managing orders).
* `src/entities/category/`: Defines `Category` types, `categoryApi.ts` (for `getCategoryTree`).
* `src/entities/brand/`: Defines `Brand` types, `brandApi.ts`.
* `src/entities/review/`: Defines `Review` types, `reviewApi.ts`, UI like `RatingStars`, `Review`.
* `src/entities/filter/`: Defines filter-related types and potentially the `filterApi` for fetching filter options.
* `src/entities/price/`: Defines `PriceType`, `priceApi` for price lookups.
* `src/entities/tag/`: Defines `Tag` types, `tagApi`.
* `src/entities/organization/`: Defines `Organization` types, `organizationEndpoints`.

## Key Characteristics

1. **Domain Model Focus:** Entities represent distinct business objects in the application domain.
2. **Data Centric:** Entities are primarily concerned with data structure, fetching, and simple manipulation.
3. **Reusable:** Entity types, API hooks, and simple UI representations should be reusable across many features.
4. **Independent:** Entities should not depend on specific UI features or application flows.

## Best Practices

1. **Single Responsibility:** Each entity should represent a single business domain object.
2. **Clear Typing:** Define comprehensive TypeScript interfaces that accurately model the entity's data structure.
3. **API Centralization:** All API endpoints related to an entity should be defined within that entity's `api` segment.
4. **Minimal UI:** UI components in the entity layer should be simple and focused on representing the entity data, without complex interactions.
5. **Clear API Boundaries:** Define a clear public API through the `index.ts` file, exporting only what's needed by higher layers.
6. **Co-location:** Keep all related parts of an entity (types, API, UI) together in the same slice.
7. **Consistent Naming:**
   * Entity folders should be named using kebab-case (`product`, `order`)
   * Types/interfaces should use PascalCase (`Product`, `Order`)
   * API endpoint files should use camelCase (`productApi.ts`)
   * UI components should use PascalCase (`ProductPreview.tsx`)
8. **Document Clearly:** Each entity should have its own README.md explaining its purpose, data structure, and API.

## Common Segments

* **`model/`**: Contains TypeScript types/interfaces defining the entity structure (`types.ts` or the entity file itself like `Product.ts`) and potentially Redux state slices if the entity's state is managed globally (e.g., `userSlice.ts`).
* **`api/`**: Contains RTK Query endpoint definitions for fetching and manipulating this entity's data. Also includes related API request/response types.
* **`ui/`**: Contains simple React components for displaying the entity or its parts.
* **`lib/`**: Helper functions or hooks specifically related to processing or working with this entity's data.

## Migration Notes

When refactoring legacy code:
* Identify code that defines, fetches, or displays business domain objects
* Extract data models (types) into `model/types.ts`
* Move API endpoints into `api/{entityName}Api.ts`
* Extract simple display components into `ui/`
* Ensure entity code doesn't depend on features or other entities
* Update imports to use the new structure
