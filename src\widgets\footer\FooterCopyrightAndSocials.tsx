// src/widgets/footer/FooterCopyrightAndSocials.tsx (Temporary location)
import React from 'react';
import { NavLink } from 'react-router-dom';
import telegram from '@/shared/assets/images/telegram.svg';
import vk from '@/shared/assets/images/vk.svg';
import whatsapp from '@/shared/assets/images/whatsapp.svg';

export const FooterCopyrightAndSocials: React.FC = () => {
  return (
    <div className="pt-5 md:pt-10 md:flex justify-between items-center">
      <p className="text-colDarkGray text-xs pb-5 md:pb-0">
        © FURNICA, {new Date().getFullYear()} {/* Dynamically set year */}
      </p>
      <div className="flex justify-start items-center space-x-4 md:space-x-3 pb-5 md:pb-0">
        <NavLink to="#"> {/* TODO: Add actual social links */}
          <img src={telegram} alt="Telegram" />
        </NavLink>
        <NavLink to="#"> {/* TODO: Add actual social links */}
          <img src={whatsapp} alt="WhatsApp" />
        </NavLink>
        <NavLink to="#"> {/* TODO: Add actual social links */}
          <img src={vk} alt="VK" />
        </NavLink>
      </div>
      <NavLink className="text-colDarkGray text-xs hover:text-colGreen" to="/privacy-policy"> {/* TODO: Add actual privacy policy link */}
        Политика конфиденциальности
      </NavLink>
    </div>
  );
};
