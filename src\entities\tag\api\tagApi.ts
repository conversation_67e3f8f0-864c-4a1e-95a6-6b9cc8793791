import { api } from '@/shared/api/api';
import type { AdditionalServerResponseData } from '@/shared/types/AdditionalServerResponseData';

// Updated Tag type to match the new structure with id property
interface Tag {
  id: number;
  tag: string;
  text_color: string;
  background_color: string;
  description?: string;
  light_icon?: Record<string, any>;
  dark_icon?: Record<string, any>;
}

interface GetTagResponse extends AdditionalServerResponseData {
  data: Tag;
}

export const tagApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getTag: builder.query<Tag, string>({
      query: (id) => `/api/Tags/${id}`,
      transformResponse: (response: GetTagResponse) => response.data,
      providesTags: (result, error, id) => [{ type: 'Tag', id }],
      keepUnusedDataFor: 60,
    }),
  }),
});

export const { useGetTagQuery } = tagApi;
