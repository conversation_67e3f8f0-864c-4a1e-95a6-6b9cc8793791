// src/pages/DeliveryPage.tsx
// NOTE: Temporary location. Ideally, this would be in src/pages/delivery-page/ui/DeliveryPage.tsx
import React, { useEffect, useState } from 'react';

import boxicon from '@/shared/assets/icons/box-icon.svg';
import stallicon from '@/shared/assets/icons/stall-icon.svg';
import truckicon from '@/shared/assets/icons/truck-icon.svg';
import { scrollToTop } from '@/shared/lib/scrollToTop';

const DeliveryPage: React.FC = () => {
  const [delivery, setDelivery] = useState('pickup');

  useEffect(() => {
    scrollToTop();
  }, []);

  return (
    <div className="content lining-nums ">
      <h3 className="text-2xl my-5 font-semibold">Доставка</h3> {/* Page Title */}

      <h4 className="text-xl mt-5 mb-[10px] font-semibold">
        Способы получения
      </h4>
      <div className="flex flex-col md:flex-row gap-5 mb-[40px]"> {/* Adjusted for responsiveness */}
        <div className="flex flex-col gap-5 basis-full md:basis-[calc(30%-10px)]"> {/* Adjusted for responsiveness */}
          <div
            className={`flex p-5 border ${delivery === 'pickup' ? 'border-colGreen' : 'border-colLightGray'} rounded-[10px] gap-3 cursor-pointer`}
            onClick={() => setDelivery('pickup')}
          >
            <div className="mb-[10px]">
              <img className="w-10 h-10" src={stallicon} alt="Самовывоз" />
            </div>
            <div>
              <div className="mb-[10px] font-semibold mr-2 text-xl text-colGreen">
                Самовывоз
              </div>
              <div className="text-sm">Из 255 магазинов и складов</div>
            </div>
          </div>
          <div
            className={`flex p-5 border ${delivery === 'courier' ? 'border-colGreen' : 'border-colLightGray'} rounded-[10px] gap-3 cursor-pointer`}
            onClick={() => setDelivery('courier')}
          >
            <div className="mb-[10px]">
              <img className="w-10 h-10" src={truckicon} alt="Адресная доставка" />
            </div>
            <div>
              <div className="mb-[10px] font-semibold mr-2 text-xl text-colGreen">
                Адресная доставка — Росток
              </div>
              <div className="text-sm">Доставка по всей России</div>
            </div>
          </div>
          <div
            className={`flex p-5 border ${delivery === 'tk' ? 'border-colGreen' : 'border-colLightGray'} rounded-[10px] gap-3 cursor-pointer`}
            onClick={() => setDelivery('tk')}
          >
            <div className="mb-[10px]">
              <img className="w-10 h-10" src={boxicon} alt="Транспортная компания" />
            </div>
            <div>
              <div className="mb-[10px] font-semibold mr-2 text-xl text-colGreen">
                Транспортной компанией
              </div>
              <div className="text-sm">Доставка по всей России</div>
            </div>
          </div>
        </div>

        <div className="bg-colSuperLight h-full p-5 rounded-[10px] basis-full md:basis-[calc(70%-10px)]"> {/* Adjusted for responsiveness */}
          {delivery === 'pickup' ? (
            <>
              <div className=" text-xl font-semibold mb-5">Правила самовывоза</div>
              <div className=" font-semibold mb-1">На сайте</div>
              <div className=" mb-4">
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>
                  При оформлении заказа на сайте выберите способ доставки «Самовывоз»
                </div>
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>
                  Выберите ближайший к вам филиал «РОСТОК»
                </div>
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>
                  После обработки заказа менеджер свяжется с вами для уточнения деталей и времени самовывоза
                </div>
              </div>
              <div className=" font-semibold mb-1">В филиале</div>
              <div className=" mb-4">
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>
                  Вы можете самостоятельно забрать товар в рабочее время филиала
                </div>
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>
                  Необходимо назвать номер заказа или ФИО получателя
                </div>
              </div>
            </>
          ) : null}
          {delivery === 'courier' ? (
            <>
              <div className=" text-xl font-semibold mb-5">Правила адресной доставки</div>
              <div className=" font-semibold mb-1">Условия</div>
              <div className=" mb-4">
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>
                  Доставка осуществляется по указанному адресу в согласованное время
                </div>
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>
                  Стоимость доставки рассчитывается индивидуально в зависимости от региона и объема заказа
                </div>
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>
                  Менеджер свяжется с вами для подтверждения деталей доставки и расчета стоимости
                </div>
              </div>
            </>
          ) : null}
          {delivery === 'tk' ? (
            <>
              <div className=" text-xl font-semibold mb-5">Правила доставки транспортной компанией</div>
              <div className=" font-semibold mb-1">Условия</div>
              <div className=" mb-4">
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>
                  Доставка осуществляется до терминала транспортной компании в вашем городе или по указанному адресу (согласно условиям ТК)
                </div>
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>
                  Стоимость доставки и услуги транспортной компании оплачиваются отдельно при получении заказа
                </div>
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>
                  Мы сотрудничаем с ведущими транспортными компаниями: ПЭК, Деловые Линии, СДЭК и другими
                </div>
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>
                  Менеджер поможет выбрать оптимальную транспортную компанию и рассчитать примерную стоимость доставки
                </div>
              </div>
            </>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default DeliveryPage;
