# Feature: Authentication

## Purpose

This feature handles all aspects of user authentication, including registration, login, password management, and phone verification.

## Key Functionality

*   **Login:** Allows existing users to log in using email/password or phone number verification.
*   **Registration:** Enables new users to register an account.
*   **Password Reset:** Provides a flow for users to reset forgotten passwords via email.
*   **Password Change:** Allows logged-in users to change their current password.
*   **Phone Verification:** Implements SMS code verification for phone number confirmation during registration or login.
*   **Session Management:** Interacts with the `user` entity (`userSlice`) to set/clear authentication tokens and user data upon login/logout.
*   **Data Synchronization:** Coordinates with `useSyncUserData` hook to synchronize local cart/favorites/comparison data with the server upon successful login.

## Components (`ui/`)

*   **Modals:**
    *   `AuthModal.tsx`: Main entry point modal, often using the GX Phone Widget.
    *   `CheckAuth.tsx`: Initial step to check if a user exists (email/phone).
    *   `AuthWithEmail.tsx`: Form for email/password login.
    *   `Register.tsx`: User registration form.
    *   `ResetPassword.tsx`: Form to request a password reset link.
    *   `LogoutModal.tsx`: Confirmation modal for logging out.
*   **Form Components:**
    *   `PasswordInput.tsx`: Reusable password input field with visibility toggle.
    *   `PhoneVerificationField.tsx`: Encapsulates the phone input and SMS code verification logic (integrates with `usePhoneVerification` hook).

## State & Logic (`model/`, `lib/`)

*   **Interaction with `userSlice`:** Dispatches `setToken`, `setUserData`, `logout` actions from `src/entities/user/model/userSlice.ts`.
*   **`useAuthWithEmail.ts`:** Hook containing logic for handling email/password authentication flow.
*   **`useRegister.ts`:** Hook containing logic for handling user registration flow.
*   **`useSyncUserData.ts`:** Hook responsible for syncing local data (cart, favs, comparison) with the server after login.
*   **Validation (`lib/validation/`):** Contains Zod schemas (e.g., `registerSchema.ts`) for form validation.
*   **Error Constants (`lib/errors.ts`):** Defines common authentication error messages/codes.

## API Integration (`api/`)

*   **`authenticationApi.ts`:** Defines RTK Query mutations for primary auth actions (check, auth, register, reset/change password, phone entry).
*   **`verificationApi.ts`:** Defines RTK Query mutations for sending and confirming SMS verification codes.

## Usage

*   Authentication modals are typically triggered globally via the `useModal` hook from `src/features/modals/`.
*   The `AuthModal` or `CheckAuth` component is often the initial modal shown when authentication is required.
*   Other components (like `LoginButton`, `ProfileButton`) might trigger specific auth modals.
*   `ProtectedRoute` relies on the authentication state managed by `userSlice`.