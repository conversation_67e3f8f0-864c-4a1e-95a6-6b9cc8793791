# Feature: Modals

## Purpose

This feature provides a centralized system for managing and displaying various modal dialogs across the application. It uses a React Context API approach to decouple modal triggering from modal rendering.

## Key Functionality

*   **Global Modal State:** Manages which modal (if any) is currently visible and its associated content/props.
*   **Modal Triggering:** Provides a `useModal` hook with `showModal` and `hideModal` functions that can be called from any component within the `ModalProvider` context.
*   **Centralized Rendering:** The `ModalManager` component renders the appropriate modal based on the current state managed by the context. This keeps modal rendering logic separate from the components that trigger them.
*   **Type Safety:** Uses TypeScript (`model/types.ts`) to define the structure and props for different types of modals, ensuring consistency.

## Components (`ui/`)

*   **`ModalManager.tsx`:** The central component responsible for rendering the currently active modal based on the context state. It imports and conditionally renders all specific modal components.
*   **Specific Modals (`ui/modals/`):** Contains the actual implementations for each type of modal dialog defined in `model/types.ts`. Examples:
    *   `AuthModal.tsx` (delegates to `features/auth`)
    *   `LogoutModal.tsx` (delegates to `features/auth`)
    *   `ShareModal.tsx`
    *   `ConfirmationModal.tsx`
    *   `QuestionModal.tsx`
    *   `FastOrderModal.tsx` (delegates to `features/fast-order`)
    *   `ShareCartModal.tsx` (delegates to `features/cart-share`)
    *   `ShowSharedCartModal.tsx` (delegates to `features/cart-share`)
    *   `ReviewModal.tsx` (delegates to `entities/review`)
    *   And others...

## State & Logic (`model/`)

*   **`context.tsx`:** Defines the `ModalContext`, `ModalProvider` component, and the `useModal` hook.
    *   `ModalProvider`: Wraps the application (usually in `Layout.tsx` or `App.tsx`) and holds the state for `modalContent` and `isModalVisible`.
    *   `useModal`: Hook used by components to access `showModal`, `hideModal`, `modalContent`, and `isModalVisible`.
*   **`types.ts`:** Defines the `ModalTypes` mapped type, which specifies the unique `type` identifier and the expected props for each kind of modal dialog in the application.

## API Integration

This feature itself doesn't directly interact with APIs. However, the *specific modal components* it renders often contain logic that triggers API calls (e.g., `AuthModal` triggers login mutations, `FastOrderModal` triggers the one-click order mutation).

## Usage

1.  **Wrap the App:** Ensure the main application component (or relevant layout) is wrapped with `ModalProvider`.
    ```tsx
    // In src/app/layouts/Layout.tsx (example)
    import { ModalProvider } from '@/features/modals';
    // ...
    <ModalProvider>
      {/* Rest of the layout */}
      <ModalManager /> {/* Render the manager */}
    </ModalProvider>
    ```
2.  **Trigger Modals:** Use the `useModal` hook in any component that needs to open a modal.
    ```tsx
    import { useModal } from '@/features/modals';

    const MyComponent = () => {
      const { showModal } = useModal();

      const handleOpenLogin = () => {
        showModal({ type: 'auth', title: 'Вход / Регистрация' });
      };

      const handleConfirmDelete = () => {
        showModal({
          type: 'confirmation',
          title: 'Подтверждение',
          text: 'Вы уверены, что хотите удалить?',
          action: async () => { /* delete logic */ }
        });
      };

      return (
        <button onClick={handleOpenLogin}>Login</button>
        <button onClick={handleConfirmDelete}>Delete</button>
      );
    };
    ```
3.  **Implement Modals:** Create specific modal components within `src/features/modals/ui/modals/` that consume the props defined in `model/types.ts` for their respective `type`. Register these components within `ModalManager.tsx`.

## Benefits

*   **Decoupling:** Components triggering modals don't need to know about the modal's implementation details.
*   **Centralization:** Modal rendering logic is centralized in `ModalManager`.
*   **Consistency:** Type definitions ensure modals are called with the correct props.
*   **Reusability:** Common modal patterns (like `ConfirmationModal`) can be easily reused.