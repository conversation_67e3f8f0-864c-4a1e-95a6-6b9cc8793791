# Layer: `pages`

## Purpose

The `pages` layer is responsible for constructing complete pages of the application by composing components from lower layers (`widgets`, `features`, `entities`, `shared`). Each file typically corresponds to a specific route.

## Responsibilities

* **Routing Composition:** Defining the component rendered for a specific application route (as configured in `src/app/routing/router.tsx`).
* **Layout Assembly:** Importing and arranging `widgets` (like <PERSON>er, Footer, Sidebars) and `features` to create the structure and content of a specific page.
* **Data Fetching Initiation:** Pages often initiate top-level data fetching for the page's primary content using hooks from `features` or `entities` (e.g., fetching product details on a product page, fetching orders on an orders page).
* **Minimal Business Logic:** Pages should contain minimal business logic themselves. Logic should primarily reside within `features` or `entities`. A page might handle simple state related to its own layout (e.g., sidebar visibility toggle) but not core application or domain logic.

## Structure

* Each file typically represents a page (e.g., `HomePage.tsx`, `ProductPage.tsx`, `CartPage.tsx`).
* Subdirectories might be used for pages with complex sub-routes or closely related auxiliary components.
* Page components often follow this pattern:
  ```tsx
  const ProductPage = () => {
    // 1. Fetch necessary data using entity/feature hooks
    const { data, isLoading } = useGetProductQuery(productId);
    
    // 2. Handle loading/error states
    if (isLoading) return <Skeleton />;
    if (!data) return <NotFound />;
    
    // 3. Compose the page using widgets and features
    return (
      <div className="page-container">
        <Breadcrumbs />
        <ProductPageContent product={data} />
        <RelatedProducts ids={data.relatedIds} />
      </div>
    );
  };
  ```

## Dependencies

* Pages can depend on `widgets`, `features`, `entities`, and `shared`.
* Pages **cannot** depend on `app` or other `pages`.

## Examples in Furnica

* `src/pages/Home/Home.tsx`: Composes various widgets (`Banner`, `PopularCategories`, `Promotions`, etc.) for the main landing page.
* `src/pages/ProductPage/ProductPage.tsx`: Fetches product data and orchestrates the display using `ProductPageDesktop` or `ProductPageMobile` widgets.
* `src/pages/cart/CartPage.tsx`: Renders the main `Cart` feature component.
* `src/pages/Profile/Profile.tsx`: Provides the layout for the profile section and renders nested routes via `<Outlet />`.
* `src/pages/About/About.tsx`, `src/pages/Contacts/Contacts.tsx`, etc.: Primarily display static or fetched content, potentially using shared components or simple widgets.

## Best Practices

1. **Keep Pages Thin:** Pages should primarily compose other components, with minimal logic of their own.
2. **Consistent Naming:** Use the suffix `Page` (e.g., `ProductPage`, `CheckoutPage`) to clearly identify page components.
3. **Consistent Structure:**
   - Use a descriptive folder name (`Checkout/`)
   - Main page component in a file with the same name (`CheckoutPage.tsx`)
   - Any page-specific components in separate files within the same folder.
4. **Handle Loading and Error States:** Use skeletons, error messages, or redirects appropriately.
5. **Avoid Page-to-Page Communication:** If two pages need to share state, that state should likely be managed at the `feature` or `entity` level.
6. **Extract Repeated Logic:** If similar logic is needed across multiple pages, extract it to a custom hook in the appropriate `feature` or `entity` layer.

## Migration Notes

Various components currently located in legacy directories (like `src/components/Catalog/`) that function as pages should be moved into the `src/pages/` directory, following the FSD structure and conventions described above.
