// src/shared/lib/services/errorService.ts
import axios from 'axios';

/**
 * Interface for error log data sent to the server
 */
export interface ErrorLogData {
  // Error information
  message: string;
  stack?: string;
  componentStack?: string;
  type: string;
  
  // Context information
  url: string;
  route: string;
  timestamp: string;
  
  // User information
  userAgent: string;
  screenSize: string;
  isAuthenticated: boolean;
  userId?: string | number;
  
  // System information
  sessionId: string;
  referrer: string;
  
  // Additional custom data
  additionalInfo?: Record<string, any>;
}

// Create a dedicated error tracking instance with appropriate timeouts
const errorReportingClient = axios.create({
  baseURL: 'https://rosstok.ru',
  timeout: 10000, // 10 seconds timeout for error reporting
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Generate a session ID for tracking related errors
 * This ID persists for the browser session
 */
const getSessionId = (): string => {
  // Use existing session ID or create a new one
  let sessionId = sessionStorage.getItem('error_session_id');
  if (!sessionId) {
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
    sessionStorage.setItem('error_session_id', sessionId);
  }
  return sessionId;
};

/**
 * Service for handling error logging and reporting
 */
export const errorService = {
  /**
   * Log an error to the server
   * 
   * @param error The error object
   * @param errorInfo Additional React error info (from componentDidCatch)
   * @param additionalInfo Custom data to include in the error report
   */
  async logError(
    error: Error,
    errorInfo?: React.ErrorInfo,
    additionalInfo?: Record<string, any>
  ): Promise<void> {
    try {
      // Get user information from Redux store if available
      let userId: string | number | undefined;
      let isAuthenticated = false;
      
      try {
        // Try to access Redux store - wrap in try/catch to avoid errors if not available
        const state = window.__REDUX_STATE__;
        if (state?.user) {
          isAuthenticated = state.user.isAuthenticated || false;
          userId = state.user.data?.id;
        }
      } catch (storeError) {
        console.debug('Could not access Redux state:', storeError);
      }

      // Build error data object
      const errorData: ErrorLogData = {
        // Error details
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo?.componentStack,
        type: error.constructor.name,
        
        // Context information
        url: window.location.href,
        route: window.location.pathname,
        timestamp: new Date().toISOString(),
        
        // User information
        userAgent: window.navigator.userAgent,
        screenSize: `${window.innerWidth}x${window.innerHeight}`,
        isAuthenticated,
        ...(userId ? { userId } : {}),
        
        // System information
        sessionId: getSessionId(),
        referrer: document.referrer || 'direct',
        
        // Additional info
        ...(additionalInfo ? { additionalInfo } : {}),
      };

      // Send error data to API
      await errorReportingClient.post('/api/Logs/front/error', errorData);
      
      if (process.env.NODE_ENV === 'development') {
        console.log('🚨 Error logged successfully:', errorData);
      }
    } catch (loggingError) {
      // Fail silently in production, log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Error logging failed:', loggingError);
      }
    }
  },

  /**
   * Log a custom error message with additional context
   * 
   * @param message Error message
   * @param additionalInfo Additional context data
   */
  async logCustomError(
    message: string,
    additionalInfo?: Record<string, any>
  ): Promise<void> {
    const error = new Error(message);
    await this.logError(error, undefined, additionalInfo);
  },
  
  /**
   * Track unhandled promise rejections
   */
  setupGlobalErrorListeners(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      const error = event.reason instanceof Error 
        ? event.reason 
        : new Error(String(event.reason));
        
      this.logError(error, undefined, { 
        type: 'unhandledRejection',
        value: String(event.reason)
      });
    });
    
    // Add global error handler for uncaught exceptions
    window.addEventListener('error', (event) => {
      // Ignore errors from browser extensions and cross-origin scripts
      if (event.filename && !event.filename.includes(window.location.origin) && 
          !event.filename.startsWith('blob:') && 
          !event.filename.startsWith('http://localhost')) {
        return;
      }
      
      const error = event.error || new Error(event.message);
      this.logError(error, undefined, {
        type: 'uncaughtException',
        source: event.filename,
        lineNo: event.lineno,
        colNo: event.colno
      });
    });
  }
};

// For TypeScript globals
declare global {
  interface Window {
    __REDUX_STATE__?: any;
  }
}
