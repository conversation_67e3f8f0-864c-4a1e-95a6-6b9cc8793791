import { Details, Share } from '@/shared/ui';

export const ExploreBrands = () => {
  return (
    <div className='w-full h-[100px] p-[25px] border border-[#E6E9E8] bg-[#fff] rounded-[8px] flex gap-[12px] items-center'>
      <div className='max-h-[50px] h-[50px]'>
        <img
          src='https://dev.furnica.store/files/loaded/6813724ea2e93/size_250_57430599_photo_2025-05-01_16-08-00.webp'
          alt=''
          className='h-full w-full'
        />
      </div>
      <div>
        <h2 className='text-[#222222] leading-[1.2] text-[24px] font-bold'>ООО БОЯРД</h2>
        <span className='leading-[1.2] text-[16px] text-[#757575] font-bold'>Бренд</span>
      </div>
      <div className='ml-auto flex gap-[12px]'>
        <button className='p-[8px] bg-[#DCE6E3] rounded-[8px] text-[#414947] hover:text-colGreen '>
          <Details />
        </button>
        <button className='p-[8px] bg-[#DCE6E3] rounded-[8px] text-[#414947] hover:text-colGreen '>
          <Share />
        </button>
      </div>
    </div>
  );
};
