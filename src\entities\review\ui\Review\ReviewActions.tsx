import type React from 'react';
import { useState } from 'react';

import { MoreVert } from '@mui/icons-material';
import { Menu, MenuItem, IconButton } from '@mui/material';
import { toast } from 'sonner';

import { useDeleteSelfReviewMutation } from '@/entities/review';
import { useModal } from '@/features/modals/model/context';
import { Button } from '@/shared/ui';

import type { Review } from '../../api/types';

interface ReviewActionsProps {
  review: Review;
  variant_id: number;
}

export const ReviewActions = ({ review, variant_id }: ReviewActionsProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { showModal } = useModal();
  const [deleteReview] = useDeleteSelfReviewMutation();

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    showModal({
      type: 'review',
      variantId: variant_id,
      editReview: review,
    });
    handleMenuClose();
  };

  const handleDelete = async () => {
    try {
      const result = await deleteReview({ review_id: review.review_id }).unwrap();
      if (result.success) {
        toast.success('Отзыв успешно удален');
      } else {
        toast.error('Ошибка при удалении отзыва');
      }
    } catch (err) {
      console.error('Error deleting review:', err);
      toast.error('Произошла ошибка при удалении отзыва');
    }
    handleMenuClose();
  };

  return (
    <>
      <IconButton
        aria-label="more"
        aria-controls="review-menu"
        aria-haspopup="true"
        onClick={handleMenuOpen}
        size="small"
      >
        <MoreVert />
      </IconButton>
      <Menu
        id="review-menu"
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          style: {
            maxHeight: 48 * 4.5,
            width: '200px',
          },
        }}
      >
        <MenuItem onClick={handleEdit} className="flex items-center !p-2">
          <Button variant="link">Редактировать</Button>
        </MenuItem>
        <MenuItem onClick={handleDelete} className="flex items-center !p-2">
          <Button variant="link" className="text-red-500">
            Удалить
          </Button>
        </MenuItem>
      </Menu>
    </>
  );
};
