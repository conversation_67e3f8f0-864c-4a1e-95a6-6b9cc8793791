// src/pages/PaymentPage.tsx (Temporary location)
import React, { useEffect, useState } from 'react';

import { useModal } from '@/features/modals/model/context';
import boxicon from '@/shared/assets/icons/box-icon.svg';
import stallicon from '@/shared/assets/icons/stall-icon.svg';
import truckicon from '@/shared/assets/icons/truck-icon.svg'; 
import { scrollToTop } from '@/shared/lib/scrollToTop';

const PaymentPage: React.FC = () => {
  const [payment, setPayment] = useState('cash');
  const { showModal } = useModal();

  useEffect(() => {
    scrollToTop();
  }, []);

  return (
    <div className="content lining-nums ">
      <h3 className="text-2xl my-5 font-semibold">Оплата</h3>

      <h4 className="text-xl mt-5 mb-[10px] font-semibold ">Способы оплаты</h4>

      <div className="flex flex-col md:flex-row gap-5 mb-[40px]">
        <div className="flex flex-col gap-5 shrink basis-full md:basis-[calc(30%-10px)]">
          <div
            className={`flex p-5 border ${payment === 'cash' ? 'border-colGreen' : 'border-colLightGray'} rounded-[10px] gap-3 cursor-pointer`}
            onClick={() => setPayment('cash')}
          >
            <div className="mb-[10px]">
              <img className="w-10 h-10" src={stallicon} alt="Наличными" />
            </div>
            <div>
              <div className="mb-[10px] font-semibold mr-2 text-xl text-colGreen">
                Наличными
              </div>
              <div className="text-sm">При получении заказа</div>
            </div>
          </div>
          <div
            className={`flex p-5 border ${payment === 'online' ? 'border-colGreen' : 'border-colLightGray'} rounded-[10px] gap-3 cursor-pointer`}
            onClick={() => setPayment('online')}
          >
            <div className="mb-[10px]">
              <img className="w-10 h-10" src={truckicon} alt="Онлайн-оплата" />
            </div>
            <div>
              <div className="mb-[10px] font-semibold mr-2 text-xl text-colGreen">
                Онлайн-оплата
              </div>
              <div className="text-sm">Картами Visa, MasterCard, Мир</div>
            </div>
          </div>
          <div
            className={`flex p-5 border ${payment === 'account' ? 'border-colGreen' : 'border-colLightGray'} rounded-[10px] gap-3 cursor-pointer`}
            onClick={() => setPayment('account')}
          >
            <div className="mb-[10px]">
              <img className="w-10 h-10" src={boxicon} alt="Оплата по счёту" />
            </div>
            <div>
              <div className="mb-[10px] font-semibold mr-2 text-xl text-colGreen">
                Оплата по счёту
              </div>
              <div className="text-sm">Для юридических лиц</div>
            </div>
          </div>
          <button
            className="flex items-center justify-center p-5 border bg-colGreen rounded-[10px] gap-3"
            onClick={() => showModal({ type: 'question' })}
          >
            <div className="font-semibold cursor-pointer mr-2 text-xl text-white">
              Задать вопрос
            </div>
          </button>
        </div>

        <div className="bg-colSuperLight h-full p-5 rounded-[10px] basis-full md:basis-[calc(70%-10px)]">
          {payment === 'cash' ? (
            <>
              <div className=" text-xl font-semibold mb-5">Правила оплаты наличными</div>
              <div className=" mb-4">
                Оплата наличными при получении товара возможна в филиалах компании
                «РОСТОК»
              </div>
            </>
          ) : null}
          {payment === 'online' ? (
            <>
              <div className=" text-xl font-semibold mb-5">Правила онлайн-оплаты</div>
              <div className=" font-semibold mb-1">На сайте</div>
              <div className=" mb-4">
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>{' '}
                  После обработки и согласования заказа менеджер интернет-магазина
                  предоставит реквизиты для оплаты картой
                </div>
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>{' '}
                  Оплатить можно с помощью клиент-банка через интернет, через
                  мобильное приложение банка, терминал самообслуживания
                </div>
              </div>
              <div className=" font-semibold mb-1">В филиале</div>
              <div className=" mb-4">
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>{' '}
                  В филиале можно осуществить оплату картой непосредственно на
                  кассе
                </div>
              </div>
            </>
          ) : null}
          {payment === 'account' ? (
            <>
              <div className=" text-xl font-semibold mb-5">Правила оплаты по счёту</div>
              <div className=" font-semibold mb-1">На сайте</div>
              <div className=" mb-4">
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>{' '}
                  После согласования заказа на сайте необходимо выслать по e-mail
                  реквизиты для формирования безналичного счета
                </div>
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>{' '}
                  Бухгалтерия формирует безналичный счет и менеджер
                  интернет-магазина отправляет его электронной почтой клиенту для
                  оплаты
                </div>
              </div>
              <div className=" font-semibold mb-1">В филиале</div>
              <div className=" mb-4">
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>
                  Вам необходимо предоставить реквизиты для формирования
                  безналичного счета
                </div>
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>
                  Бухгалтер филиала составляет счет и отправляет его на почту
                  клиента, либо отдает непосредственно в руки
                </div>
                <div className="flex gap-2 items-start mb-1">
                  <div className="min-w-2 min-h-2 rounded-full bg-colGreen mt-2"></div>
                  Бухгалтерия формирует безналичный счет и менеджер
                  интернет-магазина отправляет его электронной почтой клиенту для
                  оплаты
                </div>
              </div>
            </>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default PaymentPage;
