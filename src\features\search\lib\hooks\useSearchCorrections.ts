import { useState, useEffect, useCallback } from 'react';
import { useGetSearchCorrectionsQuery } from '../../api/searchApi';
import type { SearchCorrectionItem } from '../../api/types';

export interface UseSearchCorrectionsResult {
  corrections: SearchCorrectionItem[];
  isLoading: boolean;
  isError: boolean;
  applyCorrection: (item: SearchCorrectionItem, currentQuery: string) => string;
}

/**
 * Hook to fetch and manage search corrections/suggestions
 * @param query Current search query
 * @param skipFetch Option to skip the fetch (default: false)
 * @param maxItems Maximum number of corrections to return (default: 10)
 * @param debounceTime Debounce time in milliseconds (default: 300)
 * @returns Object with corrections and helper functions
 */
export const useSearchCorrections = (
  query: string,
  skipFetch: boolean = false,
  maxItems: number = 10,
  debounceTime: number = 300
): UseSearchCorrectionsResult => {
  // We'll use a separate state for the debounced query
  const [debouncedQuery, setDebouncedQuery] = useState('');
  
  // Skip the query if the input is too short or skipFetch is true
  const shouldSkip = debouncedQuery.length < 2 || skipFetch;
  
  const { data, isLoading, isError } = useGetSearchCorrectionsQuery(debouncedQuery, {
    skip: shouldSkip,
    // Refresh the data when the query changes
    refetchOnMountOrArgChange: true,
  });
  
  const [corrections, setCorrections] = useState<SearchCorrectionItem[]>([]);

  // Debounce the query input
  useEffect(() => {
    // Don't set a timeout for empty queries
    if (query.length < 2) {
      setDebouncedQuery('');
      return undefined;
    }
    
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, debounceTime);
    
    // Clean up function
    return () => {
      clearTimeout(timer);
    };
  }, [query, debounceTime]);

  // Update corrections when data changes
  useEffect(() => {
    if (data?.success && Array.isArray(data.data)) {
      // Limit to maxItems
      setCorrections(data.data.slice(0, maxItems));
    } else {
      setCorrections([]);
    }
  }, [data, maxItems]);

  /**
   * Apply a correction to the current query
   * @param item The correction item to apply
   * @param currentQuery The current search query
   * @returns The new query after applying the correction
   */
  const applyCorrection = useCallback((item: SearchCorrectionItem, currentQuery: string): string => {
    const parts = currentQuery.split(' ');
    
    if (item.action === 'replace') {
      // Replace the last word
      if (parts.length > 0) {
        parts[parts.length - 1] = item.word;
      } else {
        // If there are no parts, just set the word
        return item.word;
      }
    } else if (item.action === 'append') {
      // Add the word to the end
      parts.push(item.word);
    }
    
    return parts.join(' ');
  }, []);

  return {
    corrections,
    isLoading,
    isError,
    applyCorrection,
  };
};
