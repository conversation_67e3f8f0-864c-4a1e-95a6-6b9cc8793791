// src/app/providers/InitializationProvider/InitializationProvider.tsx
import type { FC, PropsWithChildren } from 'react';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';

import { initializeAuthAsync, setUserData } from '@/entities/user';
import { useAuthContext } from '@/entities/user/model/AuthContext';
import { useGetUserDataQuery } from '@/entities/user/api/userApi';
// import { useInitialDataSync } from './lib/useInitialDataSync'; // Disabled - using SyncProvider instead
import { LoadingOverlay } from '@/shared/ui/LoadingOverlay/LoadingOverlay';
// import { useSyncContext } from '@/app/providers/SyncProvider'; // Will be used later if needed

import type { AppDispatch } from '@/app/providers/store';

/**
 * Top-level provider that handles:
 * 1. Authentication initialization (simpler approach)
 * 2. User data fetching
 *
 * Note: Data synchronization is now handled by SyncProvider
 * Uses simplified direct token approach.
 */
export const InitializationProvider: FC<PropsWithChildren> = ({ children }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { isInitialized, isAuthenticated } = useAuthContext();
  // const { syncData, isSynced, isDataSyncing } = useInitialDataSync(); // Disabled - using AuthSyncProvider instead

  // For development debugging
  const logInit = (message: string, data = {}) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[InitProvider] ${message}`, {
        isInitialized,
        isAuthenticated,
        // isSynced,
        // isDataSyncing,
        ...data
      });
    }
  };

  // Step 1: Initialize authentication from cookies
  useEffect(() => {
    const initializeApplication = async () => {
      if (!isInitialized) {
        logInit('Starting auth initialization');
        await dispatch(initializeAuthAsync());
        logInit('Auth initialization complete');
      }
    };

    initializeApplication();
  }, [dispatch, isInitialized]);

  // Step 2: Get user data if authenticated
  const { data: userData, isLoading: isUserDataLoading } = useGetUserDataQuery(undefined, {
    skip: !isInitialized || !isAuthenticated,
  });

  // Update user data in Redux when available
  useEffect(() => {
    if (userData?.user && isAuthenticated) {
      logInit('User data received, updating Redux');
      dispatch(setUserData(userData.user));
    }
  }, [userData, dispatch, isAuthenticated]);

  // Step 3: Data sync is now handled by SyncProvider
  // (No longer needed here)

  // Only show loading overlay for critical initialization steps
  const isLoading = !isInitialized || isUserDataLoading;

  return (
    <>
      {children}

      {/* Critical initialization - blocks UI for essential app functionality */}
      {isLoading ? <LoadingOverlay /> : null}
    </>
  );
};
