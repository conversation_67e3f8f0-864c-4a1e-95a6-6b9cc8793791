// src/components/ProductPage/ProductTabs/ProductTabs.tsx
import React from 'react';
import { Tab, Tabs, Tab<PERSON>ist, TabPanel } from 'react-tabs';
import 'react-tabs/style/react-tabs.css';

import CharactersticsTab from './CharactersticsTab';
import FilesTab from './FilesTab';
import ReviewsTab from './ReviewsTab';
import InfoTab from './InfoTab';

// Placeholder for product variant data
interface ProductVariant {
  id: string | number;
  // Add other relevant properties for a product variant
  [key: string]: any;
}

// Placeholder for main product data, including reviews and files
interface ProductGroup {
  id: string | number;
  reviews?: any; // Define more specific review type later
  files?: any[]; // Define more specific file type later
  // Add other relevant properties for a product group
  [key: string]: any;
}

interface ProductTabsProps {
  current?: ProductVariant | null;
  group?: ProductGroup | null;
  tabIndex: number;
  setTabIndex: (index: number) => void;
}

const ProductTabs: React.FC<ProductTabsProps> = ({ current, group, tabIndex, setTabIndex }) => {
  return (
    <Tabs id="char" className="w-full mt-5" selectedIndex={tabIndex} onSelect={(index) => setTabIndex(index)}>
      <TabList className="w-full flex flex-wrap gap-[10px]">
        <Tab selectedClassName="bg-colLightGray !border-colGray" className="cursor-pointer text-lg border-2 border-colLightGray rounded-lg flex justify-center items-center p-3 basis-[calc(50%-5px)] sm:basis-[calc(25%-7.5px)] hover:bg-colLightGray focus:outline-none focus:ring-2 focus:ring-colGreen/50">Характеристика и описание</Tab>
        <Tab selectedClassName="bg-colLightGray !border-colGray" className="cursor-pointer text-lg border-2 border-colLightGray rounded-lg flex justify-center items-center p-3 basis-[calc(50%-5px)] sm:basis-[calc(25%-7.5px)] hover:bg-colLightGray focus:outline-none focus:ring-2 focus:ring-colGreen/50">Документы и сертификаты</Tab>
        <Tab selectedClassName="bg-colLightGray !border-colGray" className="cursor-pointer text-lg border-2 border-colLightGray rounded-lg flex justify-center items-center p-3 basis-[calc(50%-5px)] sm:basis-[calc(25%-7.5px)] hover:bg-colLightGray focus:outline-none focus:ring-2 focus:ring-colGreen/50">Отзывы</Tab>
        <Tab selectedClassName="bg-colLightGray !border-colGray" className="cursor-pointer text-lg border-2 border-colLightGray rounded-lg flex justify-center items-center p-3 basis-[calc(50%-5px)] sm:basis-[calc(25%-7.5px)] hover:bg-colLightGray focus:outline-none focus:ring-2 focus:ring-colGreen/50">Доставка и оплата</Tab>
      </TabList>

      <TabPanel className="mt-5">
        {/* Pass necessary props to CharactersticsTab */}
        <CharactersticsTab current={current} group={group} />
      </TabPanel>

      <TabPanel className="mt-5">
        <FilesTab product={group} />
      </TabPanel>

      <TabPanel className="mt-5">
        <ReviewsTab current={current} />
      </TabPanel>

      <TabPanel className="mt-5">
        <InfoTab />
      </TabPanel>
    </Tabs>
  );
};

export default ProductTabs;
