import { ChevronLeft } from '@/shared/ui';
import { useGroupContext } from './hook/useGroupContext';

export const GroupHeaderArrow = () => {
  const { data, setIsOpen, isOpen } = useGroupContext();

  return (
    <div
      className='flex justify-between items-center text-[#222] group/filterName cursor-pointer '
      onClick={() => setIsOpen((p) => !p)}
    >
      <span className='font-semibold group-hover/filterName:text-colGreen select-none'>
        {data.name}
      </span>
      <span
        className={`transition-all ${isOpen && 'rotate-[-90deg]'} group-hover/filterName:text-colGreen text-[#d1d1d1]`}
      >
        <ChevronLeft size={20} />
      </span>
    </div>
  );
};
