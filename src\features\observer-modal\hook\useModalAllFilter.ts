import { ProductFilterURLInput } from '@/components/Catalog/CatalogRoot';
import { useGetFiltersNewMutation } from '@/entities/filter';
import {
  buildQueryParams,
  buildServerFilter
} from '@/shared/lib/catalog-filter/catalog-filter.utils';
import { getFiltersFromUrlUpdate } from '@/shared/lib/getFiltersFromUrl';
import { updateFilterState } from '@/shared/lib/queryUtils';
import { removePrefixes } from '@/shared/lib/removePrefixes';
import { ModalFilterAll, ObserverItem } from '@/shared/types/observer';
import { ProductFilter } from '@/types/Filters/ProductFilter';
import { ProductFilterURL, ProductFilterURLRange } from '@/types/Filters/ProductFilterURL';
import qs from 'query-string';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

export const useModalAllFilter = ({ data, onAccept }: ObserverItem<ModalFilterAll>) => {
  const [filters, setFilters] = useState<ProductFilter[][]>();
  const [selects, setSelects] = useState<ProductFilterURL[]>([]);
  const [chips, setChips] = useState<ProductFilterURL[]>([]);
  const [params, setParams] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isActive, setIsActive] = useState(false);

  const [fetch] = useGetFiltersNewMutation();
  const { categoryId } = useParams();

  function splitArrayIntoChunks(arr: ProductFilter[], chunks: number): ProductFilter[][] {
    const result = [];
    const chunkSize = Math.ceil(arr.length / chunks);

    for (let i = 0; i < arr.length; i += chunkSize) {
      result.push(arr.slice(i, i + chunkSize));
    }

    return result;
  }

  const handleAccept = () => {
    onAccept(filters.flat(), params);
  };

  const handleChips = async (item: ProductFilterURL) => {
    if (item.type === 'range') {
      await handleChangeFilter({ alone: { ...item, value: null } });
    } else {
      await handleChangeFilter({ alone: item });
    }
  };

  const updateSelects = (
    arr: ProductFilterURL[],
    input: ProductFilterURLInput
  ): ProductFilterURL[] => {
    if (!('alone' in input)) return arr;

    const fl = input.alone;

    const isSameParent = (item: ProductFilterURL) => item.parentId == fl.parentId;

    if (fl.type === 'multiple') {
      const exists = arr.some(
        (item) => item.type === 'multiple' && isSameParent(item) && item.id == fl.id
      );

      return exists
        ? arr.filter(
            (item) => !(item.type === 'multiple' && isSameParent(item) && item.id == fl.id)
          )
        : [...arr, { ...fl, value: true }];
    }

    if (fl.type === 'range') {
      let newItem: ProductFilterURLRange;
      if (fl.value === null) {
        newItem = {
          type: 'range',
          parentId: fl.parentId,
          value: null,
          title: fl.title
        };
      } else {
        newItem = {
          type: 'range',
          parentId: fl.parentId,
          value: fl.value as [number, number],
          title: fl.title
        };
      }

      const exists = arr.some(isSameParent);

      return exists ? arr.map((item) => (isSameParent(item) ? newItem : item)) : [...arr, newItem];
    }

    return arr;
  };

  const fetchAndUpdateFilters = async (filters: Record<string, any>) => {
    const r = await fetch({
      category_id: categoryId,
      filters
    });
    if ('data' in r) {
      setFilters(splitArrayIntoChunks(r.data.data, 3));
    }
  };

  const handleChangeFilter = async (input: ProductFilterURLInput) => {
    setIsLoading(true);
    if ('alone' in input) {
      const updated = updateSelects(selects, input);

      const parsedQuery = qs.parse(window.location.search, {
        arrayFormat: 'separator',
        arrayFormatSeparator: '|'
      });

      let filterData = removePrefixes(parsedQuery, ['filter_', 'range_']);

      updated.forEach((filter) => {
        filterData = updateFilterState(filterData, filter);
      });

      const queryParams = buildQueryParams(filterData);
      const serverFilters = buildServerFilter(filterData);

      await fetchAndUpdateFilters(serverFilters);

      setChips(updateSelects(chips, input));
      setSelects(updated);
      setParams(queryParams);
      setIsLoading(false);
    }
  };

  function createProductFilterURL(
    filterData: ProductFilter[],
    userValues: unknown
  ): ProductFilterURL[] {
    const result: ProductFilterURL[] = [];

    for (const [key, filter] of Object.entries(userValues)) {
      if (filter.type === 'multiple') {
        const parentId = key;
        const foundFilter = filterData.find((f) => f.id == parentId);
        if (foundFilter && foundFilter.input_type === 'multiple') {
          for (const val of filter.value) {
            const matched = foundFilter.values.find((v) => v.id == val);
            if (matched) {
              result.push({
                type: 'multiple',
                parentId,
                id: matched.id,
                title: foundFilter.name,
                text: matched.text,
                value: true
              });
            }
          }
        }
      }

      if (filter.type === 'range') {
        const range = filter.value;
        result.push({
          type: 'range',
          parentId: key,
          title: 'Цена',
          value: [+range.from, +range.to]
        });
      }
    }

    return result;
  }

  const resetFilters = async () => {
    const { filterParams } = getFiltersFromUrlUpdate();
    const r = await fetch({
      category_id: categoryId,
      filters: {}
    });
    if ('data' in r) {
      setFilters(splitArrayIntoChunks(r.data.data, 3));
    }
    setSelects(createProductFilterURL(data, filterParams));
    setChips([]);
    setIsLoading(false);
  };

  useEffect(() => {
    const parsedQuery = qs.parse(window.location.search, {
      arrayFormat: 'separator',
      arrayFormatSeparator: '|'
    });

    let filterData = removePrefixes(parsedQuery, ['filter_', 'range_']);

    const items = createProductFilterURL(data, filterData);
    setChips(items);

    return () => {};
  }, []);

  useEffect(() => {
    if (!selects.length) setIsActive(true);
    else setIsActive(false);
  }, [selects]);

  useEffect(() => {
    setFilters(splitArrayIntoChunks(data, 3));
  }, [data]);

  return {
    filters,
    isLoading,
    handleChangeFilter,
    isActive,
    handleAccept,
    selects,
    handleChips,
    chips,
    resetFilters
  };
};
