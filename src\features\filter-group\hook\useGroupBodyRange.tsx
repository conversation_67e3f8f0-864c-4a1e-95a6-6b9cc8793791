import { useState } from 'react';
import { GroupFilterRange } from '../model/context';
import { useGroupContext } from './useGroupContext';

export const useGroupBodyRange = () => {
  const { data } = useGroupContext();

  const {
    current_values: { max: currentMax, min: currentMin },
    min,
    max
  } = data as GroupFilterRange;

  const [range, setRange] = useState<[number, number]>([currentMin, currentMax]);

  return {
    range,
    setRange,
    data,
    min,
    max
  };
};
