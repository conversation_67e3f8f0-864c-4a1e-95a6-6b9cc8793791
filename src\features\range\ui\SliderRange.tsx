import { SliderRange as Slider } from '@/shared/ui';
import { useEffect, useState } from 'react';
import { useRange } from '../hook/useRange';

export const SliderRange = () => {
  const { value, setValue, onValueCommit, min, max, disabled } = useRange();
  const [range, setRange] = useState<[number, number]>(value);

  useEffect(() => {
    if (value === null) {
      setRange([min, max]);
    } else {
      setRange(value);
    }
  }, [value]);

  const handleRange = (num: [number, number]) => {
    setRange(num);
  };

  const handleCommit = (num: [number, number]) => {
    setValue(num);
    if (num[0] === min && num[1] === max) {
      onValueCommit(null);
    } else {
      onValueCommit(num);
    }
  };

  return (
    <Slider
      value={range}
      min={min}
      max={max}
      onValueChange={handleRange}
      onValueCommit={handleCommit}
      className='py-[7px]'
      disabled={disabled}
    />
  );
};
