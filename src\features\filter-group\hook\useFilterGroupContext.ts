import { useContext } from 'react';
import type { FilterGroupContextProps } from '../model/FilterGroupContext';
import { FilterGroupContext } from '../model/FilterGroupContext';

export const useFilterGroupContext = (): FilterGroupContextProps => {
  const context = useContext(FilterGroupContext);

  if (!context) {
    throw new Error('useFilterGroupContext must be used within a FilterGroup');
  }

  return context;
};
