import { api } from '@/shared/api/api';
import type { 
  GetVariantsResponse, 
  GetVariantsRequest,
  GetFiltersResponse, 
  GetFiltersRequest,
  GetSearchSuggestionsResponse,
  GetSearchSuggestionsRequest,
  GetSearchCorrectionsResponse,
} from './types';

export const searchApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Product search
    searchProducts: builder.mutation<GetVariantsResponse, GetVariantsRequest>({
      query: (params) => ({
        url: '/api/Products/variants',
        method: 'POST',
        body: params,
      }),
    }),
    
    // Search filters
    getSearchFilters: builder.mutation<GetFiltersResponse, GetFiltersRequest>({
      query: (params) => ({
        url: '/api/Products/filters',
        method: 'POST',
        body: params,
      }),
    }),
    
    // Search suggestions - updated endpoint
    getSearchSuggestions: builder.mutation<GetSearchSuggestionsResponse, GetSearchSuggestionsRequest>({
      query: (params) => ({
        url: '/api/Products/searchSuggestions',
        method: 'POST',
        body: params,
      }),
    }),
    
    // Search corrections/suggestions from new API
    getSearchCorrections: builder.query<GetSearchCorrectionsResponse, string>({
      query: (query) => ({
        url: `/api/ProdSearch/suggestions?query=${encodeURIComponent(query)}`,
        method: 'GET',
      }),
    }),
    
    // Search history
    getSearchHistory: builder.query<string[], void>({
      query: () => ({
        url: '/api/Products/search/history',
        method: 'GET',
      }),
      transformResponse: (response: any) => {
        return response?.data || [];
      },
    }),
    
    // Add to search history
    addToSearchHistory: builder.mutation<{ success: string }, string>({
      query: (searchTerm) => ({
        url: '/api/Products/search/history/add',
        method: 'POST',
        body: { searchTerm },
      }),
    }),
    
    // Clear search history
    clearSearchHistory: builder.mutation<{ success: string }, void>({
      query: () => ({
        url: '/api/Products/search/history/clear',
        method: 'POST',
      }),
    }),
  }),
  overrideExisting: false,
});

export const { 
  useSearchProductsMutation,
  useGetSearchFiltersMutation,
  useGetSearchSuggestionsMutation,
  useGetSearchCorrectionsQuery,
  useGetSearchHistoryQuery,
  useAddToSearchHistoryMutation,
  useClearSearchHistoryMutation,
} = searchApi;
