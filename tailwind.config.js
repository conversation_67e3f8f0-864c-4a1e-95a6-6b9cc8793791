/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ['class'],
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
  	extend: {
  		colors: {
  			colBlack: '#222',
  			colGreen: '#15765B',
  			colGray: '#B5B5B5',
  			colDarkGray: '#727272',
  			colSuperLight: '#F5F5F5',
  			// colLightGray: '#EBEBEB',
			colLightGray: '#DCE6E3',
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out'
  		}
  	},
  	animation: {
  		'tree-fade-in': 'tree-fade-in 0.3s ease-out forwards',
  		'tree-fade-out': 'tree-fade-out 0.2s ease-in forwards'
  	},
  	keyframes: {
  		'tree-fade-in': {
  			'0%': {
  				opacity: '0',
  				transform: 'translateY(-8px)'
  			},
  			'100%': {
  				opacity: '1',
  				transform: 'translateY(0)'
  			}
  		},
  		'tree-fade-out': {
  			'0%': {
  				opacity: '1',
  				transform: 'translateY(0)'
  			},
  			'100%': {
  				opacity: '0',
  				transform: 'translateY(-8px)'
  			}
  		}
  	},
  	screens: {
  		xl: '1280px',
  		ll: '1024px',
  		lg: '991px',
  		md: '768px',
  		mm: '576px',
  		sm: '480px',
  		xs: '380px',
  		sx: '280px'
  	}
  },
  plugins: [require('tailwindcss-animate')]
};
