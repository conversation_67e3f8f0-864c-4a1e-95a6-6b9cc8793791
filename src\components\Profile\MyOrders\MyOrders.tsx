import { useEffect, useRef, useState } from 'react';
import { NavLink } from 'react-router-dom';
import { useGetUserOrdersQuery } from '@/entities/order/api/orderApi';
import arrowIcon from '@/shared/assets/icons/arrow-icon.svg';
import { Loading } from '@/shared/ui/Loader';
import ErrorEmpty from '@/shared/ui/ErrorEmpty';
import { OrderItem } from './OrderItem';

const ORDERS_PER_PAGE = 10;

export const MyOrders = () => {
  const [page, setPage] = useState(1);
  const loadMoreRef = useRef<HTMLDivElement>(null);
  
  const {
    data: ordersData,
    isLoading: ordersIsLoading,
    isFetching,
    isFetchingNextPage, // Use this for loading more indicator
  } = useGetUserOrdersQuery({ page, limit: ORDERS_PER_PAGE });

  // Reset to first page when component mounts or categoryId changes (if applicable)
  useEffect(() => {
    setPage(1);
  }, []); // Add dependencies if needed, e.g., categoryId

  // Handle infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (
          entry?.isIntersecting && 
          !isFetching && // Use isFetching to avoid multiple calls while loading
          ordersData && 
          page < ordersData.pages
        ) {
          setPage((p) => p + 1);
        }
      },
      { threshold: 0.1 }
    );

    const currentRef = loadMoreRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [isFetching, ordersData, page]);

  // Show loading state only on initial load of the first page
  if (ordersIsLoading && page === 1) {
    return <Loading />;
  }

  // Show empty state if no orders after initial load
  if (!ordersIsLoading && !ordersData?.data?.length) {
    return (
      <div className="w-full lining-nums proportional-nums mb-5">
        <NavLink
          className="flex mm:hidden items-center space-x-1 mb-2"
          to="/profile"
        >
          <img src={arrowIcon} alt="*" />
          <span className="text-sm font-semibold">Вернуться к профилю</span>
        </NavLink>
        <h3 className="text-xl font-semibold text-colBlack pb-4">Все заказы</h3>
        <ErrorEmpty
          title="У вас пока нет заказов!"
          desc="Начните покупки, чтобы увидеть их здесь."
          height="230px"
          hideBtn={true}
        />
      </div>
    );
  }

  return (
    <div className="w-full lining-nums proportional-nums mb-5">
      <NavLink
        className="flex mm:hidden items-center space-x-1 mb-2"
        to="/profile"
      >
        <img src={arrowIcon} alt="*" />
        <span className="text-sm font-semibold">Вернуться к профилю</span>
      </NavLink>
      <h3 className="text-xl font-semibold text-colBlack pb-4">Все заказы</h3>

      <div className="space-y-5">
        {ordersData?.data?.map((order) => (
          <OrderItem key={order.order_number} order={order} />
        ))}
        
        {/* Show loading indicator when fetching next page */} 
        {isFetchingNextPage && <Loading />}
        <div ref={loadMoreRef} style={{ height: '20px' }} />
        
        {ordersData && page >= ordersData.pages && ordersData.data.length > 0 && (
          <div className="text-center py-4 text-gray-500">
            Вы просмотрели все заказы
          </div>
        )}
      </div>
    </div>
  );
};

export default MyOrders;
