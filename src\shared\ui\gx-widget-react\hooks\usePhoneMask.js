import { useState, useEffect, useCallback } from 'react';
import { formatPhoneNumber, cleanPhoneNumber } from '../utils/formatters';

/**
 * Hook to handle phone number masking
 * 
 * @param {string} initialValue - Initial value of the input
 * @param {string} mask - Mask pattern (X represents a digit placeholder)
 * @param {Function} onChange - Optional onChange handler
 * @returns {Object} - Input props and formatting helpers
 */
export const usePhoneMask = (initialValue = '', mask = '', onChange) => {
  const [value, setValue] = useState(initialValue || '');
  const [formattedValue, setFormattedValue] = useState('');
  const [currentMask, setCurrentMask] = useState(mask);
  const [maskApplied, setMaskApplied] = useState(false);
  
  // Format the value when it changes or when the mask changes
  const applyMask = useCallback((rawValue, maskPattern) => {
    if (maskPattern && rawValue) {
      return formatPhoneNumber(rawValue, maskPattern);
    }
    return rawValue;
  }, []);
  
  // Update the mask if it changes
  useEffect(() => {
    if (mask && mask !== currentMask) {
      setCurrentMask(mask);
      
      // Format the existing value with the new mask
      if (value) {
        const newFormattedValue = applyMask(value, mask);
        setFormattedValue(newFormattedValue);
        setMaskApplied(true);
      }
    }
  }, [mask, currentMask, value, applyMask]);
  
  // Update the formatted value when the value changes
  useEffect(() => {
    if (currentMask && value) {
      const newFormattedValue = applyMask(value, currentMask);
      setFormattedValue(newFormattedValue);
      setMaskApplied(true);
    } else if (value) {
      setFormattedValue(value);
    }
  }, [value, currentMask, applyMask]);
  
  // Initialize with initial value
  useEffect(() => {
    if (initialValue && initialValue !== value) {
      setValue(initialValue);
      
      // If we have a mask, immediately format the initial value
      if (currentMask) {
        const newFormattedValue = applyMask(initialValue, currentMask);
        setFormattedValue(newFormattedValue);
        setMaskApplied(true);
      } else {
        setFormattedValue(initialValue);
      }
    }
  }, [initialValue, currentMask, value, applyMask]);
  
  // Apply mask immediately to empty input if available
  useEffect(() => {
    if (currentMask && !maskApplied) {
      // Create a placeholder for empty input based on mask
      // This helps the user see what format they should use
      const placeholder = currentMask.replace(/X/g, '_');
      setFormattedValue('');  // Intentionally keep empty but set maskApplied to true
      setMaskApplied(true);
    }
  }, [currentMask, maskApplied]);
  
  // Handle input changes
  const handleChange = useCallback((e) => {
    // Allow only numbers and common phone formatting characters for direct input
    let inputValue = e.target.value.replace(/[^\d\s\-\(\)\.]/g, '');
    
    // If we have a mask, we need special handling to maintain the format
    if (currentMask) {
      // Store the raw value (digits only)
      setValue(cleanPhoneNumber(inputValue));
      
      // Format the value according to the mask
      const newFormattedValue = applyMask(inputValue, currentMask);
      setFormattedValue(newFormattedValue);
      setMaskApplied(true);
      
      // Call the external onChange if provided
      if (onChange) {
        // Create a synthetic event with the formatted value
        const syntheticEvent = {
          ...e,
          target: {
            ...e.target,
            value: newFormattedValue
          }
        };
        onChange(syntheticEvent);
      }
    } else {
      // No mask, just use the input value directly
      setValue(inputValue);
      setFormattedValue(inputValue);
      
      // Call the external onChange if provided
      if (onChange) {
        onChange(e);
      }
    }
  }, [currentMask, onChange, applyMask]);
  
  // Reset the input
  const reset = useCallback(() => {
    setValue('');
    setFormattedValue('');
    setMaskApplied(false);
  }, []);
  
  // Set a new value programmatically
  const setInputValue = useCallback((newValue) => {
    setValue(newValue);
    
    if (currentMask && newValue) {
      const newFormattedValue = applyMask(newValue, currentMask);
      setFormattedValue(newFormattedValue);
      setMaskApplied(true);
    } else {
      setFormattedValue(newValue);
    }
  }, [currentMask, applyMask]);
  
  // Update the mask programmatically
  const updateMask = useCallback((newMask) => {
    if (newMask !== currentMask) {
      setCurrentMask(newMask);
      
      // Also apply the new mask to the current value
      if (value) {
        const newFormattedValue = applyMask(value, newMask);
        setFormattedValue(newFormattedValue);
        setMaskApplied(true);
      }
    }
  }, [currentMask, value, applyMask]);
  
  return {
    value: formattedValue, // The value to display in the input
    rawValue: value, // The underlying unformatted value
    handleChange, // Event handler for the input
    inputProps: {
      value: formattedValue,
      onChange: handleChange
    },
    setInputValue, // Programmatically set the input value
    updateMask, // Programmatically update the mask
    reset, // Reset the input value
    maskApplied // Whether a mask has been applied
  };
};

export default usePhoneMask;