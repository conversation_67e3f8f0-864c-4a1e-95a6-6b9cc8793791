# Entity: Product

## Purpose

This entity represents the core **Product** data model within the Rostok application. It defines the structure of product information, provides API endpoints for fetching product data, and may include simple UI components for displaying product previews.

## Key Data Points

*   Basic product details (ID, SKU, slug, name, description)
*   Pricing information (`PriceType` from `entities/price`)
*   Availability (stock, preorder dates)
*   Images and other files (`ImageSet`)
*   Categorization (`CategoryBase`)
*   Brand information (`Brand`)
*   Tags (`Tag`)
*   Modification Attributes (`ModificationAttribute`) - attributes that define different product variants (e.g., color, size).
*   Product Group information (`ProductGroup`) - Represents the parent group containing multiple variants.

## Structure

*   **`model/types.ts`:** Defines the main `Product`, `ProductGroup`, `ModificationAttribute`, `Tag`, `Brand`, `AvailabilityState` interfaces.
*   **`api/productApi.ts`:** Defines RTK Query endpoints using `api.injectEndpoints`:
    *   `getProduct`: Query to fetch detailed `ProductGroup` data (including all variants) by product slug/ID. Uses `providesTags` for caching.
    *   `getVariants`: Mutation (used like a query) to fetch a list of `Product` variants based on filtering, sorting, and pagination criteria. Used for catalog and search results. *Note: Although defined as a mutation, it's often used for fetching lists due to complex POST parameters.*
*   **`api/types.ts`:** Defines request and response types specifically for the product API endpoints.
*   **`ui/`:** Contains simple UI components directly representing a product:
    *   `ProductPreview.tsx`: A compact display showing product image, name, and SKU, often used in lists like orders or reviews.
*   **`index.ts`:** Public API for the entity, exporting types, hooks, and UI components.

## Usage

*   **Data Fetching:** Use `useGetProductQuery` (on product detail pages) and `useGetVariantsMutation` (on catalog/search pages) to retrieve product data.
*   **Type Usage:** Import `Product`, `ProductGroup`, etc., types when working with product data throughout the application (e.g., in features like Cart, Favorites, Comparison).
*   **UI Display:** Use `ProductPreview` for concise representations. More complex displays like `ProductCard` reside in the `widgets` layer.
*   **Features Interaction:** Features like `cart`, `favorite`, `comparison` operate on `Product` data.

## Related Entities

*   `Category`: Products belong to categories.
*   `Brand`: Products can have a brand.
*   `Tag`: Products can have tags.
*   `Price`: Products have pricing information.
*   `Review`: Reviews are associated with products/variants.

## Migration Notes

*   Ensure all product-related data fetching logic uses the RTK Query hooks defined here.
*   Refactor any direct manipulation or fetching of product data outside this entity into using these centralized methods.