import { ModalFilterAll, ObserverItem } from '@/shared/types/observer';
import { Button, Chips } from '@/shared/ui';
import { ProductFilter } from '../product-filter';
import { useModalAllFilter } from './hook/useModalAllFilter';
import { useModalAnimation } from './hook/useModalAnimation';

interface ModalAllFilterProps {
  data: ObserverItem<ModalFilterAll>;
}

export const ModalAllFilter: React.FC<ModalAllFilterProps> = ({ data }) => {
  const { isUnvisible } = useModalAnimation(data.observerDismiss);
  const {
    filters,
    handleChangeFilter,
    isActive,
    isLoading,
    handleAccept,
    handleChips,
    chips,
    resetFilters
  } = useModalAllFilter(data);

  return (
    <div
      className={`absolute px-6 py-8 inset-0 bg-white transition-opacity duration-200 ease-in-out ${!isUnvisible ? 'opacity-100' : 'opacity-0'}`}
      style={{
        zIndex: `${data.observerId - 1}`
      }}
    >
      <div className='w-full lg:max-w-4xl mx-auto h-full m-auto flex flex-col'>
        <div className='flex justify-between'>
          <h2 className='text-2xl font-semibold text-[#222]'>Все фильтры</h2>
          <div className='gap-2 hidden sm:flex'>
            <Button onClick={handleAccept} disabled={isActive}>
              Применить и закрыть
            </Button>
            <Button variant='secondary' onClick={data.onCancel}>
              Закрыть
            </Button>
          </div>
        </div>
        <br className='sm:block hidden' />
        <div className='flex gap-[10px] flex-nowrap overflow-x-auto scrollable2 '>
          {!!chips.length &&
            chips.map(
              (item, index) =>
                item.value !== null && (
                  <Chips
                    title={item.title}
                    text={
                      item.type === 'multiple'
                        ? item.text
                        : `от ${item.value[0]} до ${item.value[1]}`
                    }
                    onClick={() => handleChips(item)}
                    key={`${item.parentId}-${index}`}
                  />
                )
            )}
          {!!chips.length && (
            <Chips
              title={'Очистить фильтр'}
              onClick={resetFilters}
              disabled={isLoading}
              variant='secondary'
              className='flex-shrink-0 whitespace-nowrap'
            />
          )}
        </div>
        <br className='sm:block hidden' />
        <div className='flex gap-4 w-full mt-4 flex-wrap overflow-auto scrollable2'>
          {filters &&
            filters.map((item, index) => {
              return (
                <div
                  className='w-full sm:w-[48%] lg:w-[32%] h-fit grow bg-[#F4F5F5] p-4 rounded-[8px]'
                  key={index}
                >
                  {item.map((filter, index) => (
                    <div
                      key={filter.id}
                      className={`  ${item.length - 1 === index ? '' : 'border-b-2'} border-[#ffffff] py-[12px] px-[6px] will-change-[height]`}
                    >
                      <ProductFilter
                        data={filter}
                        onChange={handleChangeFilter}
                        mode='full'
                        show={false}
                        disabled={isLoading}
                      />
                    </div>
                  ))}
                </div>
              );
            })}
        </div>
        <div className='gap-2 flex sm:hidden sticky bottom-0 bg-white w-full p-3 mt-auto'>
          <Button onClick={handleAccept} disabled={isActive}>
            Применить и закрыть
          </Button>
          <Button variant='secondary' className='w-full' onClick={data.onCancel}>
            Закрыть
          </Button>
        </div>
      </div>
    </div>
  );
};
