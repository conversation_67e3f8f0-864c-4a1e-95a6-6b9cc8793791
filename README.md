# Furnica

A modern e-commerce frontend application built with React, Redux Toolkit, and TypeScript. The project is structured according to Feature-Sliced Design (FSD) architecture to enhance maintainability, scalability, and developer experience.

## Key Features

* **Product Catalog**: Browse products via categories, brands, or tags
* **Product Search**: Find products with type-ahead suggestions
* **Shopping Cart**: Add/remove items, adjust quantities, and share cart with others
* **Authentication**: Register, login (phone/email+password), password management
* **User Profile**: Manage personal data, organizations, review order history
* **Checkout**: Multi-step order placement process with support for multi-vendor orders and various payment methods
* **Fast Order**: "Buy in 1 Click" capability from product pages
* **Additional Features**: Product comparison, favorites, recently viewed products

## Technology Stack

* **Framework**: React 18 with TypeScript
* **Build Tool**: Vite
* **State Management**: Redux Toolkit (including RTK Query for API)
* **Routing**: React Router v6
* **Styling**: Tailwind CSS (primary), Material UI (secondary/legacy), Shadcn UI (shared components)
* **Maps**: Yandex Maps integration for pickup points
* **Linting/Formatting**: ESLint, Prettier

## Architecture: Feature-Sliced Design (FSD)

This project follows the Feature-Sliced Design methodology, organizing code into layers and slices to promote modularity and reduce coupling.

**Layers (Top-down dependency):**

`app` → `pages` → `widgets` → `features` → `entities` → `shared`

For a detailed explanation of FSD in this project, see [docs/architecture/fsd-overview.md](./docs/architecture/fsd-overview.md).

## Project Structure

The project contains both code following the target FSD structure and legacy code pending refactoring.

**Target FSD Structure:**

```
src/
├── app/               # App initialization, providers, global styles, layouts
├── pages/             # Page components, routing logic composition
├── widgets/           # Composite UI blocks (e.g., Header, Footer, ProductList)
├── features/          # User interaction logic (e.g., auth, cart, search)
├── entities/          # Core business entities (e.g., Product, User, Order)
└── shared/            # Reusable code (UI kit, API base, utils, types)
```

**Legacy Directories (To Be Refactored):**

```
src/
├── components/        # Mixed page, widget, feature UI components
├── helpers/           # Utility components/functions
├── hooks/             # Global custom hooks
├── api/               # Legacy direct API calls
├── redux/             # Legacy RTK Query endpoint definitions
└── types/             # Global types
```

See [Refactoring Plan](./docs/refactoring/plan.md) for the migration strategy and [Legacy Code Documentation](./docs/legacy/) for details on specific legacy modules.

## Documentation Structure

The project documentation is organized as follows:

1. **Root README (`/README.md`)**: You are here. Provides a high-level overview and entry point.
2. **Docs Hub (`/docs/README.md`)**: Central Table of Contents linking to all architectural documents, guides, and layer explanations.
3. **Architecture Docs (`/docs/architecture/`)**: Explanations of architectural decisions (FSD, State Management, API, Styling).
4. **Guides (`/docs/guides/`)**: How-to guides for common tasks.
5. **Layer READMEs (`/src/{layer}/README.md`)**: Each FSD layer directory contains a README explaining its role and conventions.
6. **Feature/Entity/Widget READMEs**: Key components have their own README files for more granular detail.
7. **Legacy Docs (`/docs/legacy/`)**: Documentation on legacy code directories and migration targets.
8. **Flow Docs**: Documentation for specific business flows like [checkout flow](./docs/checkout-flow.md).

## Getting Started

### Prerequisites

* Node.js (v16.x or higher recommended)
* npm (v7.x or higher recommended)

### Installation & Running

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd furnica
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Open [http://localhost:5173](http://localhost:5173) in your browser.

## Available Scripts

* `npm run dev`: Start development server with hot reload
* `npm run build`: Build the application for production
* `npm run lint`: Run ESLint to check for code style issues
* `npm run lint:fix`: Attempt to automatically fix ESLint issues
* `npm run format`: Format code using Prettier
* `npm run check-types`: Run TypeScript compiler to check for type errors
* `npm run validate`: Run linting and type checking
* `npm run preview`: Preview the production build locally

## Contributing

Please read the [Contributing Guidelines](./docs/guides/contributing.md) before submitting pull requests. Adherence to FSD principles and code style is crucial for maintaining project quality.

## Recent Updates

- **2025-04-24**: Redesigned checkout page UI to match new Figma designs, with improved support for multiple orders and vendor-specific grouping.
- **2025-04-24**: Implemented new multi-vendor checkout flow supporting separated orders and multiple payment methods.
