import { ProductFilterURLInput } from '@/components/Catalog/CatalogRoot';
import { ProductFilter } from '@/types/Filters/ProductFilter';

export interface ProductFilterProps {
  data: ProductFilter;
  mode?: 'limited' | 'full' | 'alone';
  show?: boolean;
  onChange: (data: ProductFilterURLInput) => void;
  disabled?: boolean;
}

export interface IProductFilterContext {
  data: ProductFilter;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  onChange: (data: ProductFilterURLInput) => void;
  disabled?: boolean;
}
