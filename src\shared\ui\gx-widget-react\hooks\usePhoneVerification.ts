import { useState } from 'react';

/**
 * Hook to handle phone verification and authentication
 *
 * @param {string} apiUrl - API URL for the verification endpoints
 * @returns {Object} - Verification methods and state
 */
export const usePhoneVerification = (apiUrl = 'https://phone.gexarus.com/api') => {
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationError, setVerificationError] = useState(null);
  const [sessionId, setSessionId] = useState(null);

  /**
   * Prepare phone number for API calls
   * Ensures we don't add the country code if it's already present
   */
  const preparePhoneNumber = (phoneNumber, countryCode) => {
    if (!phoneNumber) return '';

    // Clean the phone number (digits only)
    const digitsOnly = phoneNumber.replace(/\\D/g, '');

    // Check if phone already starts with the country code
    if (digitsOnly.startsWith(countryCode)) {
      // Already has country code, use as is
      return digitsOnly;
    }

    // Add country code
    return digitsOnly;
  };

  /**
   * Validates a phone number format
   */
  const validateNumber = async (phoneNumber, countryIso, countryCode) => {
    try {
      setIsVerifying(true);
      setVerificationError(null);

      // IMPORTANT: Do NOT prepend country code here - send local number only
      const cleanedPhone = preparePhoneNumber(phoneNumber, countryCode);

      const response = await fetch(`${apiUrl}/Number/info`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          phone: cleanedPhone,
          country: countryIso,
          verification: 1,
          changeCountry: true // Explicitly enable country auto-detection
        })
      });

      const data = await response.json();

      if (!data.success) {
        // Use more user-friendly error message
        const friendlyError = data.err === 'Invalid value'
          ? 'Некорректный номер телефона'
          : data.err;

        setVerificationError(friendlyError);
        return { success: false, error: friendlyError };
      }

      return {
        success: true,
        phone: data.phone,
        formattedPhone: cleanedPhone,
        data: data
      };
    } catch (error) {
      console.error('[usePhoneVerification] Error validating number:', error);
      setVerificationError(error.message);
      return { success: false, error: 'Ошибка проверки номера' };
    } finally {
      setIsVerifying(false);
    }
  };

  /**
   * Gets information about a phone number without full validation
   * Used for quick country detection
   */
  const getNumberInfo = async (phoneNumber, countryIso, countryCode) => {
    if (!phoneNumber || phoneNumber.replace(/\\D/g, '').length < 6) {
      return { success: false, error: 'Номер слишком короткий' };
    }

    try {
      // IMPORTANT: Do NOT prepend country code here - send local number only
      const cleanedPhone = preparePhoneNumber(phoneNumber, countryCode);

      const response = await fetch(`${apiUrl}/Number/info`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          phone: cleanedPhone,
          country: countryIso,
          verification: 1,
          changeCountry: true // Enable country auto-detection
        })
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('[usePhoneVerification] Error getting number info:', error);
      return { success: false, error: 'Ошибка проверки номера' };
    }
  };

  /**
   * Sends verification code to the phone number
   */
  const sendVerificationCode = async (phoneNumber, countryIso, countryCode) => {
    try {
      setIsVerifying(true);
      setVerificationError(null);

      // IMPORTANT: Do NOT prepend country code here - send local number only
      const cleanedPhone = preparePhoneNumber(phoneNumber, countryCode);

      const response = await fetch(`${apiUrl}/Verification/start`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          phone: cleanedPhone,
          country: countryIso
        })
      });

      const data = await response.json();

      if (!data.success) {
        const friendlyError = data.err === 'Invalid value'
          ? 'Некорректный номер телефона'
          : data.err;

        setVerificationError(friendlyError);
        return { success: false, error: friendlyError };
      }

      // Store the session ID for code verification
      setSessionId(data.gxVerificationSessionKey);

      return {
        success: true,
        sessionId: data.gxVerificationSessionKey,
        message: data.message,
        limitTimeSeconds: data.limitTimeSeconds,
        data: data
      };
    } catch (error) {
      console.error('[usePhoneVerification] Error sending verification code:', error);
      setVerificationError(error.message);
      return { success: false, error: 'Ошибка отправки кода' };
    } finally {
      setIsVerifying(false);
    }
  };

  /**
   * Verifies the code entered by the user
   */
  const verifyCode = async (code, sessionIdParam) => {
    try {
      setIsVerifying(true);
      setVerificationError(null);

      // Use provided session ID or stored one
      const sessionKey = sessionIdParam || sessionId;

      if (!sessionKey) {
        throw new Error('Нет активной сессии верификации');
      }

      const response = await fetch(`${apiUrl}/Verification/code`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          code: code,
          sessionKey: sessionKey
        })
      });

      const data = await response.json();

      if (!data.success) {
        setVerificationError(data.err);
        return { success: false, error: data.err };
      }

      return {
        success: true,
        verified: true,
        sessionId: sessionKey,
        data: data,
        sessid: data.sessid || sessionKey
      };
    } catch (error) {
      console.error('[usePhoneVerification] Error verifying code:', error);
      setVerificationError(error.message);
      return { success: false, error: 'Ошибка проверки кода' };
    } finally {
      setIsVerifying(false);
    }
  };



  return {
    validateNumber,
    getNumberInfo, // Export getNumberInfo for more frequent checks
    sendVerificationCode,
    verifyCode,
    sessionId,
    isVerifying,
    verificationError
  };
};

export default usePhoneVerification;