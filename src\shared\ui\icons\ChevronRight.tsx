import React from 'react';

interface SvgIconProps {
  size?: number;
  color?: string;
  stroke?: number;
}

export const ChevronRight: React.FC<SvgIconProps> = ({
  size = 24,
  color = 'currentColor',
  stroke = 2
}) => {
  return (
    <svg
      width={size + 'px'}
      height={size + 'px'}
      viewBox='0 0 24 24'
      fill='none'
      color={color}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M9 5L15 12L9 19'
        stroke='currentColor'
        strokeWidth={stroke}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  );
};
