import type { ButtonHTMLAttributes, ReactNode } from 'react';
import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/shared/lib/utils';
import { Loader2 } from 'lucide-react';
import { Link } from 'react-router-dom';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-60',
  {
    variants: {
      variant: {
        primary: 'bg-colGreen text-white hover:bg-colGreen/90 focus:ring-colGreen/50',
        secondary: 'bg-white text-colBlack border border-colGray hover:bg-colSuperLight focus:ring-colGray/50',
        outline: 'bg-transparent text-colGreen border border-colGreen hover:bg-colGreen/10 focus:ring-colGreen/50',
        link: 'text-colGreen underline-offset-8 hover:underline',
      },
      size: {
        sm: 'h-9 px-3 py-1.5',
        md: 'h-10 px-4 py-2',
        lg: 'h-12 px-6 py-3',
      },
      fullWidth: {
        true: 'w-full',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'md',
      fullWidth: false,
    },
  }
);

export interface ButtonProps
  extends ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  isLoading?: boolean;
  children: ReactNode;
  className?: string;
  to?: string;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, fullWidth, asChild = false, isLoading = false, disabled, children, to, ...props }, ref) => {
    const Comp = asChild ? Slot : to ? Link : 'button';

    const commonProps = {
      className: cn(buttonVariants({ variant, size, fullWidth, className })),
      ref,
      ...props,
    };

    if (to) {
      return (
        <Link to={to} {...commonProps}>
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {children}
        </Link>
      );
    }

    return (
      <Comp
        type={Comp === 'button' ? 'button' : undefined}
        disabled={isLoading || disabled}
        {...commonProps}
      >
        {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        {children}
      </Comp>
    );
  }
);

Button.displayName = 'Button';

export { Button, buttonVariants };
