import { modalFilterItemObserver } from '@/shared/lib/observer';
import { bodyScrollLockSingleObserver } from '@/shared/lib/observer/body.observer';
import { ProductFilter } from '@/types/Filters/ProductFilter';
import { useEffect, useRef, useState } from 'react';
import { useProductFilterContext } from './useProductFIlterContext';

export const useProductFilterOptionLimited = () => {
  const { data, isOpen, onChange, disabled } = useProductFilterContext();
  const [bodyHeight, setBodyHeight] = useState<number>(0);
  const bodyRef = useRef<HTMLDivElement | null>(null);
  const [visible, setVisible] = useState(false);
  const [isMore, setIsMore] = useState(false);
  const [limitedData, setLimitedData] = useState<ProductFilter | null>();

  const handleModal = () => {
    if (data.input_type === 'multiple') {
      // bodyScrollLockObserver.addObserver({ isLock: true });
      bodyScrollLockSingleObserver.setValue(true);
      modalFilterItemObserver.addObserver({
        type: 'item',
        data: data,
        onAccept: (data: any) => {
          // bodyScrollLockObserver.dissmissObserver();
          bodyScrollLockSingleObserver.setValue(false);
          onChange(data);
        },
        onClose: () => {
          // bodyScrollLockObserver.dissmissObserver();
          bodyScrollLockSingleObserver.setValue(false);
        }
      });
    }
  };

  useEffect(() => {
    if (!data) return;

    if (data.input_type === 'multiple') {
      if (data.values.length > 5) {
        setIsMore(true);
        if (data.type === 'color') {
          setLimitedData({ ...data, values: data.values.slice(0, 5) });
        } else if (data.type === 'text') {
          setLimitedData({ ...data, values: data.values.slice(0, 5) });
        } else if (data.type === 'image') {
          setLimitedData({ ...data, values: data.values.slice(0, 5) });
        }
      } else {
        setIsMore(false);
        setLimitedData(data);
      }
    } else if (data.input_type === 'range') {
      setLimitedData(data);
      setIsMore(false);
    }
  }, [data]);

  useEffect(() => {
    const el = bodyRef.current;
    if (!el) return;
    const height = el.scrollHeight;
    el.style.height = `${height}px`;

    requestAnimationFrame(() => {
      setBodyHeight(height);
      el.style.height = isOpen ? `${height}px` : '0px';
    });
  }, [isOpen]);

  useEffect(() => {
    let timeout: NodeJS.Timeout;

    if (isOpen) {
      timeout = setTimeout(() => setVisible(true), 200);
    } else {
      setVisible(false);
    }

    return () => clearTimeout(timeout);
  }, [isOpen]);

  return {
    bodyRef,
    bodyHeight,
    isOpen,
    data,
    visible,
    limitedData,
    isMore,
    onChange,
    disabled,
    handleModal
  };
};
