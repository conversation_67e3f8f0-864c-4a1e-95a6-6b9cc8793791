// src/app/providers/InitializationProvider/lib/useInitialDataSync.ts
import { useState, useEffect, useCallback, useRef } from 'react';
import { useDispatch } from 'react-redux';

import { useAuthContext } from '@/entities/user/model/AuthContext';
import { useGetUserCartQuery, setCart } from '@/features/cart';
import { useGetComparisonQuery, setComparison } from '@/features/comparison';
import { useGetFavoritesQuery, setFavorite } from '@/features/favorite';
import {
  useGetRecentItemsQuery,
  setRecentItems,
} from '@/features/recent-items';

import { syncLocalStorageWithState } from './utils';

import type { AppDispatch } from '@/app/providers/store';

/**
 * Hook for synchronizing user data between client and server
 * Automatically triggers when authentication state changes
 * Can also be triggered manually via the returned syncData function
 */
export const useInitialDataSync = () => {
  const dispatch: AppDispatch = useDispatch();
  // Get authentication state from AuthContext
  const { isAuthenticated, isInitialized } = useAuthContext();

  // Sync state tracking
  const [isSynced, setIsSynced] = useState(false);
  const [isDataSyncing, setIsDataSyncing] = useState(false);
  const [syncAttempted, setSyncAttempted] = useState(false);

  // Keep track of whether we're in the first render
  const isFirstRender = useRef(true);

  // Debug logging helper
  const logSync = (message, data = {}) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[DataSync] ${message}`, {
        isAuthenticated,
        isInitialized,
        isSynced,
        isDataSyncing,
        syncAttempted,
        ...data
      });
    }
  };

  // Skip queries until auth is initialized and user is authenticated
  const skipQueries = !isInitialized || !isAuthenticated;

  // Data queries with skip condition
  const {
    data: serverComparison,
    isSuccess: isSuccessComparison,
    isError: isErrorComparison,
    isFetching: isFetchingComparison,
    refetch: refetchComparison
  } = useGetComparisonQuery(undefined, { skip: skipQueries });

  const {
    data: serverFavorite,
    isSuccess: isSuccessFavorite,
    isError: isErrorFavorite,
    isFetching: isFetchingFavorite,
    refetch: refetchFavorite
  } = useGetFavoritesQuery(undefined, { skip: skipQueries });

  const {
    data: serverCart,
    isSuccess: isSuccessCart,
    isError: isErrorCart,
    isFetching: isFetchingCart,
    refetch: refetchCart
  } = useGetUserCartQuery(undefined, { skip: skipQueries });

  const {
    data: serverRecentItems,
    isSuccess: isSuccessRecentItems,
    isError: isErrorRecentItems,
    isFetching: isFetchingRecentItems,
    refetch: refetchRecentItems
  } = useGetRecentItemsQuery(undefined, { skip: skipQueries });

  // Determine if any data is currently being fetched
  const isFetching = isFetchingComparison || isFetchingFavorite || isFetchingCart || isFetchingRecentItems;

  // Function to explicitly trigger refetching all data
  const refetchAllData = useCallback(() => {
    if (!skipQueries) {
      logSync("Explicitly refetching all data");
      refetchComparison();
      refetchFavorite();
      refetchCart();
      refetchRecentItems();
    }
  }, [skipQueries, refetchComparison, refetchFavorite, refetchCart, refetchRecentItems]);

  // Main data sync function - can be called explicitly or triggered by auth state change
  const syncData = useCallback(async () => {
    // Don't sync if not initialized
    if (!isInitialized) {
      logSync("Not syncing - not initialized");
      return false;
    }

    // Don't start a new sync if one is already in progress
    if (isDataSyncing) {
      logSync("Not syncing - sync already in progress");
      return false;
    }

    // Mark sync as attempted and in progress
    setSyncAttempted(true);
    setIsDataSyncing(true);
    logSync("Starting data sync");

    try {
      // For non-authenticated users, sync with localStorage
      if (!isAuthenticated) {
        logSync("Syncing for non-authenticated user from localStorage");
        syncLocalStorageWithState(dispatch);
        setIsSynced(true);
        return true;
      }

      // For authenticated users, force-refetch data from server
      logSync("Syncing for authenticated user from server");
      refetchAllData();

      // The completion of sync will be handled by the effect below
      // when the queries finish
      return true;
    } catch (error) {
      console.error('[useInitialDataSync] Error syncing data:', error);
      setIsDataSyncing(false);
      return false;
    }
  }, [
    dispatch,
    isInitialized,
    isAuthenticated,
    isDataSyncing,
    refetchAllData,
  ]);

  // Trigger data sync when auth state changes or on initial mount
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      if (isInitialized && isAuthenticated && !isSynced) {
        logSync("Initial sync on first render");
        syncData();
      }
    } else if (isInitialized && isAuthenticated && !isSynced && !isDataSyncing) {
      logSync("Auto-triggering sync due to state change");
      syncData();
    }
  }, [isInitialized, isAuthenticated, isSynced, isDataSyncing, syncData]);

  // Handle sync completion when queries finish
  useEffect(() => {
    if (
      isAuthenticated &&
      syncAttempted &&
      !isDataSyncing &&
      !isFetching &&
      !isSynced
    ) {
      logSync("All queries completed, processing results", {
        serverComparisonSuccess: isSuccessComparison,
        serverFavoriteSuccess: isSuccessFavorite,
        serverCartSuccess: isSuccessCart,
        serverRecentItemsSuccess: isSuccessRecentItems
      });

      // All queries have completed, update state
      if (isSuccessComparison) {
        logSync("Updating comparison data", { data: serverComparison?.data });
        dispatch(setComparison(serverComparison?.data ?? []));
      }

      if (isSuccessFavorite) {
        logSync("Updating favorites data", { data: serverFavorite?.data });
        dispatch(setFavorite(serverFavorite?.data ?? []));
      }

      if (isSuccessRecentItems) {
        logSync("Updating recent items data", { data: serverRecentItems?.data });
        dispatch(setRecentItems(serverRecentItems?.data ?? []));
      }

      if (isSuccessCart) {
        logSync("Updating cart data", { data: serverCart?.data });
        dispatch(
          setCart(
            serverCart
              ? {
                  cart: serverCart.data,
                  total: serverCart.total,
                  selected: serverCart.selected,
                  currency: serverCart.current_currency,
                }
              : null
          )
        );
      }

      setIsSynced(true);
      setIsDataSyncing(false);
      logSync("Sync completed successfully");
    }

    // If queries are done but we're still in syncing state, make sure to update
    if (isDataSyncing && !isFetching) {
      setIsDataSyncing(false);
    }
  }, [
    dispatch,
    isAuthenticated,
    syncAttempted,
    isDataSyncing,
    isFetching,
    isSynced,
    isSuccessComparison,
    isSuccessFavorite,
    isSuccessCart,
    isSuccessRecentItems,
    serverComparison,
    serverFavorite,
    serverCart,
    serverRecentItems,
  ]);

  // Reset sync state when auth state changes
  useEffect(() => {
    // If authentication status changes, reset the sync state
    if (!isAuthenticated && isSynced) {
      logSync("Auth state changed to not authenticated, resetting sync state");
      setIsSynced(false);
      setSyncAttempted(false);
    }
  }, [isAuthenticated, isSynced]);

  return {
    isSynced,
    isDataSyncing,
    syncData, // Export the sync function for explicit calls
    refetchAllData,
  };
};
