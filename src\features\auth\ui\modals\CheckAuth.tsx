// src/AuthModal/CheckAuth.tsx

import { useState, useEffect, useRef } from 'react';

import { KeyboardArrowRight } from '@mui/icons-material';
import { Controller, useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { useRegistrationCheckMutation } from '@/features/auth';
import { getErrorMessage } from '@/shared/lib/errors';
import { CPhoneField } from '@/shared/ui/inputs/CPhoneField';
import { LoadingSmall } from '@/shared/ui/Loader';
import { useDispatch } from 'react-redux';

// Define types for API response
interface RegistrationCheckResponseProps {
  login_type?: string;
  err?: string;
}

interface CheckAuthProps {
  setContent: (content: string) => void;
  setLogin: (login: { type: string; login: string }) => void;
}

// Form values interface
interface FormValuesProps {
  login: string;
}

export const CheckAuth = ({ 
  setContent,
  setLogin,
}: CheckAuthProps): JSX.Element => {
  const [error, setError] = useState<string | null>(null);
  const [useGxWidget, setUseGxWidget] = useState(true); // Set default to true to prioritize the GX widget
  const [isGxPhoneValid, setIsGxPhoneValid] = useState(false);
  const [gxPhoneValue, setGxPhoneValue] = useState('');
  const [authSuccessful, setAuthSuccessful] = useState(false);
  const dispatch = useDispatch();
  const phoneInputRef = useRef(null);

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors: _errors },
  } = useForm<FormValuesProps>({ mode: 'onChange' });

  const [registrationCheck, { isLoading }] = useRegistrationCheckMutation();

  const onSubmitAuthCheck = async (data: FormValuesProps): Promise<void> => {
    setError(null);
    
    // If using GX widget, override the form data with the widget value
    const submitData = useGxWidget ? { login: gxPhoneValue } : data;
    
    try {
      const result = await registrationCheck(submitData);
      
      // Handle successful response
      if ('data' in result && result.data) {
        const responseData = result.data as RegistrationCheckResponseProps;
        
        if (
          responseData.login_type === 'email' ||
          responseData.login_type === 'phone'
        ) {
          setLogin({ type: responseData.login_type, login: submitData.login });
          setContent('authWithEmail');
        }
      } else {
        // Handle error response
        let errorMessage = 'Unknown error';
        
        if ('error' in result && result.error) {
          if ('data' in result.error && result.error.data) {
            const errorData = result.error.data as { err?: string };
            errorMessage = errorData.err || errorMessage;
          }
        }
        
        setError(errorMessage);
        setLogin({ type: 'phone', login: submitData.login });
        setContent('register');
      }
    } catch (error) {
      setError(getErrorMessage(error));
      console.error(error);
    }
  };

  // Handler for when verification completes
  const handleVerificationComplete = (response): void => {
    setAuthSuccessful(true);
    
    // Show toast notification
    toast.success('Авторизация выполнена успешно', {
      duration: 3000,
      position: 'top-center',
    });
  };

  // Effect to redirect user after successful authentication
  useEffect(() => {
    if (authSuccessful) {
      // Give time for the token to be stored
      const timer = setTimeout(() => {
        // Close the auth modal or redirect as needed
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [authSuccessful]);

  const handleGxValueChange = (value: string): void => {
    setGxPhoneValue(value);
    // Also update the form value for consistency
    setValue('login', value);
  };

  return (
    <>
      <h1 className="text-2xl mm:text-3xl text-colBlack text-center pt-2 pb-8 font-semibold">
        Вход или Регистрация
      </h1>
      
      <form onSubmit={handleSubmit(onSubmitAuthCheck)}>
        {!useGxWidget ? (
          <>
            <div className="mb-4">
              <button 
                type="button"
                onClick={() => setUseGxWidget(true)}
                className="mb-4 px-3 py-1 text-sm bg-colGreen text-white rounded"
              >
                Использовать быструю авторизацию по телефону
              </button>
            </div>
            <Controller
              name="login"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <CPhoneField
                  label="Введите телефон"
                  type="text"
                  required={true}
                  onChange={field.onChange}
                  value={field.value}
                />
              )}
            />
          </>
        ) : (
          <>
            <div className="mb-4">
              <button 
                type="button"
                onClick={() => setUseGxWidget(false)}
                className="mb-4 px-3 py-1 text-sm bg-colGray text-white rounded"
              >
                Использовать стандартный ввод
              </button>
            </div>
            <div className="mb-4">
              <label 
                htmlFor="gx-phone-input" 
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Введите телефон для авторизации
              </label>
              <GxPhoneInputReact
                ref={phoneInputRef}
                onVerificationComplete={handleVerificationComplete}
                requiredVerification={true}
                onChange={handleGxValueChange}
                onValid={setIsGxPhoneValid}
                className="w-full"
                placeholder="Ваш номер телефона"
              />
              
              {/* Success indicator is now just a small icon, no text message */}
              {authSuccessful && (
                <div className="absolute -right-2 -top-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-md animate-fade-in">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </div>
          </>
        )}
        
        {error ? (
          <p className="text-xs text-red-400">{error || 'Error!'}</p>
        ) : null}
        
        {/* Only show the continue button for the standard form, not for GX widget */}
        {!useGxWidget && (
          <button
            type="submit"
            disabled={isLoading}
            className={`w-full h-10 px-6 rounded mt-5 text-white font-semibold flex justify-center items-center ${
              isLoading ? 'bg-colGray' : 'bg-colGreen'
            }`}
          >
            {!isLoading ? (
              <>
                Продолжить
                <KeyboardArrowRight className="!w-5" />
              </>
            ) : null}
            {isLoading ? <LoadingSmall extraStyle="white" /> : null}
          </button>
        )}
      </form>
    </>
  );
};
