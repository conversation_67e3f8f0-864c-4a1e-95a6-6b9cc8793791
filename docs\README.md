# Furnica Documentation

Last updated: 2025-04-25 09:15 UTC

Welcome to the Furnica documentation hub. This is the central repository for all documentation related to the Furnica e-commerce frontend application.

## Table of Contents

1. [Architecture](./architecture/fsd-overview.md)
   - [API Integration](./architecture/api-integration.md)
   - [Authentication](./architecture/authentication.md)
   - [Cross-Domain Auth](./architecture/cross-domain-auth.md)
   - [Error Handling](./architecture/error-handling.md)
   - [Routing](./architecture/routing.md)
   - [State Management](./architecture/state-management.md)
   - [Styling](./architecture/styling.md)

2. [Business Flows](./checkout-flow.md)
   - [Checkout Flow](./checkout-flow.md)

3. [Guides](./guides/getting-started.md)
   - [Getting Started](./guides/getting-started.md)
   - [Contributing](./guides/contributing.md)

4. [Legacy Code Documentation](./legacy/)
   - [API](./legacy/api.md)
   - [Components](./legacy/components.md)
   - [Helpers](./legacy/helpers.md)
   - [Hooks](./legacy/hooks.md)
   - [Redux](./legacy/redux.md)
   - [Types](./legacy/types.md)

5. [Refactoring](./refactoring/plan.md)
   - [Plan](./refactoring/plan.md)

## Recent Updates

- **2025-04-25**: Implemented product stock validation in quantity controls. The increment button is now disabled when a product with `preorder: null` reaches its maximum stock quantity. Added a visible message to inform users about the stock limit.

- **2025-04-24**: Redesigned checkout page UI according to new Figma designs. Updated `OrdersList`, `OrderItem`, `ProductItem`, and other checkout-related components to match the new design. The checkout page now supports multiple suborders from different vendors with an improved visual hierarchy and user experience.

- **2025-04-20**: Added support for multi-vendor checkout flow with separated orders and multiple payment methods.

- **2025-04-15**: Implemented new responsive navigation for better mobile experience.

- **2025-04-10**: Migrated product card components to Feature-Sliced Design architecture.

## Getting Started

For developers new to the project, please start with the [Getting Started Guide](./guides/getting-started.md). This will help you set up your development environment and understand the project structure.

## Contributing

Please read our [Contributing Guidelines](./guides/contributing.md) before submitting pull requests.
