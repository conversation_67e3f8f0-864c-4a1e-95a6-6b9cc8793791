# Feature: Recent Items (Recently Viewed)

## Purpose

This feature tracks products that a user has recently viewed and provides functionality to display this list, typically in a dedicated section on pages like Home or Cart.

## Key Functionality

*   **Tracking (Anonymous Users):** For users who are not logged in, viewing a product page triggers adding that product (along with a timestamp) to a list stored in `sessionStorage` (`useAddToRecentItems` hook, `addToRecentItems` action). The list is capped at a certain size (e.g., 20 items), with the oldest items being removed.
*   **Fetching (Logged-in Users):** For logged-in users, the list of recently viewed items is fetched from the server using the `getRecentItems` RTK Query endpoint.
*   **State Management:**
    *   Maintains the list of recently viewed products (including timestamps for local storage) in Redux (`model/recentItemsSlice.ts`).
    *   Handles persistence to `sessionStorage` for anonymous users.
    *   Synchronizes with the server via RTK Query for logged-in users (primarily fetching, as tracking usually happens server-side for logged-in users).
*   **Display:** Provides a UI component (`RecentlyVisitedSection`) to render the list of recently viewed products, usually as a carousel or grid of product cards.

## Components (`ui/`)

*   **`RecentlyVisitedSection.tsx`:** Displays the list of recently viewed products, fetching data based on user authentication status and rendering `ProductCard` widgets.

## State & Logic (`model/`, `lib/`)

*   **`recentItemsSlice.ts`:** Redux slice managing the `RecentItemsState` (list of `RecentItemsProduct`). Includes reducers for `setRecentItems` (used when fetching server data or initializing from storage), `addToRecentItems` (for anonymous users), and `removeFromRecentItems` (potentially unused, but available for local management). Handles `sessionStorage` interaction.
*   **Hooks (`lib/hooks/`):**
    *   `useAddToRecentItems.ts`: Custom hook designed to be used on product pages. It automatically dispatches the `addToRecentItems` action *only if* the user is not logged in and a valid product is provided.
*   **Types (`model/types.ts`):** Defines `RecentItemsState` and `RecentItemsProduct` (extends `Product` with `visitTime`).

## API Integration (`api/`)

*   **`recentItemsApi.ts`:** Defines RTK Query endpoints:
    *   `getRecentItems`: Query to fetch the list of recently viewed products for the authenticated user from the server (`/api/Products/recentlyViewed`). Provides a tag for caching.

## Usage

1.  **Tracking:** The `useAddToRecentItems` hook is called within the `ProductPage` component (`src/pages/ProductPage/ProductPage.tsx`). This automatically adds the viewed product to the local list/sessionStorage if the user is anonymous.
2.  **Display:** The `RecentlyVisitedSection` component is included on pages where recently viewed items should be shown (e.g., Home page, Cart page). It fetches data using `useGetRecentItemsQuery` if the user is logged in or uses the local state (`recentItems` slice) if anonymous.

## Related Features/Entities

*   `entities/product`: The feature tracks and displays product entities.
*   `entities/user`: The user's authentication state determines whether data is tracked locally or fetched from the server.
*   `widgets/product-card`: Used by `RecentlyVisitedSection` to display the items.

## Migration Notes

*   The logic for tracking recently viewed items for *logged-in* users typically resides on the backend. The frontend fetches this list but doesn't usually manage adding items to it via API calls. The current setup correctly reflects this distinction.
