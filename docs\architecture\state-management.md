# State Management Strategy

This project utilizes **Redux Toolkit** for managing global application state and **RTK Query** for data fetching, caching, and server state management.

## Redux Toolkit (Client State)

*   **Purpose:** Manages UI state, user session information, and other client-side data that doesn't directly map to server resources or requires complex asynchronous logic handled by RTK Query.
*   **Slices:** Client state is organized into slices, co-located with the feature or entity they manage (FSD principle).
    *   Examples:
        *   `src/features/cart/model/cartSlice.ts`: Manages the local representation of the cart *before* server sync or for anonymous users.
        *   `src/entities/user/model/userSlice.ts`: Manages authentication token, user data, and initialization status.
        *   `src/features/comparison/model/comparisonSlice.ts`: Manages local comparison list for anonymous users.
        *   `src/features/favorite/model/favoriteSlice.ts`: Manages local favorites list for anonymous users.
        *   `src/features/recent-items/model/recentItemsSlice.ts`: Manages locally viewed items for anonymous users.
*   **Store Configuration:** Defined in `src/app/providers/store.ts`. Combines slice reducers and integrates RTK Query middleware.
*   **Usage:** React components use `useSelector` (from `react-redux`) to read state and `useDispatch` or the typed `useAppDispatch` hook (defined in `store.ts`) to dispatch actions defined in the slices.
*   **Persistence (Anonymous Users):** For anonymous users, relevant state slices (cart, comparison, favorites, recent items) utilize `sessionStorage` via helper functions (`src/features/storage/lib/storageManagement.ts`) within their reducers to persist data across page loads but not browser sessions.

## RTK Query (Server State & API Interaction)

*   **Purpose:** Handles all interactions with the backend API, including data fetching, caching, optimistic updates, and server state synchronization. It manages loading, error, and success states for API requests automatically.
*   **Base API Setup:** Configured in `src/shared/api/api.ts`. Defines the base URL, prepares headers (e.g., adding auth tokens), includes credentials, and sets up global request/response logging and basic error handling.
*   **Endpoint Definitions:** API endpoints (`queries` for data fetching, `mutations` for data modification) are defined by injecting them into the base API (`api.injectEndpoints`). These definitions reside within the `api` segment of the relevant entity or feature slice.
    *   Examples: `src/entities/product/api/productApi.ts`, `src/features/auth/api/authenticationApi.ts`.
*   **Caching & Invalidation:** RTK Query provides automatic caching. Cache lifetime and invalidation are managed using `keepUnusedDataFor` and tag-based invalidation (`providesTags`, `invalidatesTags`) defined in endpoint configurations. This ensures data consistency across the application.
*   **Usage:** Components use auto-generated React hooks (e.g., `useGetProductQuery`, `useAddToCartMutation`) to fetch data or trigger mutations. These hooks abstract away the complexities of request lifecycle management.

## State Synchronization (Logged-in Users)

*   **Initial Load:** The `InitializationProvider` (`src/app/providers/InitializationProvider/`) orchestrates the initial data synchronization when a logged-in user loads the application. It fetches server state for cart, favorites, comparison, etc., using RTK Query hooks and dispatches actions (`setCart`, `setFavorite`, etc.) to update the Redux store, overwriting any stale local data.
*   **On Action:** When a logged-in user performs an action (e.g., adds to cart), the relevant feature hook (e.g., `useAddToCart`) typically:
    1.  Dispatches an action to *optimistically* update the local Redux state.
    2.  Triggers the corresponding RTK Query mutation to update the server.
    3.  If the server update fails, it may dispatch another action to revert the local state change (though often RTK Query's cache invalidation handles consistency).
    4.  RTK Query's cache invalidation (`invalidatesTags`) ensures related queries are refetched, keeping the local state eventually consistent with the server.

## Key Files

*   `src/app/providers/store.ts`: Main Redux store setup.
*   `src/shared/api/api.ts`: Base RTK Query configuration.
*   `src/app/providers/InitializationProvider/`: Logic for initial data sync for logged-in users.
*   `src/features/*/model/`: Feature-specific Redux slices & state logic.
*   `src/entities/*/model/`: Entity-specific Redux slices & state logic.
*   `src/features/*/api/`: Feature-specific RTK Query endpoints.
*   `src/entities/*/api/`: Entity-specific RTK Query endpoints.
*   `src/features/storage/lib/storageManagement.ts`: Helpers for session storage persistence.
