import { ProductCard, ProductCardSkeleton } from '@/widgets/product-card';
import StickyBox from 'react-sticky-box';
import { ExploreBrands } from '../ExploreHeader/ExploreBrands';
import { ExploreTags } from '../ExploreHeader/ExploreTags';
import { CatalogChipses } from './CatalogChipses';
import CardTypeControls from './CatalogContent/CardTypeControls';
import { CatalogSidebarFilters } from './CatalogSidebarFilters';
import { CatalogSort } from './CatalogSort';
import { useExplore } from './hooks/useExplore';

export const ExplorePage = () => {
  const {
    filters,
    filtersLoading,
    handleOpenModalAllFilters,
    onResetFilters,
    onChangeFilter,
    products,
    cardType,
    isLoadingProducts,
    setTypeCard,
    sortBy,
    currentSort,
    handleSortChange,
    chips,
    pageParam,
    bannerData
  } = useExplore();

  return (
    <div className='content lining-nums proportional-nums p-4 relative'>
      {!!bannerData ? (
        pageParam.current && 'tags' in pageParam.current && bannerData ? (
          <ExploreTags data={bannerData} />
        ) : (
          <ExploreBrands />
        )
      ) : null}
      <br />

      <div className='flex gap-[20px] items-start'>
        <StickyBox offsetTop={90} offsetBottom={20}>
          <CatalogSidebarFilters
            filters={filters}
            onChangeFilter={onChangeFilter}
            filtersLoading={filtersLoading}
            handleOpenModalAllFilters={handleOpenModalAllFilters}
            onResetFilters={onResetFilters}
          />
        </StickyBox>
        <div className='flex flex-col gap-[24px] w-full'>
          <div className='flex flex-col gap-[20px]'>
            <div className='flex justify-between'>
              <CatalogSort
                currentSort={currentSort}
                handleSortChange={handleSortChange}
                sortBy={sortBy}
              />
              <CardTypeControls cardType={cardType} setTypeCard={setTypeCard} />
            </div>
            <CatalogChipses
              chips={chips}
              filtersLoading={filtersLoading}
              onChangeFilter={onChangeFilter}
              onResetFilters={onResetFilters}
            />
          </div>
          {cardType === 'tile' ? (
            <div className='grid grid-cols-2 mm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 ll:grid-cols-3 gap-5 xl:grid-cols-4'>
              {!!products.length
                ? products.map((el) => <ProductCard key={el?.id} product={el} />)
                : null}

              {isLoadingProducts
                ? Array.from({ length: 20 }).map((_, index) => <ProductCardSkeleton key={index} />)
                : null}
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};
