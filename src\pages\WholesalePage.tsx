// src/pages/WholesalePage.tsx
// NOTE: Temporary location. Ideally, this would be in src/pages/wholesale-page/ui/WholesalePage.tsx
import React, { useEffect } from 'react';

import { SupplierRequestForm } from '@/features/feedback';
import { scrollToTop } from '@/shared/lib/scrollToTop';

const WholesalePage: React.FC = () => {
  useEffect(() => {
    scrollToTop();
  }, []);

  return (
    <div className="content lining-nums">
      <SupplierRequestForm />
    </div>
  );
};

export default WholesalePage;
