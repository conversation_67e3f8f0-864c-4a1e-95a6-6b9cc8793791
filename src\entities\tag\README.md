# Entity: Tag

## Purpose

Represents **Tags** that can be applied to products, often indicating special statuses like "New", "Sale", "Hit", etc. Tags usually have associated visual styles (colors, icons).

## Key Data Points

*   `id`: Unique identifier for the tag definition (present in API response, though filtering might use the `tag` string).
*   `tag`: The string identifier/text of the tag (e.g., "ХИТ", "НОВИНКА"). This is often used as the unique key in filtering operations.
*   `text_color`: Hex color code for the tag text.
*   `background_color`: Hex color code for the tag background.
*   `description`: Optional description for the tag.
*   `light_icon`, `dark_icon`: Optional associated icons (`ImageSet`) for different themes.

## Structure

*   **`model/types.ts`:** (Implicit) Defines the `Tag` interface based on API responses. Should be explicitly defined here.
*   **`api/tagApi.ts`:** Defines RTK Query endpoints:
    *   `getTag`: Query to fetch detailed information for a *single* tag using its `id` or `tag` string (API uses `id` in the path `/api/Tags/{id}`). Uses `providesTags`.
*   **`ui/TagHeader/TagHeader.tsx`:** A simple UI component intended to display tag information (name, potentially icon/description) at the top of tag-specific pages. *(Currently contains placeholder logic)*.
*   **`index.ts`:** Exports API hooks and UI components.

## Usage

*   **Tag Pages:** The `pages/TagsPage` (*currently `src/components/Catalog/Tags.tsx`*) uses the `useGetTagQuery` hook (when a specific tag identifier is provided in the URL) to potentially fetch and display tag details using the `TagHeader` component.
*   **Filtering:** Tag information (`tag`, `text_color`, `background_color`) is included in filter options fetched by `entities/filter/api/filterApi.ts` (`getFilters` or `getBasicFilters`) and displayed as filter checkboxes/options.
*   **Product Display:** Products (`entities/product`) have a `tags` array. `ProductCard` and other display components render these tags visually using the provided colors.

## Related Entities/Features

*   `entities/product`: Products can have multiple tags.
*   `entities/filter`: Tag data is used within filtering mechanisms.
*   `pages/TagsPage`: Displays tag-specific product listings.

## Migration Notes

*   Explicitly define the `Tag` interface in `model/types.ts`.
*   Refactor the `TagHeader` component to use data fetched from `useGetTagQuery` (if detailed tag pages are required beyond just listing products).
*   Clarify whether filtering and fetching should consistently use the tag `id` or the `tag` string identifier. The `getTag` API uses `id` in the path, while filtering seems to use the `tag` string.
