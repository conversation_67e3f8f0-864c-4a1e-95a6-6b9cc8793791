

import { Icon } from '@iconify-icon/react';
import { Outlet, useNavigate } from 'react-router-dom';
import { Toaster } from 'sonner';

import Footer from '@/components/Footer/Footer';
import { ModalManager, ModalProvider } from '@/features/modals';
import { ModalObserver } from '@/features/observer-modal/ModalObserver';
import { SEO } from '@/features/SEO/SEO';
import { useBodyScrollLock } from '@/hooks/useBodyScrollLock';
import { ErrorBoundaryWrapper } from '@/shared/ui/ErrorBoundary/ErrorBoundaryWrapper';
import CatalogModal from '@/features/modals/ui/modals/CatalogModal';
import CatalogModalMobile from '@/features/modals/ui/modals/CatalogModalMobile';
import { useDevice } from '@/shared/lib/hooks/useDevice';
import { Header, MobileTabbar } from '@/widgets/header';

export const Layout = () => {
  const { isMobile } = useDevice();
  const navigate = useNavigate();
  useBodyScrollLock();


  const ErrorFallback = (error: Error, reset: () => void) => (
    <div className="min-h-[400px] flex items-center justify-center">
      <div className="text-center p-8">
        <h2 className="text-xl font-semibold text-colGreen mb-4">
          Что-то пошло не так
        </h2>
        <p className="text-colDarkGray mb-6">
          Произошла непредвиденная ошибка. Мы уже работаем над её исправлением.
        </p>
        <div className="space-x-4">
          <button
            onClick={reset}
            className="px-4 py-2 bg-colGreen text-white rounded hover:opacity-90"
          >
            Попробовать снова
          </button>
          <button
            onClick={() => navigate('/')}
            className="px-4 py-2 border border-colGreen text-colGreen rounded hover:bg-colSuperLight"
          >
            На главную
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <ModalProvider>
      <SEO />
      <div className="flex flex-col min-h-screen relative">
        <Header />
        {isMobile ? <CatalogModalMobile /> : <CatalogModal />}
        <main className="flex-grow lining-nums proportional-nums">
          <ErrorBoundaryWrapper fallback={ErrorFallback} showToast={true}>
            <Outlet />
          </ErrorBoundaryWrapper>
        </main>
        <MobileTabbar />
        <Footer />
      </div>
      <ModalObserver />
      <ModalManager />
      <Toaster
        visibleToasts={1}
        position="bottom-center"
        toastOptions={{
          duration: 3000,
          classNames: {
            toast: 'p-3 flex gap-1 items-start',
            title: 'text-sm font-semibold text-black',
            description: 'text-sm text-colDarkGray',
            closeButton: 'text-sm text-colDarkGray',
            icon: 'w-8 p-2',
          },
        }}
        icons={{
          success: (
            <Icon
              icon="solar:check-circle-bold"
              width={24}
              height={24}
              className="text-colGreen"
            />
          ),
          error: (
            <Icon
              icon="solar:close-circle-bold"
              width={24}
              height={24}
              className="text-red-600"
            />
          ),
          warning: (
            <Icon
              icon="solar:danger-triangle-bold"
              width={24}
              height={24}
              className="text-amber-500"
            />
          ),
          info: (
            <Icon
              icon="solar:info-circle-bold"
              width={24}
              height={24}
              className="text-blue-500"
            />
          ),
        }}
      />
    </ModalProvider>
  );
};
