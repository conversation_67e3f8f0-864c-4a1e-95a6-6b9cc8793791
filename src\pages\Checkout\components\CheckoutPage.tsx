import type React from 'react';
import { useState, useEffect } from 'react';

import { FormProvider, useForm } from 'react-hook-form';
import { NavLink, useNavigate, useParams } from 'react-router-dom';
import { toast } from 'sonner';

import { useGetOrderQuery, useOrderPaymentMutation, useCreateInvoicePaymentMutation } from '@/entities/order';

import type { GetOrderResponse, OrderDetailsData, VariantPay } from '@/entities/order/api/types';
import arrow from '@/shared/assets/icons/arrow-icon.svg';

import { CheckoutSummary } from './CheckoutSummary';
import { ContactForm } from './ContactForm';
import { DeliveryInfo } from './DeliveryInfo';
import { OrdersList } from './OrdersList';
import { PaymentMethodSelector } from './PaymentMethodSelector';
import { Breadcrumbs } from '@/widgets/breadcrumbs';
import { BadgeAlert } from 'lucide-react';
import { Badge } from '@/shared/ui/badge';

// Removed old OrderData type as GetOrderResponse provides the structure

const CheckoutPage: React.FC = () => {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | undefined>(undefined);
  const navigate = useNavigate();
  const methods = useForm({
    mode: 'onSubmit',
    defaultValues: {
      name: '',
      phone: '',
      email: '',
      comment: '',
    },
  });
  const { orderId } = useParams<{ orderId: string }>();

  const [orderPayment, { isLoading: isPaying }] = useOrderPaymentMutation();
  const [createInvoicePayment, { isLoading: isCreatingInvoice }] = useCreateInvoicePaymentMutation();

  const { data: orderResponse, isLoading, isError, error } = useGetOrderQuery({ number: orderId });
  const orderData: OrderDetailsData | undefined = orderResponse?.data;

  useEffect(() => {
    if (orderData?.company_info?.variants_pay && orderData.company_info.variants_pay.length > 0) {
      const defaultMethod = orderData.company_info.variants_pay.find(p => p.is_default === 1);
      if (defaultMethod) {
        setSelectedPaymentMethod(defaultMethod.code);
      } else {
        setSelectedPaymentMethod(orderData.company_info.variants_pay[0].code);
      }
    }
  }, [orderData]);

  const onSubmit = async (contactFormData: any) => {
    if (!orderData || !selectedPaymentMethod) {
      toast.error('Данные заказа или способ оплаты не найдены.');
      return;
    }

    const orderNumber = orderData.order_number;
    // TODO: Combine contactFormData with order details if needed for payment APIs

    try {
      // toast.info(`Обработка платежа для заказа ${orderNumber} методом ${selectedPaymentMethod}...`);

      switch (selectedPaymentMethod) {
        case 'card': {
          // Note: payment_id is not available from variants_pay, so we cannot send it
          // If you ever get payment_id, add it to the payload
          const payload = { order_number: [orderNumber], code: "card" };
          const response = await orderPayment(payload as any).unwrap();
          const firstLink = response?.data?.[0]?.bill?.link;
          if (firstLink) {
            window.location.href = firstLink;
            return;
          }
          // toast.success('Заказ успешно оплачен!');
          break;
        }
        case 'bank_invoice': {
          const payload = { order_number: orderNumber };
          const response = await createInvoicePayment(payload).unwrap();
          console.log(response)
          const fileUrl = response?.data?.file?.url;
          if (fileUrl) {
            window.open(fileUrl, '_blank', 'noopener');
          }
          toast.success('Счет успешно создан!');
          break;
        }
        default:
          toast.error('Неизвестный способ оплаты.');
          return;
      }

    } catch (paymentError: any) {
      toast.error('Ошибка оплаты заказа.');
      console.error('Payment error:', paymentError);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[300px]">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#15765B] mb-4"></div>
          <p className="text-[#15765B] font-medium">Загрузка данных заказа...</p>
        </div>
      </div>
    );
  }

  if (isError || !orderData) {
    console.error('Order fetching error:', error);
    return (
      <div className="flex items-center justify-center min-h-[300px]">
        <div className="text-center">
          <p className="text-red-600 font-medium mb-4">
            {isError ? 'Ошибка загрузки заказа.' : 'Данные заказа не найдены.'}
          </p>
          <NavLink
            to="/shopping-cart"
            className="text-white font-semibold bg-[#15765B] rounded py-2 px-4 hover:bg-[#0f5a44] transition-colors"
          >
            Вернуться в корзину
          </NavLink>
        </div>
      </div>
    );
  }

  const totalOrderAmount = orderData.total.amount;
  const totalOrderQuantity = orderData.total.quantity;

  return (
    <div className="content mb-10" style={{ fontFeatureSettings: '\'pnum\' on, \'lnum\' on' }}>
      <Breadcrumbs />
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)} className="mx-auto">
          <NavLink to="/shopping-cart" className="inline-flex items-center group">
            <div className="flex items-center mb-4 text-[#727272] hover:text-[#15765B] transition-colors">
              <img src={arrow} alt="" className="w-4 h-4 mr-2 transform" />
              <div className="text-sm group-hover:underline">Вернуться к корзине</div>
            </div>
          </NavLink>
          <div className="flex items-center gap-4">
            <h1 className="font-semibold text-2xl sm:text-3xl text-[#222222]">
              Оплата заказа № {orderData.order_number}
            </h1>
            <div
              className="p-2 rounded-md font-semibold"
              style={{
                backgroundColor: orderData.status.background_color,
                color: orderData.status.text_color,
              }}
            >
              {orderData.status.name}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <OrdersList order={orderData} />
            </div>
            <div className="flex flex-col gap-4">
              <CheckoutSummary
                totalAmount={totalOrderAmount}
                totalQuantity={totalOrderQuantity}
                // deliveryCost and finalAmount might need recalculation or come from API if delivery is chosen
              />
              {orderData?.status.name === 'Новый' && (
                <>
                  <PaymentMethodSelector
                    methods={orderData.company_info.variants_pay}
                    selectedMethod={selectedPaymentMethod}
                    onSelectMethod={setSelectedPaymentMethod}
                  />
                  <button
                    type="submit"
                    // disabled={isPaying || isPayingByCard || isPayingByInvoice } // Example disabled state
                    className="mt-6 w-full bg-[#15765B] text-white py-3 rounded-md font-semibold hover:bg-[#0f5a44] transition-colors disabled:opacity-50"
                  >
                    {/* {isPaying || isPayingByCard || isPayingByInvoice ? 'Обработка...' : 'Оплатить заказ'} */}
                    Оплатить заказ
                  </button>
                </>
              )}
              <DeliveryInfo orderNumber={orderData.order_number} />

            </div>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default CheckoutPage;
