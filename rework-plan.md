# Furnica App Refactoring & FSD Compliance Plan

This document outlines the plan for refactoring the Furnica application to align with Feature-Sliced Design (FSD) principles, improve code consistency, and perform general cleanup.

## Overall Goals:

1.  **FSD Compliance**: Restructure the application into FSD layers (app, pages, widgets, features, entities, shared).
2.  **Code Consistency**: Standardize naming conventions, component structure, and state management patterns.
3.  **Cleanup**: Remove unused code, convert `.jsx` to `.tsx`, improve type safety, and address inconsistencies.
4.  **Maintainability & Scalability**: Improve the overall architecture for easier future development and maintenance.

## I. Checkout / Order Page (`src/pages/Checkout`)

**Current State:**

*   Directory named `Checkout` but primarily handles order payment and details after cart submission.
*   Main component `CheckoutPage.tsx` is nested within `src/pages/Checkout/components`.
*   Uses `useGetOrderQuery`, `useOrderPaymentMutation`, `useCreateInvoicePaymentMutation` from `entities/order`.
*   Potential for unused components within its `components` subdirectory.

**Refactoring Plan:**

1.  **Rename & Relocate Page:**
    *   Rename the page concept from "Checkout" to "Order Details" or "Order Payment".
    *   Create a new FSD page slice: `src/pages/order-payment-page` (or similar, e.g., `order-confirmation-page`).
    *   Move the primary logic from `src/pages/Checkout/components/CheckoutPage.tsx` to this new page slice.
2.  **Decompose `CheckoutPage.tsx` into FSD Slices:**
    *   **Page Component (`src/pages/order-payment-page/ui/OrderPaymentPage.tsx`):** The main layout and composition component.
    *   **Widgets:**
        *   `src/widgets/order-summary`: For displaying order totals, items, and status (currently parts of `CheckoutPage.tsx` and `CheckoutSummary.tsx`).
        *   `src/widgets/payment-processor`: To encapsulate payment method selection and the payment submission button/logic (currently parts of `PaymentMethodSelector.tsx` and `CheckoutPage.tsx` form handling).
        *   `src/widgets/delivery-information`: For displaying/managing delivery info (currently `DeliveryInfo.tsx`).
    *   **Features:**
        *   `src/features/select-payment-method`: The UI and logic for choosing a payment method (from `PaymentMethodSelector.tsx`).
        *   `src/features/submit-order-payment`: The form handling and mutation call for payment (from `CheckoutPage.tsx`'s `onSubmit`).
    *   **Entities:**
        *   `entities/order` (already partially exists): Continue to use for API calls (`orderApi.ts`), types (`types.ts`), and potentially selectors/hooks for order data.
3.  **API & Types (`src/entities/order`):**
    *   Review `orderApi.ts` and `types.ts` for clarity, completeness, and adherence to FSD principles (e.g., clear public API via `index.ts`).
4.  **Cleanup:**
    *   Analyze components in `src/pages/Checkout/components` (e.g., `CartCheckout.tsx`, `CheckoutTotals.tsx`, `CustomRadioButton.tsx`) and determine if they are used, can be refactored into the new FSD structure, or removed.
    *   Ensure all paths are updated after moving files.

## II. Favorites & Comparison Pages

**Current State:**

*   `src/pages/Favorites/Favorites.tsx` and `src/pages/Comparison/Comparison.tsx`.
*   Very similar logic for fetching data, managing categories (via Redux and `useCategorySwitcher` widget), and displaying product lists.
*   Use non-FSD aliases like `@components/Favorites/FavDetail`.

**Refactoring Plan:**

1.  **Relocate Pages:**
    *   `src/pages/favorites-page/ui/FavoritesPage.tsx`
    *   `src/pages/comparison-page/ui/ComparisonPage.tsx`
2.  **Shared Widgets & Features:**
    *   **`src/widgets/product-grid-display` (or `product-list-display`):**
        *   Create a reusable widget to display a list/grid of products. This will be used by Favorites, Comparison, and potentially other pages.
        *   Accepts product data and configuration props.
        *   Internal components like `ProductCard` (assuming it's already an entity or shared component) would be used here.
    *   **`src/widgets/category-filter-panel`:**
        *   Refactor the existing `CategorySwitcher` widget (`src/widgets/category-switcher`) to be more generic if needed, or ensure its FSD placement is correct.
        *   The `useCategorySwitcher` hook should reside within this widget's `model` or `lib` if it's tightly coupled, or be moved to `src/shared/lib/hooks` if it's a generic utility.
    *   **`src/features/toggle-favorite`** (already exists in `src/features/favorite` - ensure FSD compliance).
    *   **`src/features/toggle-comparison`** (already exists in `src/features/comparison` - ensure FSD compliance).
    *   **`src/features/dnd-product-sort` (for Comparison):** If drag-and-drop is complex, it can be a feature. The `DndProvider` setup might belong to the page level or a specific widget that needs it.
3.  **Page-Specific Widgets/Features:**
    *   `FavDetail` and `FavSidebar` (from `Favorites`) should be decomposed. `FavDetail` likely becomes part of the `product-grid-display` usage on the favorites page. `FavSidebar` (if it's just category filtering) would be replaced by `category-filter-panel`.
    *   `ComDetail` (from `Comparison`) would also leverage the `product-grid-display`.
4.  **State Management (Redux):**
    *   Review `favorite` and `comparison` Redux slices. Ensure they are primarily storing IDs or minimal state, with product data itself coming from `entities/product`.
5.  **Cleanup:**
    *   Remove old component paths (e.g., `@components/Favorites`).
    *   Consolidate any duplicated UI logic into the new shared widgets/features.

## III. Content Pages (Contacts, FAQ, PaymentDelivery, Warranty, Wholesale, About)

**Current State:**

*   Located in `src/pages/`.
*   Data fetching for some (Contacts, FAQ, etc.) via `contentEndpoints.ts` (`api/PageContent/get` with a `target` parameter).
*   Types are in `src/types/ServerData/PageContent/` (directory with individual type files like `GetContactsResponse.ts`).
*   `Wholesale.jsx` is a `.jsx` file and seems simpler, mostly embedding a form.
*   `About.tsx` is static.

**Refactoring Plan:**

1.  **Relocate Pages:**
    *   `src/pages/contacts-page/ui/ContactsPage.tsx`
    *   `src/pages/faq-page/ui/FaqPage.tsx`
    *   `src/pages/payment-delivery-page/ui/PaymentDeliveryPage.tsx`
    *   `src/pages/warranty-page/ui/WarrantyPage.tsx`
    *   `src/pages/wholesale-page/ui/WholesalePage.tsx` (convert from `.jsx`)
    *   `src/pages/about-page/ui/AboutPage.tsx`
2.  **Entity for Page Content:**
    *   Create an entity `src/entities/static-content` (or `page-content`).
    *   **Model (`src/entities/static-content/model/slice.ts` or `api.ts`):** Move the RTK Query logic from `contentEndpoints.ts` here. It will provide hooks like `useGetStaticContentQuery({ target: 'contacts' })`.
    *   **Types (`src/entities/static-content/model/types.ts`):** Consolidate or reference the types from `src/types/ServerData/PageContent/` here. The individual response types (`GetContactsResponse`, etc.) can remain or be moved into this entity's type definitions.
3.  **Decompose Page Components:**
    *   **Contacts Page:**
        *   `BranchCard` -> `src/widgets/branch-card/ui/BranchCard.tsx` (if complex enough to be a widget, or a feature if simpler).
        *   `RequisitesTable` -> `src/features/display-requisites/ui/RequisitesTable.tsx`.
        *   `QuestionForm` (from `@helpers/QuestionForm`) -> `src/features/submit-question-form/ui/QuestionForm.tsx`. Consider if this form is used elsewhere; if so, it's a feature. If specific to contacts, it can be part of the `contacts-page` features.
    *   **Wholesale Page:**
        *   `WantToBePartnerFrom` -> `src/features/partner-application-form/ui/WantToBePartnerForm.tsx`. This form seems reusable (also used in About). Convert to `.tsx`.
    *   **About Page:**
        *   `Advantages` (from `@components/Home/Advantages`) -> `src/widgets/advantages-display/ui/Advantages.tsx` (if it's a substantial, reusable block).
        *   `Brands` (from `@components/Home/Brands`) -> `src/widgets/brands-carousel/ui/Brands.tsx` (or similar).
4.  **Cleanup:**
    *   Update all imports after moving files.
    *   Ensure consistent data fetching patterns for pages that need it.
    *   Remove old `@helpers` and `@components` paths.

## IV. Reviews Page (`src/pages/Reviews`)

**Current State:**

*   `src/pages/Reviews/ReviewsPage.jsx`.
*   Routed via `/product/:productId/reviews`.
*   Fetches product and review data. Uses modals for review submission.

**Refactoring Plan:**

1.  **Placement Strategy:** Treat as a dedicated page due to its distinct data fetching and UI.
    *   `src/pages/product-reviews-page/ui/ProductReviewsPage.tsx` (convert from `.jsx`).
2.  **Page Decomposition:**
    *   **Page Component (`ProductReviewsPage.tsx`):** Handles fetching product and review data using existing entity hooks (`useGetProductQuery`, `useGetReviewsQuery`). Manages layout.
    *   **Widgets:**
        *   `src/widgets/product-review-header`: Displays product preview (`ProductPreview` entity component) and overall rating summary.
        *   `src/widgets/review-list`: Displays the list of `Review` entity components.
    *   **Features:**
        *   `src/features/submit-review`: Encapsulates the "Leave Review" button and the logic to show the `review` modal (`useModal`). The modal itself might be defined in `app/providers/modals` or a dedicated `widgets/modals` layer.
        *   `src/features/check-user-review-status`: The `useHasUserReview` hook and associated UI logic (e.g., disabling the review button).
    *   **Entities:**
        *   `entities/product` (for `ProductPreview`).
        *   `entities/review` (for `Review` component, `RatingStars`, API calls).
3.  **Cleanup:**
    *   Convert to `.tsx`.
    *   Ensure clear prop passing and data flow.

## V. Footer (`src/components/Footer`)

**Current State:**

*   `src/components/Footer/Footer.tsx`. Large component with navigation, subscription form, etc.

**Refactoring Plan:**

1.  **Relocate to Widgets:**
    *   `src/widgets/footer/ui/Footer.tsx`.
2.  **Decompose Footer Internals:**
    *   **Features/Shared UI:**
        *   Navigation link groups (About, Buyer, Info) -> `src/features/footer-navigation` or multiple smaller features if logic differs (e.g., `src/shared/ui/LinkList` components styled for the footer).
        *   Subscription form -> `src/features/subscribe-newsletter/ui/SubscribeForm.tsx`.
        *   Social media links -> `src/features/social-links-bar/ui/SocialLinksBar.tsx`.
    *   The accordion logic for mobile can be managed within the `Footer.tsx` widget or encapsulated in a shared UI component if complex.
3.  **API Calls:**
    *   `useGetBasicFiltersQuery` usage needs to be evaluated. If this data is for constructing catalog links, it should be sourced appropriately, perhaps via `entities/filter` or `entities/category`.
4.  **Cleanup:**
    *   Simplify state management for mobile accordions.
    *   Ensure all links are correctly routed.

## VI. ProtectedRoute (`src/components/ProtectedRoute/ProtectedRoute.jsx`)

**Current State:**

*   `src/components/ProtectedRoute/ProtectedRoute.jsx`.
*   Checks authentication and shows auth modal or redirects.

**Refactoring Plan:**

1.  **Relocate & Convert:** - **COMPLETED**
    *   This is an auth-related feature concerning routing.
    *   Moved to `src/features/auth/ProtectedRoute.tsx` (Note: Placed directly in `auth/` instead of `auth/guard/` due to persistent directory creation issues. This can be revisited.)
    *   Converted from `.jsx` to `.tsx`.
2.  **Dependencies:** - **CONFIRMED**
    *   `useAuthContext` (from `src/entities/user/model/AuthContext`) is still in use. (Note: FSD compliance of `AuthContext` path to be reviewed as part of `entities/user` or `entities/session` refactor).
    *   `useModal` (from `src/features/modals/model/context`) is still in use. (Note: FSD compliance of `useModal` path to be reviewed as part of modal system refactor).
3.  **FSD Considerations:**
    *   This component is typically used in the `app/providers/router` configuration.

## VII. General Tasks & Principles

*   **Directory Structure**: Strictly adhere to FSD layers and slices.
*   **Public API (`index.ts` files)**: Create/update for every slice to control exports.
*   **Aliases**: Review and update path aliases in `tsconfig.json` or build configuration to support the new FSD structure (e.g., `@/pages`, `@/widgets`). Remove non-standard ones like `@components` or `@helpers` once their content is refactored.
*   **Global Styles & UI Kit**: Ensure `src/shared/ui` contains genuinely shared, atomic UI components (Button, Input, Loader, etc.). Global styles in `src/app/styles`.
*   **Type Safety**: Convert all `.jsx` to `.tsx`. Add types where missing. Leverage TypeScript for better code quality.
*   **Linting & Formatting**: Run linters and formatters to ensure consistency.
*   **Testing**: Update existing tests and add new ones for refactored components/logic.
*   **Iterative Approach**: Tackle one major section (e.g., Checkout, then Content Pages) at a time to manage complexity.
*   **Temporary File Placements**: Due to persistent tooling issues preventing programmatic creation of nested subdirectories (e.g., `/ui/`, `/model/` within a slice), new FSD-compliant components may be temporarily placed in the parent slice directory (e.g., `src/pages/MyPage.tsx` instead of `src/pages/my-page/ui/MyPage.tsx`). These temporary locations **must** be documented in `cleanup-list.md` for future restructuring.
*   **Router Updates**: When page components are refactored (moved, renamed), `src/app/routing/router.tsx` **must** be updated to reflect new import paths and component names. Add `TODO` comments in `router.tsx` for components still awaiting FSD refactoring.
*   **Directory Cleanup**: After refactoring components out of a directory, if the directory becomes empty, attempt to delete it. If automated deletion fails (e.g., "resource busy" errors), note this in `cleanup-list.md` for manual follow-up or retry.

## VIII. `PageContent.ts` Investigation

*   The file `PageContent.ts` was not found directly.
*   Evidence from `contentEndpoints.ts` suggests `src/types/ServerData/PageContent/` is a directory containing individual type files (e.g., `GetContactsResponse.ts`).
*   **Action**: As part of refactoring content pages, these types will be moved or referenced by the new `src/entities/static-content` entity.

This plan provides a roadmap. Each section will require detailed implementation and careful testing. Communication and iterative reviews will be key to success.
