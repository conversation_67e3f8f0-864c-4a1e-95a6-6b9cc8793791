import React from 'react';

interface SvgIconProps {
  size?: number;
  color?: string;
  stroke?: number;
}

export const Forbidden: React.FC<SvgIconProps> = ({
  size = 24,
  color = 'currentColor',
  stroke = 2
}) => {
  return (
    <svg
      width={size + 'px'}
      height={size + 'px'}
      viewBox='0 0 24 24'
      fill='none'
      color={color}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M18.5 5.5L5.50002 18.4998'
        stroke='currentColor'
        strokeWidth={stroke}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <circle cx='12' cy='12' r='10' stroke='currentColor' strokeWidth='1.5' />
    </svg>
  );
};
