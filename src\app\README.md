# Layer: `app`

## Purpose

The `app` layer is the root of the application setup. It initializes the application environment, connects all the pieces together, and defines global styles and providers.

## Responsibilities

* **Initialization:** Setting up the core application entry point (`main.tsx`, `App.tsx`).
* **Store Configuration:** Initializing and configuring the Redux store, including middleware (RTK Query) (`providers/store.ts`).
* **Routing:** Setting up the main router (`routing/router.tsx`) and providing it to the application.
* **Global Providers:** Wrapping the application in essential context providers (Redux `Provider`, `ModalProvider`, `InitializationProvider`, `ErrorBoundary`).
* **Global Styles:** Defining base styles, importing Tailwind CSS layers, and setting up global CSS variables (`index.css`).
* **Core Layouts:** Defining the main application layout structure (e.g., `layouts/Layout.tsx`) that includes common elements like Header and Footer and the main content outlet (`<Outlet />`).
* **Environment Configuration:** Managing environment-specific settings.

## Structure

* `App.tsx`: Core application component, renders providers and router.
* `index.css`: Global styles, Tailwind imports, CSS variables.
* `main.tsx`: Application entry point, renders the root component.
* `layouts/`: Contains main layout components (e.g., `Layout.tsx`).
* `providers/`: Houses application-level providers like Redux store setup (`store.ts`) and custom context providers (e.g., `InitializationProvider/`).
* `routing/`: Defines the application's routes (`router.tsx`).

## Dependencies

* The `app` layer depends on all other layers (`pages`, `widgets`, `features`, `entities`, `shared`) to compose the final application.
* It often imports providers or setup functions from lower layers (e.g., `store` setup might import reducers from features/entities).

## Key Implementations

* `App.tsx` initializes the React root and renders the Redux `Provider`, `InitializationProvider`, and `RouterProvider`.
* `layouts/Layout.tsx` defines the common structure with Header, Footer, main content area, and integrates the `ModalProvider` and `ModalManager`. It also includes the global `ErrorBoundaryWrapper`.
* `providers/store.ts` configures the Redux store, combining slice reducers and RTK Query middleware.
* `providers/InitializationProvider/` handles the initial data synchronization logic for authenticated users.
* `routing/router.tsx` defines all application routes using `react-router-dom`.
* `index.css` sets up Tailwind and global base styles.

## Best Practices

1. Keep the `app` layer focused on initialization and composition.
2. Don't include business logic in this layer; it should be delegated to the appropriate feature or entity.
3. Minimize the code in the core `App.tsx` - it should primarily orchestrate providers and the router.
4. Keep route definitions modular and organized by feature or section in the routing configuration.
5. Ensure proper provider order to avoid dependency issues (e.g., Redux `Provider` before components that use Redux).
