# Quantity Control Feature

Last updated: 2025-04-25 10:20 UTC

The quantity control feature allows users to increase or decrease the quantity of products in their cart. It includes validation logic to prevent adding more items than available in stock.

## Components

### useQuantityControl Hook

Located at: `src/features/cart/model/hooks/useQuantityControl.ts`

This hook handles the logic for:
- Increasing and decreasing product quantities
- Managing minimum quantity limits (preventing going below 1 when not in removal mode)
- Managing maximum quantity limits based on stock availability
- Syncing quantity changes with the server
- Providing loading states

#### Key Properties

- `quantity`: Current quantity value
- `isLoading`: Loading state during API calls
- `handleIncrease`: Function to increase quantity
- `handleDecrease`: Function to decrease quantity
- `isMinQuantity`: Boolean indicating if at minimum quantity
- `isMaxQuantity`: <PERSON><PERSON>an indicating if at maximum stock limit
- `stockLimitMessage`: Message to display when at stock limit

### QuantityControl Component

Located at: `src/features/cart/ui/controls/QuantityControl.tsx`

This component renders:
- Decrease button (-)
- Current quantity display
- Increase button (+)
- Optional stock limit message

#### Props

- `product`: Cart product object
- `enableRemove`: Boolean to allow removal when quantity is 1
- `className`: Additional CSS classes
- `showMessage`: Bo<PERSON>an to control visibility of the stock limit message

## Stock Limit Behavior

The quantity control implements specific behavior depending on the product's availability:

### When `preorder` is `null`

If a product's `preorder` field is `null`, the quantity is limited by the `stock` value, and when reaching this limit:

1. The "+" button will be disabled
2. If `supplier` value exists (greater than 0), a message is shown displaying the supplier quantity

For example, with this availability object:

```json
"availability": {
  "stock": 15,
  "preorder": null,
  "supplier": 33
}
```

In this scenario:
- The maximum quantity allowed is 15 (based only on `stock`)
- The "+" button will be disabled when quantity reaches 15
- A message "Максимальное количество: 33 шт." will be displayed showing the `supplier` value

### When `preorder` is not `null`

If a product has a `preorder` date value, there is no maximum quantity limit based on stock.

## Implementation Details

### Key Logic

The component uses several key values, all implemented with `useMemo` for performance:

```typescript
// Extract availability values with proper type handling
const stockValue = useMemo(() => {
  return typeof product.availability?.stock === 'number' ? product.availability.stock : 0;
}, [product.availability?.stock]);

const supplierValue = useMemo(() => {
  return typeof product.availability?.supplier === 'number' ? product.availability.supplier : 0;
}, [product.availability?.supplier]);

const hasPreorder = useMemo(() => {
  return product.availability?.preorder !== null;
}, [product.availability?.preorder]);

// Check if current quantity is at maximum stock limit (only compare with stock, not supplier)
const isMaxQuantity = useMemo(() => {
  return !hasPreorder && quantity >= stockValue;
}, [hasPreorder, quantity, stockValue]);

// Message shows supplier value when at max stock
const stockLimitMessage = useMemo(() => {
  return isMaxQuantity && supplierValue > 0
    ? `Максимальное количество: ${supplierValue} шт.` 
    : null;
}, [isMaxQuantity, supplierValue]);
```

### Button Behavior

- The **decrement button** is disabled when:
  - The current quantity is 1 AND `enableRemove` is false
  - The component is in a loading state

- The **increment button** is disabled when:
  - The component is in a loading state
  - `isMaxQuantity` is true (current quantity >= stock value and preorder is null)

### Message Format

When at maximum quantity, if there is a supplier value available, the displayed message follows this format:
```
Максимальное количество: [supplierValue] шт.
```

## Usage Example

```tsx
import { QuantityControl } from '@/features/cart';

// Basic usage
<QuantityControl product={cartProduct} />

// With removal enabled
<QuantityControl product={cartProduct} enableRemove />

// With message hidden
<QuantityControl product={cartProduct} showMessage={false} />
```

## Debugging

The component includes comprehensive debug logging (in development mode) to help diagnose issues:

```
------- useQuantityControl Debug -------
Product ID: [id]
Product name: [name]
Stock: [stockValue]
Supplier: [supplierValue]
Has preorder: [hasPreorder]
Current quantity: [quantity]
Is max quantity: [isMaxQuantity]
Stock limit message: [stockLimitMessage]
--------------------------------------
```

These logs appear in the browser console and can be used to verify that quantity limits are being correctly calculated.
