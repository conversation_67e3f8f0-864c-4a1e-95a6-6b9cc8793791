// src/shared/ui/ErrorEmpty.tsx (Temporary location)
import React from 'react';
import { NavLink } from 'react-router-dom';

interface ErrorEmptyProps {
  title: string;
  desc: string;
  height?: string | number;
  hideBtn?: boolean;
}

export const ErrorEmpty: React.FC<ErrorEmptyProps> = ({ title, desc, height, hideBtn }) => {
  return (
    <div
      style={{ height: height }}
      className='flex justify-center items-center text-center w-full h-full'
    >
      <div className='max-w-[460px] w-full mx-auto lining-nums proportional-nums'>
        <h3 className='text-2xl text-colBlack font-semibold'>{title}</h3>
        <p className='pb-6 pt-3'>{desc}</p>
        {!hideBtn && (
          <NavLink
            className='py-2 px-6 font-semibold text-colGreen border border-colGreen rounded hover:bg-colGreenHover hover:text-white transition-colors'
            to='/'
          >
            На главную
          </NavLink>
        )}
      </div>
    </div>
  );
};

// Default export for easier adoption if needed, can be named export too.
export default ErrorEmpty;
