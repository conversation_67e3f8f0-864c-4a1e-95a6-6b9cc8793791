import type { PriceType } from '@/entities/price';
import { formatPrice } from '@/shared/lib/format/formatPrice';

type PriceProps = {
  price: PriceType;
  alignment?: 'left' | 'center' | 'right';
  variant?:
    | 'default'           // Standard display with unit price
    | 'mobile-cart'       // Compact display for mobile cart
    | 'total'             // Total price display (green)
    | 'total-product-card' // Total price display for product cards
    | 'cart-item'         // Display for cart items
    | 'compact';          // Compact display without unit
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  showDiscount?: boolean;
  showUnit?: boolean;
  showPerUnit?: boolean;
  quantity?: number;
  className?: string;
};

export const PriceDisplay = ({
  price,
  alignment = 'left',
  variant = 'default',
  size = 'md',
  showDiscount = true,
  showUnit = true,
  showPerUnit = false,
  quantity = 1,
  className = '',
}: PriceProps): JSX.Element => {
  // Helper function to get text size class based on size prop
  const getTextSizeClass = (): string => {
    switch (size) {
      case 'xs': return 'text-xs';
      case 'sm': return 'text-sm';
      case 'md': return 'text-base';
      case 'lg': return 'text-lg';
      case 'xl': return 'text-xl';
      default: return 'text-base';
    }
  };

  // Helper function to get font weight class based on variant
  const getFontWeightClass = (): string => {
    switch (variant) {
      case 'mobile-cart': return 'font-normal';
      case 'compact': return 'font-medium';
      default: return 'font-bold';
    }
  };

  // If price is not provided, show "Цена не указана" with the same styling as regular prices
  if (!price) {
    // Use the same styling as the variant would use
    if (variant === 'total') {
      return (
        <p className="font-bold text-xl text-colGreen">Цена не указана</p>
      );
    } else if (variant === 'total-product-card') {
      return (
        <p className="font-bold text-2xl">Цена не указана</p>
      );
    } else {
      const fontWeightClass = getFontWeightClass();
      const textSizeClass = getTextSizeClass();
      return (
        <p className={`${fontWeightClass} ${textSizeClass}`}>Цена не указана</p>
      );
    }
  }


  // Only show "Цена не указана" if price.final is explicitly 0 or null/undefined
  // Make sure we're checking for a valid number that's not 0
  if (!price || price.final === 0 || price.final === null || price.final === undefined || isNaN(Number(price.final))) {
    // Use the same styling as the variant would use
    if (variant === 'total') {
      return (
        <p className="font-bold text-xl text-colGreen">Цена не указана</p>
      );
    } else if (variant === 'total-product-card') {
      return (
        <p className="font-bold text-2xl">Цена не указана</p>
      );
    } else {
      const fontWeightClass = getFontWeightClass();
      const textSizeClass = getTextSizeClass();
      return (
        <p className={`${fontWeightClass} ${textSizeClass}`}>Цена не указана</p>
      );
    }
  }

  // We don't need to calculate total price or text color class here

  // Alignment class
  const alignmentClass = alignment === 'center'
    ? 'justify-center'
    : alignment === 'right'
      ? 'justify-end'
      : 'justify-start';

  // Mobile cart variant (compact)
  if (variant === 'mobile-cart') {
    return (
      <p className={`${getFontWeightClass()} ${getTextSizeClass()} flex items-center space-x-2 proportional-nums lining-nums ${className}`}>
        {price?.final && !isNaN(Number(price.final)) && Number(price.final) > 0
          ? `${formatPrice(price.final)} ${price.currency?.symbol || ''}`
          : 'Цена не указана'}
      </p>
    );
  }

  // Total price variant (green)
  if (variant === 'total') {
    // For products in cart, use the total property if available
    const displayPrice = price?.total && !isNaN(Number(price.total)) && Number(price.total) > 0
      ? price.total
      : price?.final && !isNaN(Number(price.final)) && Number(price.final) > 0
        ? price.final
        : null;

    return (
      <div className={`flex flex-wrap items-center ${alignmentClass} ${className}`}>
        <p className="font-bold text-xl text-colGreen flex items-center">
          {displayPrice
            ? `${formatPrice(displayPrice)} ${price.currency?.symbol || ''}`
            : 'Цена не указана'}
        </p>
      </div>
    );
  }

  // Total price for product card
  if (variant === 'total-product-card') {
    // Simple display logic: if total exists, use it, otherwise use final
    const priceToShow = price?.total || price?.final;

    return (
      <div className={`flex flex-wrap items-center ${alignmentClass} ${className}`}>
        <p className="font-bold text-2xl flex items-center">
          {priceToShow && !isNaN(Number(priceToShow)) && Number(priceToShow) > 0
            ? `${formatPrice(priceToShow)} ${price.currency?.symbol || ''}`
            : 'Цена не указана'}
        </p>
      </div>
    );
  }

  // Compact variant (without unit)
  if (variant === 'compact') {
    return (
      <div className={`flex flex-wrap items-center ${alignmentClass} proportional-nums lining-nums ${className}`}>
        <p className={`${getFontWeightClass()} ${getTextSizeClass()} whitespace-nowrap`}>
          {price?.final && !isNaN(Number(price.final)) && Number(price.final) > 0
            ? `${formatPrice(price.final)} ${price.currency?.symbol || ''}`
            : 'Цена не указана'}
        </p>
        {showDiscount && price?.base !== 0 && price?.discount && (
          <div className="flex items-center ml-2">
            <span className="line-through decoration-colDarkGray text-xs text-colDarkGray whitespace-nowrap">
              {`${formatPrice(price.base)} ${price.currency?.symbol || ''}`}
            </span>
            <span className="ml-2 bg-[#F04438] text-xs font-medium text-white whitespace-nowrap px-[6px] py-[2px] rounded-xl">
              {`${price.discount.percent}%`}
            </span>
          </div>
        )}
      </div>
    );
  }

  // Default variant with full display
  return (
    <div className={`flex flex-col ${alignmentClass} proportional-nums lining-nums ${className}`}>
      <div className="flex items-center gap-1">
        <p className={`${getFontWeightClass()} ${getTextSizeClass()} whitespace-nowrap break-words`}>
          {price?.final && !isNaN(Number(price.final)) && Number(price.final) > 0
            ? `${formatPrice(price.final)} ${price.currency?.symbol || ''}${showUnit && price.unit ? `/${price.unit}` : ''}`
            : 'Цена не указана'
          }
        </p>

        {/* Show discount if applicable */}
        {showDiscount && price?.base !== 0 && price?.discount && (
          <div className="flex items-center ml-2">
            <span className="line-through decoration-colDarkGray text-xs text-colDarkGray whitespace-nowrap">
              {`${formatPrice(price.base)} ${price.currency?.symbol || ''}`}
            </span>
            <span className="ml-2 bg-[#F04438] text-xs font-medium text-white whitespace-nowrap px-[6px] py-[2px] rounded-xl">
              {`${price.discount.percent}%`}
            </span>
          </div>
        )}
      </div>

      {/* Show per unit price if requested and quantity > 1 */}
      {showPerUnit && quantity > 1 && price?.final && (
        <div className="text-xs text-colDarkGray mt-1">
          {`${formatPrice(price.final)} ${price.currency?.symbol || ''}/${price.unit || 'шт'}`}
        </div>
      )}
    </div>
  );
};
