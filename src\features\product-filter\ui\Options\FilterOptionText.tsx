import { ProductFilterURLInput } from '@/components/Catalog/CatalogRoot';
import { FilterCheckbox } from '@/shared/ui';
import { ProductFilterText } from '@/types/Filters/ProductFilter';
import { useRef } from 'react';

interface FilterOptionColorProps {
  data: ProductFilterText;
  onChange: (data: ProductFilterURLInput) => void;
  disabled?: boolean;
}

export const FilterOptionText: React.FC<FilterOptionColorProps> = ({
  data,
  onChange,
  disabled = false
}) => {
  const { current: uuid } = useRef(
    Date.now().toString(36) + Math.random().toString(36).substring(2)
  );

  const handleChange = (data: ProductFilterText, itemId: number, bool: boolean, text: string) => {
    onChange({
      alone: {
        parentId: data.id,
        id: itemId,
        type: 'multiple',
        value: bool,
        title: data.name,
        text
      }
    });
  };

  return (
    <div className='flex flex-col gap-[8px] mt-2'>
      {data.values.map((item) => (
        <FilterCheckbox
          {...item}
          key={item.id}
          id={`${uuid}-${item.id}`}
          disabled={disabled || !item.is_active}
          onChange={(bool) => handleChange(data, item.id, bool, item.text)}
        >
          {item.text}
        </FilterCheckbox>
      ))}
    </div>
  );
};
