// External Libraries
import { useEffect } from 'react';

import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useSelector } from 'react-redux';

import ComDetail from '@/components/Comparison/ComparisonDetail';
import { useComparison } from '@/features/comparison';
import ErrorEmpty from '@/helpers/Errors/ErrorEmpty.jsx';
import { scrollToTop } from '@/shared/lib/scrollToTop';
import { Breadcrumbs } from '@/widgets/breadcrumbs';
import {
  CategorySwitcher,
  useCategorySwitcher,
} from '@/widgets/category-switcher';

import type { RootState } from '@/app/providers/store';

const Comparison = (): JSX.Element => {
  const {
    comparison: allComparisonItems,
    isLoading,
    isError: comparisonIsError,
    refreshFromServer,
    totalCount, // Get totalCount from the hook
  } = useComparison();

  // Get categories directly from Redux store for consistent updates
  const { categories } = useSelector((state: RootState) => state.comparison);

  // Use our new hook for category switching logic
  const {
    selectedCategory,
    filteredProducts: filteredComparison,
    handleCategoryChange,
    isUpdating
  } = useCategorySwitcher(categories, allComparisonItems);

  // Update loading state to include category operations
  const isPageLoading = isLoading || isUpdating;

  // Fetch data when component mounts
  useEffect(() => {
    void refreshFromServer();
  }, [refreshFromServer]);

  useEffect(() => {
    scrollToTop();
  }, []);

  // We're now using the handleCategoryChange from the hook

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="pb-6 content flex flex-col gap-5">
        <Breadcrumbs />
        <h1 className="block pb-5 text-2xl font-semibold md:text-[40px] text-colBlack">
          Сравнение товаров
        </h1>
        {isPageLoading ? <p>Loading categories...</p> : null}
        {!isPageLoading && categories?.length > 0 ? (
          <CategorySwitcher
            categories={categories}
            selectedCategory={selectedCategory}
            onCategoryChange={handleCategoryChange}
            allItemsCount={totalCount} // Pass totalCount to CategorySwitcher
          />
        ) : null}

        {isPageLoading ? (
          // Placeholder for ComDetail Skeleton
          <div className="w-full h-96 bg-gray-200 animate-pulse rounded-md mt-5">
            <p className="text-center p-10 text-gray-500">Loading items...</p>
          </div>
        ) : null}
        {!isPageLoading &&
        !comparisonIsError &&
        filteredComparison &&
        filteredComparison.length > 0 ? (
          <ComDetail comparison={filteredComparison} />
        ) : null}
        {!isLoading &&
        !comparisonIsError &&
        (!filteredComparison || filteredComparison.length === 0) ? (
          <ErrorEmpty
            title="Еще не готовы к покупке?"
            desc="Добавляйте понравившийся товар в сравнение, чтобы сравнить его с аналогами."
            height="420px"
          />
        ) : null}
        {comparisonIsError ? <p>Error loading comparison items.</p> : null}
      </div>
    </DndProvider>
  );
};

export default Comparison;
