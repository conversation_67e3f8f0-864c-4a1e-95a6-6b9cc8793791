import { ReactNode, useEffect, useState } from 'react';
import { RangeContext } from '../model/context';
import { InputRange } from './InputRange';
import { SliderRange } from './SliderRange';

type RangeProps = {
  min: number;
  max: number;
  defaultValue?: [number, number];
  onValueChange?: (value: [number, number]) => void;
  onValueCommit?: (value: [number, number]) => void;
  children: ReactNode;
  disabled: boolean;
};

export const Range = ({
  min,
  max,
  defaultValue = [min, max],
  onValueChange,
  onValueCommit,
  children,
  disabled
}: RangeProps) => {
  const [value, setValue] = useState<[number, number]>(defaultValue);

  const [contextValues, setContextValues] = useState<any>({
    value,
    setValue,
    min,
    max,
    onValueChange,
    onValueCommit,
    disabled
  });

  useEffect(() => {
    setContextValues((prev) => ({ ...prev, disabled }));
    setValue(defaultValue);
  }, [disabled, defaultValue]);

  useEffect(() => {
    setValue(defaultValue);
    setContextValues((prev) => ({ ...prev, value: defaultValue }));
  }, [defaultValue]);

  return (
    <RangeContext.Provider value={contextValues}>
      <div className='flex flex-col gap-2'>{children}</div>
    </RangeContext.Provider>
  );
};

Range.Inputs = InputRange;
Range.Sliders = SliderRange;
