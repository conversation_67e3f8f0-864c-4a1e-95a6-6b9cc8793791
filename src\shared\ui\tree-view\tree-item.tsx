import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/shared/ui/accordion';
import { TreeNode } from '@/types/CategorieTree';
import { NavLink } from 'react-router-dom';

export const TreeItem = ({ node }: { node: TreeNode; isChild?: boolean }) => {
  const hasChildren = node.children && !!node.children.length;

  return (
    <AccordionItem value={node.id.toString()} className='border-none' disabled={!hasChildren}>
      <AccordionTrigger
        isIcon={hasChildren}
        className={`group flex items-center gap-2 py-[4px] px-[7px] rounded-[8px] hover:no-underline hover:bg-[#D7DBDA] truncate`}
      >
        <span className='text-start linting-nums relative w-[calc(100%_-_16px)] min-h-[20px]'>
          {node.link.length ? (
            <NavLink
              to={`/catalog/${node.link}`}
              className={`hover:text-colGreen text-[14px] text-start left-0 top-0 w-full text-start text-wrap ${hasChildren ? '' : 'block'}`}
            >
              {node.label}
              {node.countProducts ? (
                <sup className='text-[12px] lining-nums text-[#B5B5B5] ml-[6px]'>
                  {node.countProducts}
                </sup>
              ) : null}
            </NavLink>
          ) : (
            <span
              className={` text-start text-[14px] left-0 top-0 text-[16px] w-full text-start text-wrap ${hasChildren ? 'hover:text-colGreen' : 'block '}`}
            >
              {node.label}
            </span>
          )}
        </span>
      </AccordionTrigger>

      <AccordionContent className='overflow-hidden pb-1'>
        <div className={`pt-1 pl-4`}>
          {hasChildren ? (
            <Accordion type='multiple' className='border-none'>
              {node.children.map((child) => (
                <TreeItem isChild key={child.id} node={child} />
              ))}
            </Accordion>
          ) : null}
        </div>
      </AccordionContent>
    </AccordionItem>
  );
};
