import { memo } from 'react';
// Remove direct API query import
// import { useGetTagQuery } from '../../api/tagApi';

export interface TagHeaderProps {
  tagId: string;
  className?: string;
}

export const TagHeader = memo(({ tagId, className }: TagHeaderProps) => {
  // Instead of making API calls, we'll display a simpler header
  // The actual tag data will come from the filters response
  
  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      <div className="flex items-center">
        <span
          className="w-10 h-10 rounded-full flex items-center justify-center mr-4 text-xl font-semibold bg-primary text-white"
        >
          #
        </span>
        <div>
          <h1 className="text-2xl font-bold text-colBlack">Tag #{tagId}</h1>
          <div className="mt-2 text-gray-600">
            Products with this tag
          </div>
        </div>
      </div>
    </div>
  );
});

TagHeader.displayName = 'TagHeader';
