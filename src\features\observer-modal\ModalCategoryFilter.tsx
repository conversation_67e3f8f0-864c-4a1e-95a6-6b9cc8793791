import { ObserverItem } from '@/shared/types/observer';
import { ModalFilterCategory } from '@/shared/types/observer/modal.observer';
import { Button } from '@/shared/ui';
import { Accordion } from '@/shared/ui/accordion';
import { TreeItem } from '@/shared/ui/tree-view/tree-item';
import { useModalAnimation } from './hook/useModalAnimation';

interface ModalCategoryFilterProps {
  data: ObserverItem<ModalFilterCategory>;
}

export const ModalCategoryFilter: React.FC<ModalCategoryFilterProps> = ({ data }) => {
  const { isUnvisible } = useModalAnimation(data.observerDismiss);

  console.log(data.data);

  return (
    <>
      <div
        className={`absolute inset-0 bg-black/40 transition-opacity duration-200 ease-in-out ${!isUnvisible ? 'opacity-100' : 'opacity-0'}`}
        style={{
          zIndex: `${data.observerId - 1}`
        }}
      >
        <div
          style={{
            zIndex: `${data.observerId}`,
            maxHeight: '90vh'
          }}
          className={`flex h-[500px] w-[320px]  overflow-hidden gap-4 absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-white p-4 rounded-lg shadow-lg transition-all duration-200 flex flex-col ${
            !isUnvisible ? 'opacity-100 scale-100' : 'opacity-0 scale-[.2]'
          }`}
        >
          <div className='flex-grow overflow-y-scroll scrollable2'>
            <Accordion type='multiple' className='w-full'>
              <TreeItem onClick={data.onCancel} node={data.data} />
            </Accordion>
          </div>
          <Button onClick={data.onCancel} variant={'secondary'}>
            Закрыть
          </Button>
        </div>
      </div>
    </>
  );
};
