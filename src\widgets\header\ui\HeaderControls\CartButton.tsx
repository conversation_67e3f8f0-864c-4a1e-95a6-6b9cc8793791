import type React from 'react';
import { useState, useEffect } from 'react';
import {
  useFloating,
  autoUpdate,
  offset,
  flip,
  shift,
  useHover,
  useFocus,
  useDismiss,
  useRole,
  useInteractions,
  safePolygon,
} from '@floating-ui/react';
import { useLocation, NavLink } from 'react-router-dom';
import { Icon } from '@iconify-icon/react';

import { CartPopover } from './CartPopover';

type CartButtonProps = {
  getCartQuantity: () => number;
};

export const CartButton: React.FC<CartButtonProps> = ({ getCartQuantity }) => {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();

  // Close popover when on cart page
  useEffect(() => {
    if (location.pathname === '/shopping-cart') {
      setIsOpen(false);
    }
  }, [location.pathname]);

  // Cart data is now handled by the CartPopover component

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: setIsOpen,
    placement: 'top',
    // Make sure the tooltip stays on the screen
    whileElementsMounted: autoUpdate,
    middleware: [
      offset(5),
      flip({
        fallbackAxisSideDirection: 'start',
      }),
      shift(),
    ],
  });

  const hover = useHover(context, {
    move: true,
    delay: 500,
    handleClose: safePolygon(),
    enabled: location.pathname !== '/shopping-cart', // Disable hover when on cart page
  });
  const focus = useFocus(context);
  const dismiss = useDismiss(context);
  // Role props for screen readers
  const role = useRole(context, { role: 'tooltip' });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    hover,
    focus,
    dismiss,
    role,
  ]);

  return (
    <>
      <NavLink
        ref={refs.setReference}
        {...getReferenceProps()}
        to="/shopping-cart"
        className="min-w-[70px]"
      >
        {({ isActive }) => (
            <div
              className={`relative flex flex-col`}
            >
              <Icon
                icon={isActive ? 'solar:cart-large-2-bold' : 'solar:cart-large-2-linear'}
                width={24}
                height={24}
                className={isActive ? 'text-colGreen' : 'text-colBlack'}
              />
              <span className="text-xs pt-1 font-medium text-colBlack text-center">
                Корзина
              </span>
              {getCartQuantity() > 0 ? (
          <span className="absolute -top-2 right-0 bg-colGreen h-5 pb-[2px] min-w-[20px] flex justify-center items-center text-xs text-white rounded-full px-1 -z-10">
            {getCartQuantity() > 99 ? '99+' : getCartQuantity()}
          </span>
        ) : null}
            </div>
        )}
      </NavLink>

      {/* Use the CartPopover component */}
      <CartPopover
        isOpen={isOpen}
        refs={refs}
        getFloatingProps={getFloatingProps}
        floatingStyles={floatingStyles}
      />
    </>
  );
};

// Also export as default for backward compatibility
export default CartButton;