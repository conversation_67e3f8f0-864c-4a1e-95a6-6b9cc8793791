// src/features/auth/hooks/useSyncUserData.ts
import { useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { getTokenFromCookies } from '@/entities/user/lib/cookies';
import { useCart } from '@/features/cart/model/hooks/useCart';
import {
  useSendComparisonMutation,
  useGetComparisonQuery,
  setComparison,
} from '@/features/comparison';
import {
  useSendFavoritesMutation,
  useGetFavoritesQuery,
  setFavorite,
} from '@/features/favorite';
import { useGetRecentItemsQuery, setRecentItems } from '@/features/recent-items';
import type { RootState } from '@/app/providers/store';
import type { ProductListRequest } from '@/shared/types/ProductListRequest';

/**
 * Hook to handle synchronization of user data between local storage and server
 * Used during login, logout, and token changes
 *
 * This is a simplified version that performs sync in the background without blocking
 * Now uses the enhanced useCart hook for cart operations
 */
export interface SyncUserDataHook {
  syncOnLogin: () => Promise<void>;
  clearOnLogout: () => void;
  isLoading: boolean;
  clearLocalData: () => void;
}

export const useSyncUserData = (): SyncUserDataHook => {
  const dispatch = useDispatch();
  const token = getTokenFromCookies();
  const [isBackgroundSyncing, setIsBackgroundSyncing] = useState(false);

  // Use the enhanced cart hook
  const {
    cart: cartData,
    syncToServer: syncCartToServer,
    clearData: clearCartData,
    refreshFromServer: refreshCart
  } = useCart();

  // Get current state from Redux with safety
  const comparison = useSelector((state: RootState) => state?.comparison?.comparison || []);
  const favorite = useSelector((state: RootState) => state?.favorite?.favorite || []);

  // API mutation hooks
  const [sendFavorites] = useSendFavoritesMutation();
  const [sendComparison] = useSendComparisonMutation();

  // API query hooks with skip if no token
  const { refetch: refetchFavorites } = useGetFavoritesQuery(undefined, {
    skip: !token,
    refetchOnMountOrArgChange: false
  });
  const { refetch: refetchComparison } = useGetComparisonQuery(undefined, {
    skip: !token,
    refetchOnMountOrArgChange: false
  });
  const { refetch: refetchRecentItems } = useGetRecentItemsQuery(undefined, {
    skip: !token,
    refetchOnMountOrArgChange: false
  });

  // Clear all local storage data
  const clearLocalData = useCallback(() => {
    // Clear cart data using the enhanced hook
    clearCartData();

    // Clear other data
    sessionStorage.removeItem('comparison');
    sessionStorage.removeItem('favorite');
    sessionStorage.removeItem('recentItems');

    // Reset Redux state for other entities
    try {
      dispatch(setComparison([]));
      dispatch(setFavorite([]));
      dispatch(setRecentItems([]));
    } catch (error) {
      console.error('[useSyncUserData] Error clearing local data:', error);
    }
  }, [dispatch, clearCartData]);

  /**
   * Sync user data in the background without blocking
   * This simplified approach prioritizes UX over perfect data consistency
   */
  const syncOnLogin = useCallback(async (): Promise<void> => {
    if (!token || isBackgroundSyncing) {
      return;
    }

    setIsBackgroundSyncing(true);

    try {
      // First, upload local data to the server
      const sendPromises: Promise<any>[] = [];

      // Use the enhanced cart hook to sync cart data
      const cartItems = cartData?.cart;
      if (Array.isArray(cartItems) && cartItems.length > 0) {
        const cartPromise = syncCartToServer(cartItems)
          .catch((err: Error) => console.warn('Error syncing cart:', err));

        if (cartPromise) {
          sendPromises.push(cartPromise);
        }
      }

      if (Array.isArray(comparison) && comparison.length > 0) {
        // Create a proper ProductListRequest object
        const comparisonIds: ProductListRequest = {
          ids: comparison.map((item: any) => item.id)
        };

        sendPromises.push(
          sendComparison(comparisonIds)
            .catch((err: Error) => console.warn('Error syncing comparison:', err))
        );
      }

      if (Array.isArray(favorite) && favorite.length > 0) {
        // Create a proper ProductListRequest object
        const favoriteIds: ProductListRequest = {
          ids: favorite.map((item: any) => item.id)
        };

        sendPromises.push(
          sendFavorites(favoriteIds)
            .catch((err: Error) => console.warn('Error syncing favorites:', err))
        );
      }

      // Wait for all uploads to complete
      if (sendPromises.length > 0) {
        await Promise.allSettled(sendPromises);
      }

      // Then clear local data to prevent duplicates
      clearLocalData();

      // Finally, fetch latest data from server
      const refreshPromise = refreshCart();
      if (refreshPromise) {
        await Promise.allSettled([
          refreshPromise,
          refetchComparison(),
          refetchFavorites(),
          refetchRecentItems()
        ]);
      }
    } catch (error) {
      console.error('[useSyncUserData] Background sync error:', error);
    } finally {
      setIsBackgroundSyncing(false);
    }
  }, [
    token,
    isBackgroundSyncing,
    cartData,
    comparison,
    favorite,
    syncCartToServer,
    sendComparison,
    sendFavorites,
    clearLocalData,
    refreshCart,
    refetchComparison,
    refetchFavorites,
    refetchRecentItems
  ]);

  // Clear data on logout
  const clearOnLogout = useCallback(() => {
    clearLocalData();
  }, [clearLocalData]);

  return {
    syncOnLogin, // Non-blocking background sync
    clearOnLogout, // Clear local data on logout
    isLoading: isBackgroundSyncing, // Simple loading state
    clearLocalData // Utility to clear local data
  };
};