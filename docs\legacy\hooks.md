# Legacy Documentation: `/src/hooks/`

This directory contains top-level custom hooks that need to be co-located with the FSD slices they belong to.

## Modules & Refactoring Targets

1.  **`useModificationAttributesManager.ts`**
    *   **Purpose:** Manages the complex state and logic related to selecting product variants based on modification attributes (color, size, etc.). It calculates available attribute combinations, finds the corresponding product variant, and handles attribute selection changes, including navigation.
    *   **Target FSD:** This hook contains significant business logic tied directly to product variants and attributes. It should reside within the `product` entity, likely in `src/entities/product/lib/hooks/` or `src/entities/product/model/`.

2.  **`useProductAttributes.ts`**
    *   **Purpose:** Similar to `useModificationAttributesManager`, this hook seems to handle logic related to product attributes, possibly for display or interaction, though its exact usage compared to the other hook needs clarification based on where it's implemented.
    *   **Target FSD:** Like the manager hook, this belongs within the `product` entity, likely in `src/entities/product/lib/hooks/` or `src/entities/product/model/`. The distinction and potential overlap with `useModificationAttributesManager` should be reviewed during refactoring.