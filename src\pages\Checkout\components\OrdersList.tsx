import React from 'react';
import type { OrderDetailsData, ApiOrderItem } from '@/entities/order/api/types';
import { OrderItem } from './OrderItem';

interface OrdersListProps {
  order: OrderDetailsData | undefined;
}

export const OrdersList: React.FC<OrdersListProps> = ({ order }) => {
  const getNoun = (number: number, one: string, two: string, five: string) => {
    let n = Math.abs(number);
    n %= 100;
    if (n >= 5 && n <= 20) {
      return five;
    }
    n %= 10;
    if (n === 1) {
      return one;
    }
    if (n >= 2 && n <= 4) {
      return two;
    }
    return five;
  };

  const formatPrice = (price: number) => {
    return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ");
  };

  if (!order) {
    return <p className="p-4 text-center">Данные заказа загружаются или отсутствуют...</p>;
  }

  return (
    <div className="p-4">
      <OrderItem
        key={order.order_number}
        order={order}
      />
    </div>
  );
};
