import { NavLink } from 'react-router-dom';

import { QuantityControl } from '@/features/cart';
import { useProductCard } from '@/widgets/product-card';

import { PreviewGallery } from './PreviewGallery';

import type { Product } from '@/entities/product';

interface ProductCardPopoverProps {
  product: Product;
}

export const ProductCardPopover = ({ product }: ProductCardPopoverProps): JSX.Element | null => {
  const { productInCart } = useProductCard(product);

  if (!productInCart) return null;

  // Calculate the total price based on quantity
  const totalPrice = productInCart.price?.final
    ? productInCart.price.final * productInCart.quantity
    : 0;

  return (
    <div className="flex flex-1">
      <div className="cursor-pointer flex-1 overflow-hidden bg-gray-100 rounded-md">
        <div className="flex p-2 flex-1">
          {/* Product Image */}
          <div className="min-w-[70px] w-[70px] h-full relative bg-white rounded-md overflow-hidden">
            <PreviewGallery
              product={product}
              showButtons={false}
              showTags={false}
              className="w-full h-full"
            />
          </div>

          {/* Product Details */}
          <div className="flex flex-col gap-1 ml-2 grow">
            {/* Product Name */}
            <div className="font-semibold text-sm line-clamp-2">
              {product?.fullName}
            </div>

            {/* Quantity Control and Total Price */}
            <div className="flex items-center justify-between mt-auto">
              <div className="basis-1/2">
                <QuantityControl
                  product={productInCart}
                  enableRemove={true}
                />
              </div>
              <div className="flex flex-col items-end">
                {/* Total Price */}
                <div className="text-sm font-semibold">
                  {totalPrice
                    ? `${totalPrice} ${productInCart?.price?.currency?.symbol || ''}`
                    : 'Цена не указана'}
                </div>

                {/* Price per unit */}
                <div className="text-xs text-colDarkGray">
                  {productInCart?.price?.final
                    ? `${productInCart.price.final} ${productInCart.price.currency?.symbol || ''}/${productInCart.price.unit || 'шт'}`
                    : ''}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
