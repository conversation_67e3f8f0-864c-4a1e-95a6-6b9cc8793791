import { useProductFilterContext } from './hook/useProductFIlterContext';
import { FilterTriggerTitle } from './ui/Trigger/FilterTriggerTitle';

export const ProductFilterTrigger = () => {
  const { data } = useProductFilterContext();

  const multiple = () => {
    switch (data.type) {
      case 'color':
        return <FilterTriggerTitle />;
      case 'text':
        return <FilterTriggerTitle />;
      case 'image':
        return <FilterTriggerTitle />;
    }
  };

  switch (data.input_type) {
    case 'multiple': {
      return multiple();
    }
    case 'range': {
      return <FilterTriggerTitle />;
    }
  }
};
