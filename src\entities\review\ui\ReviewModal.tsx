import React, { useState, useEffect } from 'react';

import { Box, Modal } from '@mui/material';
import { Controller, useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { NavLink } from 'react-router-dom';
import { toast } from 'sonner';

import { useGetProductQuery } from '@/entities/product';
import {
  useSetReviewMutation,
  useUpdateSelfReviewMutation,
} from '@/entities/review';
import { RatingStars } from '@/entities/review/ui/RatingStars';
import { useModal } from '@/features/modals/model/context';
import noImg from '@/shared/assets/images/no-image.png';
import { CTextField } from '@/shared/ui/inputs/CTextField';
import { Loading } from '@/shared/ui/Loader';

export const ReviewModal = () => {
  const [rating, setRating] = useState(5);
  const { hideModal, isModalVisible, modalContent } = useModal();

  const editReview = modalContent?.editReview;
  const isEditMode = Boolean(editReview);
  const variantId = modalContent?.variantId;

  // Reset form and state when modal closes
  const handleClose = () => {
    reset({
      text: '',
      advantages: '',
      disadvantages: '',
    });
    setRating(5);
    hideModal();
  };

  const { data: product, isLoading: isProductLoading } = useGetProductQuery(
    variantId,
    { skip: !variantId }
  );

  // Find the variant that matches the variantId
  const currentProduct = product?.data?.variants?.find(
    (variant) => variant?.id === Number(variantId)
  );

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { errors, isValid },
  } = useForm({
    mode: 'onChange',
    defaultValues: {
      text: '',
      advantages: '',
      disadvantages: '',
    },
  });

  // Set initial values for edit mode
  useEffect(() => {
    if (isEditMode && editReview) {
      setValue('text', editReview.comment || '');
      setValue('advantages', editReview.pluses || '');
      setValue('disadvantages', editReview.minuses || '');
      setRating(editReview.rating || 5);
    } else {
      reset({
        text: '',
        advantages: '',
        disadvantages: '',
      });
      setRating(5);
    }
  }, [isEditMode, editReview, setValue, reset]);

  // Mutations for create/update
  const [setReview, { isLoading: isCreating }] = useSetReviewMutation();
  const [updateReview, { isLoading: isUpdating }] =
    useUpdateSelfReviewMutation();

  const isSubmitting = isCreating || isUpdating;

  const onSubmit = async (data) => {
    if (!currentProduct) return;

    const reviewData = {
      variant_id: currentProduct.id,
      rating,
      comment: data.text,
      pluses: data.advantages,
      minuses: data.disadvantages,
    };

    try {
      let result;

      if (isEditMode && editReview) {
        result = await updateReview({
          ...reviewData,
          review_id: editReview.review_id,
        }).unwrap();
        if (result.success) {
          toast.success('Отзыв успешно обновлен');
          handleClose();
        } else {
          toast.error('Ошибка при обновлении отзыва');
        }
      } else {
        result = await setReview(reviewData).unwrap();
        if (result.success) {
          toast.success('Отзыв успешно добавлен');
          handleClose();
        } else {
          toast.error('Ошибка при добавлении отзыва');
        }
      }
    } catch (err) {
      console.error('Error with review:', err);
      toast.error('Произошла ошибка при сохранении отзыва');
    }
  };

  if (isProductLoading) {
    return (
      <Modal
        open={isModalVisible ? modalContent?.type === 'review' : null}
        onClose={handleClose}
      >
        <Box className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg p-8">
          <Loading />
        </Box>
      </Modal>
    );
  }

  if (!currentProduct) {
    return null;
  }

  return (
    <Modal
      open={isModalVisible ? modalContent?.type === 'review' : null}
      onClose={handleClose}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 lining-nums proportional-nums bg-white rounded-lg border-none outline-none py-10 px-8 max-w-[500px] w-full">
        <span
          onClick={handleClose}
          className="absolute top-0 right-0 text-4xl text-colGray font-light cursor-pointer pr-4"
        >
          &times;
        </span>
        <form className="flex flex-col gap-5" onSubmit={handleSubmit(onSubmit)}>
          <div className=" font-semibold text-3xl">Ваш отзыв о товаре</div>

          <div className="flex p-2">
            <div className="w-20 h-20 rounded mr-5">
              <NavLink
                to={`/catalog/${product?.data?.category_chain?.[0]?.slug}/${currentProduct?.slug}`}
                className="cursor-pointer min-w-[56px] w-14 h-14 overflow-hidden bg-gray-100 rounded-md"
              >
                <img
                  className="w-full h-full object-contain"
                  src={currentProduct?.files?.[0]?.large || noImg}
                  onError={(e) => {
                    e.target.onError = null;
                    e.target.src = noImg;
                  }}
                  alt="*"
                />
              </NavLink>
            </div>
            <div className="flex flex-col gap-2">
              <div className="text-sm font-medium">{`${currentProduct?.groupName} ${currentProduct?.name}`}</div>
              <div className="text-xs font-medium text-colDarkGray flex items-center space-x-1">
                {currentProduct?.sku}
              </div>

              <div className="flex gap-x-2 flex-wrap">
                {product?.data?.attributes?.map((attribute, index) => (
                  <p
                    key={index}
                    className="text-xs font-medium text-colDarkGray flex items-center space-x-1"
                  >
                    <span>{attribute?.name}:</span>
                    <span>{attribute?.values?.[0]?.text}</span>
                  </p>
                ))}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3 ">
            <div className=" font-semibold text-lg">Общая оценка</div>
            <div className="flex">
              <RatingStars
                totalStars={5}
                initialRating={rating}
                isActive={true}
                handleSetRating={setRating}
              />
            </div>
          </div>

          <div>
            <div className=" font-semibold text-lg mb-3">Мнение о товаре</div>
            <Controller
              name="text"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <CTextField
                  multiline
                  minRows={2}
                  label="Напишите ваше мнение"
                  type="text"
                  onChange={field.onChange}
                  value={field.value}
                />
              )}
            />
          </div>
          <div>
            <div className=" font-semibold text-lg mb-3">Плюсы</div>
            <Controller
              name="advantages"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <CTextField
                  multiline
                  minRows={2}
                  label="Напишите ваше мнение"
                  type="text"
                  onChange={field.onChange}
                  value={field.value}
                />
              )}
            />
          </div>
          <div>
            <div className=" font-semibold text-lg mb-3">Минусы</div>
            <Controller
              name="disadvantages"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <CTextField
                  multiline
                  minRows={2}
                  label="Напишите ваше мнение"
                  type="text"
                  onChange={field.onChange}
                  value={field.value}
                />
              )}
            />
          </div>
          <button className="py-3 flex justify-center text-white font-semibold bg-colGreen w-full rounded cursor-pointer">
            {isEditMode ? 'Обновить отзыв' : 'Отправить отзыв'}
          </button>
        </form>
      </Box>
    </Modal>
  );
};
