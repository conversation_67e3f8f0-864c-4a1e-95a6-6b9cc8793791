// src/features/comparison/model/comparisonSlice.ts
import { createSlice, type PayloadAction } from '@reduxjs/toolkit';

import { getTokenFromCookies } from '@/entities/user';
import { saveToSessionStorage } from '@/features/storage/lib';

import type { ComparisonState } from './types';
import type {
  ProductListCategoryChain,
  CategoryWithImage,
} from '@/entities/category';
import type { Product } from '@/entities/product';

// Exporting parseCategories
export const parseCategories = (
  products: Product[],
): ProductListCategoryChain[] => {
  if (!products?.length) {
    return [];
  }

  const categoryMap = products.reduce(
    (acc, product) => {
      const categoryId = product.category.id;

      if (!acc.has(categoryId)) {
        acc.set(categoryId, {
          category: product.category,
          count: 1,
        });
      } else {
        acc.get(categoryId)!.count++;
      }

      return acc;
    },
    new Map<number, { category: Product['category']; count: number }>(),
  );

  return Array.from(categoryMap.values()).map(
    ({ category, count }) => ({
      chain: [
        {
          id: category.id,
          name: category.name,
          slug: category.slug || '',
          image: category.image || '',
          product_count: count, // count of products in this category from the list
        } as CategoryWithImage,
      ],
      count,
    }),
  );
};

const initialState: ComparisonState = {
  comparison: [],
  categories: [],
  totalCount: 0, // Initialize totalCount
};

export const comparisonSlice = createSlice({
  name: 'comparison',
  initialState,
  reducers: {
    setComparison: (state, action: PayloadAction<Product[]>) => {
      state.comparison = action.payload || [];
      state.categories = parseCategories(state.comparison);
      state.totalCount = state.comparison.length; // Also update totalCount for direct sets or unauthenticated users
    },
    addToComparison: (state, action: PayloadAction<Product>) => {
      const token = getTokenFromCookies();
      if (!state.comparison.find((p) => p.id === action.payload.id)) {
        state.comparison.push({ ...action.payload });
        state.categories = parseCategories(state.comparison);
        if (!token) {
          saveToSessionStorage('comparison', state.comparison);
        }
      }
    },
    removeFromComparison: (state, action: PayloadAction<Product>) => {
      const token = getTokenFromCookies();
      state.comparison = state.comparison.filter(
        (item) => item.id !== action.payload.id,
      );
      state.categories = parseCategories(state.comparison);
      if (!token) {
        saveToSessionStorage('comparison', state.comparison);
      }
    },
    setServerProvidedCategories: (
      state,
      action: PayloadAction<ProductListCategoryChain[]>
    ) => {
      state.categories = action.payload || [];
    },
    setComparisonWithTotalCount: (
      state,
      action: PayloadAction<{ products: Product[]; totalCount: number }>
    ) => {
      state.comparison = action.payload.products || [];
      state.categories = parseCategories(state.comparison); // This uses the products from the current comparison list
      state.totalCount = action.payload.totalCount || 0;
    },
  },
});

export const {
  setComparison,
  addToComparison,
  removeFromComparison,
  setServerProvidedCategories,
  setComparisonWithTotalCount, // Export the new action
} = comparisonSlice.actions;

export const comparisonReducer = comparisonSlice.reducer;
