// src/shared/lib/hooks/useDevice.ts
import { useState, useEffect } from 'react';

const DESKTOP_BREAKPOINT = 1024; // Corresponds to Tailwind's 'lg' breakpoint

interface DeviceInfo {
  isMobile: boolean;
  isDesktop: boolean;
}

export const useDevice = (): DeviceInfo => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>(() => {
    if (typeof window === 'undefined') {
      // Default for SSR or environments without window
      return { isMobile: false, isDesktop: true };
    }
    const isMobileView = window.innerWidth < DESKTOP_BREAKPOINT;
    return { isMobile: isMobileView, isDesktop: !isMobileView };
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      const isMobileView = window.innerWidth < DESKTOP_BREAKPOINT;
      setDeviceInfo({ isMobile: isMobileView, isDesktop: !isMobileView });
    };

    window.addEventListener('resize', handleResize);
    // Call handler right away so state gets updated with initial window size
    handleResize();

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return deviceInfo;
};
