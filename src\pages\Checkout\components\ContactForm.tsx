import React from 'react';
import { useFormContext } from 'react-hook-form';

export const ContactForm: React.FC = () => {
  const { 
    register, 
    formState: { errors } 
  } = useFormContext();

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100">
      <div className="p-4 bg-gray-50 border-b border-gray-100">
        <h3 className="font-bold text-lg">Контактная информация</h3>
      </div>
      
      <div className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Name field */}
          <div className="col-span-1">
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Имя <span className="text-red-500">*</span>
            </label>
            <input
              id="name"
              type="text"
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:ring-colGreen focus:border-colGreen ${
                errors.name ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Иван Иванов"
              {...register('name', { 
                required: 'Имя обязательно для заполнения',
                minLength: { value: 2, message: 'Имя должно содержать минимум 2 символа' }
              })}
            />
            {errors.name && (
              <p className="mt-1 text-xs text-red-500">{errors.name.message as string}</p>
            )}
          </div>
          
          {/* Phone field */}
          <div className="col-span-1">
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
              Телефон <span className="text-red-500">*</span>
            </label>
            <input
              id="phone"
              type="tel"
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:ring-colGreen focus:border-colGreen ${
                errors.phone ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="+7 (___) ___-__-__"
              {...register('phone', { 
                required: 'Телефон обязателен для заполнения',
                pattern: {
                  value: /^(\+7|8)[\s\-]?\(?\d{3}\)?[\s\-]?\d{3}[\s\-]?\d{2}[\s\-]?\d{2}$/,
                  message: 'Неверный формат телефона'
                }
              })}
            />
            {errors.phone && (
              <p className="mt-1 text-xs text-red-500">{errors.phone.message as string}</p>
            )}
          </div>
          
          {/* Email field */}
          <div className="col-span-1">
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email <span className="text-red-500">*</span>
            </label>
            <input
              id="email"
              type="email"
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:ring-colGreen focus:border-colGreen ${
                errors.email ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="<EMAIL>"
              {...register('email', { 
                required: 'Email обязателен для заполнения',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Неверный формат email'
                }
              })}
            />
            {errors.email && (
              <p className="mt-1 text-xs text-red-500">{errors.email.message as string}</p>
            )}
          </div>
          
          {/* Comment field */}
          <div className="col-span-1 md:col-span-2">
            <label htmlFor="comment" className="block text-sm font-medium text-gray-700 mb-1">
              Комментарий к заказу
            </label>
            <textarea
              id="comment"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-colGreen focus:border-colGreen"
              placeholder="Дополнительные пожелания к заказу"
              {...register('comment')}
            />
          </div>
        </div>
        
        <div className="mt-4 text-sm text-gray-500">
          <p>Поля, отмеченные <span className="text-red-500">*</span>, обязательны для заполнения</p>
        </div>
      </div>
    </div>
  );
};
