import { memo, useState, useCallback } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { useGetCategoryTreeQuery } from '@/entities/category';
import { ArrowIcon } from '@/shared/ui';

import { CatalogAccordionSkeleton } from './CatalogAccordionSkeleton';

interface CatalogAccordionProps {
  categoryId?: string | null;
  className?: string;
}

export const CatalogAccordion = memo(({ categoryId, className = '' }: CatalogAccordionProps) => {
  const {
    data: categories,
    isLoading,
    isSuccess,
  } = useGetCategoryTreeQuery(categoryId);
  
  const location = useLocation();

  const [accordion, setAccordion] = useState<{
    parent: string | null;
    child: string | null;
    childLast: string | null;
  }>({
    parent: null,
    child: null,
    childLast: null,
  });

  const toggleAccordion = (type: 'parent' | 'child' | 'childLast', id: string): void => {
    setAccordion((prevState) => ({
      ...prevState,
      [type]: prevState[type] === id ? null : id,
    }));
  };
  
  // Helper function to preserve URL parameters when navigating to a category
  const getCategoryUrl = useCallback((slug: string): string => {
    // Get current pathname and search params
    const currentPath = location.pathname;
    const currentSearch = location.search;
    
    // Check if we're already on this category
    if (currentPath.includes(`/catalog/${slug}`)) {
      // Return the full URL including search params to preserve them
      return `${currentPath}${currentSearch}`;
    }
    
    // Otherwise, return just the new category path without params
    return `/catalog/${slug}`;
  }, [location]);

  if (isLoading) {
    return <CatalogAccordionSkeleton className={className} />;
  }

  if (!isSuccess || !categories?.children?.length) {
    return null;
  }

  return (
    <div className={className}>
      <ul className="space-y-2">
        {categories.children.map((category) => (
          <li key={category.id} className="pl-3">
            <div className="flex justify-between">
              <NavLink
                to={getCategoryUrl(category.slug)}
                className="text-colBlack leading-5 font-semibold cursor-pointer"
                preventScrollReset={true} // Prevent scroll reset when clicking
              >
                <p className="relative max-w-[170px]">
                  {category.name}
                  <span className="absolute text-colGray font-[400] text-xs pl-2">
                    ({category.product_count})
                  </span>
                </p>
              </NavLink>
              {category.children?.length > 0 && (
                <ArrowIcon
                  onClick={() => toggleAccordion('parent', category.id)}
                  className={`${
                    accordion.parent !== category.id && 'rotate-[180deg]'
                  } cursor-pointer !m-0 !w-4 !h-4`}
                />
              )}
            </div>
            
            {/* Level 2 categories */}
            <div
              className={`${
                accordion.parent === category.id ? 'block' : 'hidden'
              } pl-5 pt-1 space-y-1`}
            >
              {category.children?.map((child) => (
                <div key={child.id}>
                  <div className="flex justify-between items-center">
                    <NavLink
                      to={getCategoryUrl(child.slug)}
                      className="text-colBlack text-sm leading-4 font-semibold cursor-pointer"
                      preventScrollReset={true} // Prevent scroll reset when clicking
                    >
                      <p className="relative max-w-[140px] w-full">
                        {child.name}
                        <span className="absolute text-colGray font-[400] text-xs pl-2">
                          ({child.product_count})
                        </span>
                      </p>
                    </NavLink>
                    {child.children?.length > 0 && (
                      <ArrowIcon
                        onClick={() => toggleAccordion('child', child.id)}
                        className={`${
                          accordion.child !== child.id && 'rotate-[180deg]'
                        } cursor-pointer !m-0 !w-4 !h-4`}
                      />
                    )}
                  </div>
                  
                  {/* Level 3 categories */}
                  <div
                    className={`${
                      accordion.child === child.id ? 'block' : 'hidden'
                    } pl-5 pb-2 space-y-1`}
                  >
                    {child.children?.map((item) => (
                      <div key={item.id}>
                        <div className="flex justify-between">
                          <NavLink
                            to={getCategoryUrl(item.slug)}
                            className="text-colBlack leading-5 text-sm cursor-pointer relative flex"
                            preventScrollReset={true} // Prevent scroll reset when clicking
                          >
                            <p className="relative max-w-[140px] w-full leading-4">
                              {item.name}
                              <span className="absolute text-colGray font-[400] text-xs pl-2">
                                ({item.product_count})
                              </span>
                            </p>
                          </NavLink>
                          {item.children?.length > 0 && (
                            <ArrowIcon
                              onClick={() => toggleAccordion('childLast', item.id)}
                              className={`${
                                accordion.childLast !== item.id && 'rotate-[180deg]'
                              } cursor-pointer !m-0 !w-4 !h-4`}
                            />
                          )}
                        </div>
                        
                        {/* Level 4 categories */}
                        <div
                          className={`${
                            accordion.childLast === item.id ? 'block' : 'hidden'
                          } pl-2 pb-2 pt-1`}
                        >
                          {item.children?.map((itemChild) => (
                            <NavLink
                              to={getCategoryUrl(itemChild.slug)}
                              key={itemChild.id}
                              className="text-colBlack leading-5 text-sm cursor-pointer relative flex"
                              preventScrollReset={true} // Prevent scroll reset when clicking
                            >
                              <p className="relative max-w-[140px] w-full">
                                {itemChild.name}
                                <span className="absolute text-colGray font-[400] text-xs pl-2">
                                  ({itemChild.product_count})
                                </span>
                              </p>
                            </NavLink>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
});

CatalogAccordion.displayName = 'CatalogAccordion';
