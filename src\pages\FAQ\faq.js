 const questions = 
  [
    {
      id:  '1',
      group: 'Часто задаваемые вопросы',
      qa: [
        {
          question: 'Что такое React.js и зачем он нужен?',
          answer: 'React.js - это фреймворк для создания интерактивных веб-приложений. Он позволяет создавать интерактивные веб-приложения с помощью JavaScript, CSS и HTML.',
        },
        {
          question:  'Что такое React.js и зачем он нужен?', 
          answer: 'React.js - это фреймворк для создания интерактивных веб-приложений. Он позволяет создавать интерактивные веб-приложения с помощью JavaScript, CSS и HTML.',
        },
        {
          question:  'Что такое React.js и зачем он нужен?', 
          answer: 'React.js - это фреймворк для создания интерактивных веб-приложений. Он позволяет создавать интерактивные веб-приложения с помощью JavaScript, CSS и HTML.',
        },
      ]
    },
    {
      id: '2',
      group: 'Вопросы по товару',
      qa: [
        {
          question: 'Что такое React.js и зачем он нужен?',
          answer: 'React.js - это фреймворк для создания интерактивных веб-приложений. Он позволяет создавать интерактивные веб-приложения с помощью JavaScript, CSS и HTML.',
        },
        {
          question:  'Что такое React.js и зачем он нужен?', 
          answer: 'React.js - это фреймворк для создания интерактивных веб-приложений. Он позволяет создавать интерактивные веб-приложения с помощью JavaScript, CSS и HTML.',
        },
        {
          question:  'Что такое React.js и зачем он нужен?', 
          answer: 'React.js - это фреймворк для создания интерактивных веб-приложений. Он позволяет создавать интерактивные веб-приложения с помощью JavaScript, CSS и HTML.',
        },
        {
          question:  'Что такое React.js и зачем он нужен?', 
          answer: 'React.js - это фреймворк для создания интерактивных веб-приложений. Он позволяет создавать интерактивные веб-приложения с помощью JavaScript, CSS и HTML.',
        },
      ]
    },
    {
      id:  '3',
      group: 'Вопросы по доставке',
      qa: [
        {
          question: 'Что такое React.js и зачем он нужен? Что такое React.js и зачем он нужен? Что такое React.js и зачем он нужен?',
          answer: 'React.js - это фреймворк для создания интерактивных веб-приложений. Он позволяет создавать интерактивные веб-приложения с помощью JavaScript, CSS и HTML.',
        },
        {
          question:  'Что такое React.js и зачем он нужен?', 
          answer: 'React.js - это фреймворк для создания интерактивных веб-приложений. Он позволяет создавать интерактивные веб-приложения с помощью JavaScript, CSS и HTML.',
        },
        {
          question:  'Что такое React.js и зачем он нужен?', 
          answer: 'React.js - это фреймворк для создания интерактивных веб-приложений. Он позволяет создавать интерактивные веб-приложения с помощью JavaScript, CSS и HTML.',
        },
        {
          question:  'Что такое React.js и зачем он нужен?', 
          answer: 'React.js - это фреймворк для создания интерактивных веб-приложений. Он позволяет создавать интерактивные веб-приложения с помощью JavaScript, CSS и HTML.',
        },
        {
          question:  'Что такое React.js и зачем он нужен?', 
          answer: 'React.js - это фреймворк для создания интерактивных веб-приложений. Он позволяет создавать интерактивные веб-приложения с помощью JavaScript, CSS и HTML.',
        },
      ]
    },
  ]
  
  export default questions