# Architecture: Feature-Sliced Design (FSD) Overview

This project adopts the Feature-Sliced Design (FSD) methodology to structure its frontend codebase. FSD promotes modularity, scalability, and maintainability by organizing code into distinct layers and slices.

## Core Principles

* **Layered Architecture:** Code is organized into layers with a strict top-down dependency rule (higher layers can import from lower layers, but not vice-versa).
* **Slice-Based Organization:** Within most layers, code is further divided into "slices" based on business domains (e.g., `product`, `user`, `cart`).
* **Standardized Segments:** Each slice follows a predictable internal structure with segments like `ui`, `model`, `api`, `lib`.

## Layers in Furnica

1. **`app`**:
   * **Purpose:** Global application setup.
   * **Contents:** Store initialization (Redux), router setup, global styles, core providers (Auth, Theme), main layout components.
   * **Location:** `src/app/`

2. **`pages`**:
   * **Purpose:** Defines application routes and composes UI for specific pages.
   * **Contents:** Page-level components that primarily import and arrange widgets and features. Minimal logic.
   * **Location:** `src/pages/`

3. **`widgets`**:
   * **Purpose:** Composite UI components representing distinct sections of the interface. They orchestrate features and entities.
   * **Contents:** Components like `Header`, `Footer`, `ProductCard`, `CatalogSidebar`. They have knowledge of the business domain they serve but not the overall application context.
   * **Location:** `src/widgets/`

4. **`features`**:
   * **Purpose:** Implements specific user interactions and business scenarios (Use Cases).
   * **Contents:** Logic and UI related to actions like `AddToCart`, `UserAuthentication`, `ProductSearch`, `AddToFavorites`. Features interact with entities and the `shared` layer.
   * **Location:** `src/features/`

5. **`entities`**:
   * **Purpose:** Represents core business domain objects and their associated logic/data.
   * **Contents:** Data models (TypeScript interfaces), API endpoints (RTK Query slices) related to specific entities like `Product`, `User`, `Order`, `Category`, `Brand`. May include simple UI components directly representing the entity (e.g., `UserAvatar`, `ProductPreview`).
   * **Location:** `src/entities/`

6. **`shared`**:
   * **Purpose:** Contains reusable code that is not specific to any business domain. It's the lowest layer and cannot depend on any other layer.
   * **Contents:** UI Kit components (`Button`, `Input`, `Modal`), utility functions (`lib`), base API configuration (`api`), shared TypeScript types (`types`), global configuration (`config`), assets (`assets`).
   * **Location:** `src/shared/`

## Dependency Rule

A key rule in FSD is that code within a layer can only import from layers *below* it.

```
app -> pages -> widgets -> features -> entities -> shared
```

This prevents circular dependencies and ensures a clear flow of control.

## Segment Structure Within a Slice

Each slice (e.g., `auth` feature, `product` entity) typically contains standardized segments:

1. **`ui/`**: React components specific to this slice.
2. **`model/`**: State management (Redux slices, reducers, selectors) and business logic.
3. **`api/`**: API interaction (RTK Query endpoints).
4. **`lib/`**: Helper functions and utilities specific to this slice.
5. **`types.ts`**: TypeScript types/interfaces for this slice (sometimes within `model/`).
6. **`index.ts`**: Public API exports (what other parts of the application can import).

## Example Structure

```
src/
├── features/
│   ├── auth/
│   │   ├── ui/
│   │   │   ├── LoginForm.tsx
│   │   │   ├── RegistrationForm.tsx
│   │   │   └── ...
│   │   ├── model/
│   │   │   ├── types.ts
│   │   │   └── ...
│   │   ├── api/
│   │   │   ├── authApi.ts
│   │   │   └── ...
│   │   ├── lib/
│   │   │   └── ...
│   │   ├── index.ts
│   │   └── README.md
│   └── ...
└── ...
```

## Benefits of FSD

* **Clear Boundaries:** Each layer and slice has well-defined responsibilities and dependencies.
* **Modularity:** Features and entities can be developed, tested, and maintained independently.
* **Scalability:** The architecture readily accommodates growth in both team size and codebase.
* **Discoverability:** New developers can easily understand where specific code should be located.
* **Consistency:** Standard patterns and locations reduce cognitive load and decision fatigue.

## Migration Status

This project is transitioning to FSD. You will find legacy code in directories like `src/components`, `src/helpers`, etc. The goal is to progressively refactor this code into the appropriate FSD layers. Refer to the [Refactoring Plan](../refactoring/plan.md) for more details.

When adding new functionality, please adhere to the FSD structure. When modifying existing code, consider refactoring it to follow FSD principles if feasible within the scope of your task.
