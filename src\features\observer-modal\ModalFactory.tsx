import { ModalFilterData, ObserverItem } from '@/shared/types/observer';
import { ModalAllFilter } from './ModalAllFilter';
import { ModalCategoryFilter } from './ModalCategoryFilter';
import { ModalExploreAllFilter } from './ModalExploreAllFilters';
import { ModalItem } from './ModalItem';
import { ModalSearchAllFilter } from './ModalSearchAllFilter';
interface ModalFactoryProps {
  data: ObserverItem<ModalFilterData>[];
}

export const ModalFactory = ({ data }: ModalFactoryProps) => {
  return data.map((item) => {
    switch (item.type) {
      case 'item': {
        return <ModalItem data={item} key={item.observerId} />;
      }
      case 'all': {
        return <ModalAllFilter data={item} key={item.observerId} />;
      }
      case 'category': {
        return <ModalCategoryFilter data={item} key={item.observerId} />;
      }
      case 'search': {
        return <ModalSearchAllFilter data={item} key={item.observerId} />;
      }
      case 'explore': {
        return <ModalExploreAllFilter data={item} key={item.observerId} />;
      }
    }
  });
};
