import type { ServerCartState } from '../';
import type { AdditionalServerResponseData } from '@/shared/types/AdditionalServerResponseData';
import type { PriceType } from '@/entities/price/types';

export interface GetUserCartResponse
  extends AdditionalServerResponseData,
    ServerCartState {}

export interface SendCartPayload {
  id: number;
  quantity: number;
  selected: boolean;
}

export type SendCartRequest = SendCartPayload | { items: SendCartPayload[] };

export interface SendCartResponse extends AdditionalServerResponseData {
  data: {
    availability: {
      stock: number;
      preorder: {
        unix: number;
        formatted: string;
        date: string;
        iso8601: string;
        rfc3339: string;
        rfc2822: string;
        atom: string;
        cookie: string;
      } | null;
      supplier: number;
    };
    item_id: number;
    price: PriceType;
  };
  total_amount: number;
  total_quantity: number;
}
export interface GetCartItemPriceResponse extends AdditionalServerResponseData {
  data: null | {
    item_id: number;
    price: PriceType;
  };
  total_amount: number;
  total_quantity: number;
}

export interface GetCartItemPriceRequest {
  item_id: number;
  quantity: number;
}

export interface GetCartShareLinkResponse {
  data: string;
}
