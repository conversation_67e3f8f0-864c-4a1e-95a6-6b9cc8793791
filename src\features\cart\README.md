# Feature: Cart

## Purpose

This feature encompasses all functionality related to the user's shopping cart, including adding items, removing items, changing quantities, managing item selection, and calculating totals.

## Key Functionality

*   **Adding Items:** Provides logic (`useAddToCart`) and UI (`AddToCartButton`) to add products to the cart. Handles both anonymous and logged-in users.
*   **Removing Items:** Provides logic (`useRemoveFromCart`) and UI (`RemoveFromCartButton`) to remove items. Includes optional confirmation.
*   **Quantity Management:** Provides logic (`useQuantityControl`) and UI (`QuantityControl`) to increase/decrease item quantities. Handles minimum quantity rules and potential item removal if quantity reaches zero.
*   **Item Selection:** Manages which items in the cart are selected for checkout (`useCartSelection`, `SelectCartItemButton`, select all checkbox).
*   **State Management:**
    *   Maintains the cart state (items, totals, selected totals) in Redux (`model/cartSlice.ts`).
    *   Uses `sessionStorage` for persistence for anonymous users.
    *   Synchronizes with the server via RTK Query for logged-in users.
*   **Data Transformation:** Includes logic (`lib/transformData.ts`) to convert server cart data structure to the local state structure.
*   **Cart Display:** Provides UI components (`ui/Cart.tsx`, `ui/components/`) to display cart contents, order summaries, and related actions.
*   **Cart Sharing:** Integrates with the `cart-share` feature to allow users to share their selected cart items.

## Components (`ui/`)

*   **Main Cart View:** `Cart.tsx` (likely used on the `/shopping-cart` page).
*   **Cart Item Displays:** `CartItem.tsx`, `CartItemLine.tsx`, `MobileCartItem.tsx`.
*   **Controls:** `AddToCartButton.tsx`, `QuantityControl.tsx`, `RemoveFromCartButton.tsx`, `SelectCartItemButton.tsx`.
*   **Summaries/Info:** `CartOrderInfo.tsx`, `MobileToCheckoutBar.tsx`.
*   **Details View:** `CartDetail.tsx`.

## State & Logic (`model/`)

*   **`cartSlice.ts`:** Redux slice managing the `LocalCartState` (items, totals, selection). Includes reducers for adding, removing, updating quantity, selecting/deselecting items, and setting the entire cart state. Handles persistence to `sessionStorage` for anonymous users.
*   **Hooks:**
    *   `useCart.ts`: Provides the current cart state (local or server-derived).
    *   `useCartState.ts`: Provides a comprehensive set of actions (add, remove, update, select) that handle both local state and server sync. *(Consider consolidating logic into this hook where appropriate)*.
    *   `useAddToCart.ts`: Logic specifically for the "Add to Cart" action.
    *   `useRemoveFromCart.ts`: Logic for removing items.
    *   `useQuantityControl.ts`: Logic for +/- quantity buttons.
    *   `useCartSelection.ts`: Logic for managing item selection state and actions.
*   **Types (`model/types.ts`):** Defines `LocalCartState`, `ServerCartState`, `CartProduct`, etc.

## API Integration (`api/`)

*   **`cartApi.ts`:** Defines RTK Query endpoints:
    *   `getUserCart`: Fetches the user's cart from the server.
    *   `sendCart`: Sends updates (add, remove, quantity change, selection change) to the server cart. Can handle single items or batches.
    *   `removeFromCart`: (Potentially redundant if `sendCart` with quantity 0 is used) Specific endpoint for removal.
*   **Interaction with `priceApi`:** Uses `useGetCartItemPriceMutation` (`src/entities/price/api/priceApi.ts`) to fetch updated prices when quantities change.

## Usage

*   Cart controls (`AddToCartButton`, etc.) are used on product cards (`widgets/product-card`) and product pages (`pages/ProductPage`).
*   The main cart display (`Cart.tsx`) is rendered on the `/shopping-cart` page.
*   Cart state (item count) is displayed in the header (`widgets/header`).