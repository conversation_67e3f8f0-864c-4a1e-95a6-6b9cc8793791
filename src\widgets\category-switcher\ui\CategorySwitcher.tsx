import { memo } from 'react';

import clsx from 'clsx';

import type { ProductListCategoryChain } from '@/entities/category';
import categoryIcon from '@/shared/assets/icons/category.svg';

interface CategorySwitcherProps {
  categories: ProductListCategoryChain[];
  selectedCategory: string | number;
  onCategoryChange: (categoryId: string | number) => void;
  allItemsCount?: number; // Added to display count on "All" button
}

export const CategorySwitcher = memo(
  ({
    categories = [],
    selectedCategory,
    onCategoryChange,
    allItemsCount, // Destructure allItemsCount
  }: CategorySwitcherProps) => {
    const safeCategories = Array.isArray(categories) ? categories : [];

    if (!safeCategories.length) {
      return null;
    }

    return (
        <div className="flex gap-4">
        <button
          type="button"
          className={clsx(
            'shadow-[0_1px_2px_0_rgba(0,0,0,.1)] p-1 lg:p-2 rounded-md',
            'flex justify-center items-center outline-none min-w-[120px]',
            'transition-colors duration-200',
            selectedCategory === '' || selectedCategory === 0
              ? 'bg-colLightGray text-colBlack hover:bg-colGray'
              : 'bg-white text-colBlack hover:bg-gray-100',
          )}
          onClick={() => onCategoryChange('')}
        >
          <img className="w-4 mr-1" src={categoryIcon} alt="All categories" />
          <span className="truncate text-sm">Все</span>
          {allItemsCount && allItemsCount > 0 && (
            <span className="absolute -top-2 -right-2 z-10 bg-colGreen h-5 pb-[2px] min-w-[20px] flex justify-center items-center text-xs text-white rounded-full px-1">
              {allItemsCount > 99 ? '99+' : allItemsCount}
            </span>
          )}
        </button>

        {safeCategories.map((el) => {
          if (!el.chain || !Array.isArray(el.chain) || !el.chain.length) {
            return null;
          }

          if (el.count === 0) {
            return null;
          }

          const lastCategory = el.chain[el.chain.length - 1];
          if (!lastCategory) {
            return null;
          }

          return (
            <button
              key={lastCategory.id}
              type="button"
              className={clsx(
                'relative shadow-[0_1px_2px_0_rgba(0,0,0,.1)] p-1 lg:p-2 rounded-md',
                'flex justify-center items-center outline-none min-w-[120px]',
                'transition-colors duration-200',
                String(selectedCategory) === String(lastCategory.id)
                  ? 'bg-colLightGray text-colBlack hover:bg-colGray'
                  : 'bg-white text-colBlack hover:bg-gray-100',
              )}
              onClick={() => onCategoryChange(lastCategory.id)}
            >
              {lastCategory.image && (
                <img
                  className="w-4 h-4 mr-1"
                  src={
                    typeof lastCategory.image === 'object' &&
                    lastCategory.image !== null
                      ? lastCategory.image.medium || lastCategory.image.small
                      : categoryIcon
                  }
                  alt={lastCategory.name}
                />
              )}
              <span className="truncate text-sm">
                {lastCategory.name || 'Категория'}
              </span>
              <span className="absolute -top-2 -right-2 z-10 bg-colGreen h-5 pb-[2px] min-w-[20px] flex justify-center items-center text-xs text-white rounded-full px-1 -z-10">
                {el.count > 99 ? '99+' : el.count}
              </span>
            </button>
          );
        })}
      </div>
    );
  },
);

CategorySwitcher.displayName = 'CategorySwitcher';
