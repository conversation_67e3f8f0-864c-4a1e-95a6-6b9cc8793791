import React, { useState } from 'react';

import { ProductTags } from '@/entities/product';
import { ComparisonButton } from '@/features/comparison';
import { FavoriteButton } from '@/features/favorite';
import noImg from '@/shared/assets/images/no-image.png';

import type { Product } from '@/entities/product';

interface PreviewGalleryProps {
  product: Product;
  imageHeight?: string; // Optional height, defaults to current values
  imageWidth?: string; // Optional width, defaults to full width
  showButtons?: boolean; // Optional flag to show/hide buttons, defaults to true
  showTags?: boolean;
  className?: string;
}

export const PreviewGallery = ({
  product,
  showButtons = true,
  showTags = true,
  className = '',
}: PreviewGalleryProps) => {
  const [hoveredIndex, setHoveredIndex] = useState(0);

  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);

  const minSwipeDistance = 50; // Adjust this value as needed

  const onTouchStart = (e) => {
    setTouchEnd(null); // otherwise the swipe is fired even with usual touch events
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e) => setTouchEnd(e.targetTouches[0].clientX);

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe && hoveredIndex < product?.files?.length - 1) {
      setHoveredIndex(hoveredIndex + 1);
    } else if (isRightSwipe && hoveredIndex > 0) {
      setHoveredIndex(hoveredIndex - 1);
    }
  };

  const handleMouseMove = (e) => {
    if (product?.files?.length > 1) {
      const { width, left } = e.currentTarget.getBoundingClientRect();
      const hoverPosition = e.clientX - left;
      const newIndex = Math.floor(
        (hoverPosition / width) * product?.files?.length
      );
      if (newIndex >= 0 && newIndex < product?.files?.length) {
        setHoveredIndex(newIndex);
      }
    }
  };

  const displayedImage =
    product?.files?.length > 0 ? product?.files[hoveredIndex]?.medium : noImg;
  const objectFitClass = product?.files[hoveredIndex]?.stretch_image
    ? 'object-cover'
    : 'object-contain';

  return (
    <div
      className={`group rounded-md mm:rounded-xl overflow-hidden relative bg-white flex justify-center items-center ${className}`}
      onTouchStart={onTouchStart}
      onTouchMove={onTouchMove}
      onTouchEnd={onTouchEnd}
    >
      <div className="absolute w-full h-full">
        <div className="relative z-10 left-1/2 -translate-x-1/2 top-2 flex gap-1 w-fit">
          {product?.files?.length > 1
            ? product?.files?.map((_, index) => (
                <span
                  key={index}
                  className={`h-0.5 w-4 rounded-full transition-colors duration-300 ${
                    index === hoveredIndex ? 'bg-colDarkGray' : 'bg-colLightGray'
                  }`}
                ></span>
              ))
            : null}
          {product?.files?.length <= 1 || !product?.files ? (
            <span className="h-2"></span>
          ) : null}
        </div>
        {showButtons ? (
          <div className='flex flex-col absolute top-2 right-2 gap-2'>
          <FavoriteButton
            product={product}
            className="icon-btn"
          />
          <ComparisonButton
            product={product}
            className="icon-btn"
          />
          </div>
        ) : null}

        

          {/* {showButtons ? (
            <FavoriteButton
              product={product}
              className="z-10 cursor-pointer w-8 h-8 rounded-md bg-colSuperLight flex items-center justify-center transition-all duration-200 hover:scale-110 absolute top-2 right-2"
            />
          ) : null}
        {showButtons ? (
          <ComparisonButton
            product={product}
            className="z-10 cursor-pointer w-8 h-8 rounded-md bg-colSuperLight flex items-center justify-center transition-all duration-200 hover:scale-110 absolute bottom-2 right-2"
          />
        ) : null} */}
      </div>

      <div className="absolute bg-white  bg-opacity-30 backdrop-blur-lg rounded-lg overflow-hidden h-full w-full">
        <img
          onMouseMove={handleMouseMove}
          onMouseLeave={() => setHoveredIndex(0)}
          src={displayedImage}
          className={`w-full h-full ${objectFitClass}`}
        />
      </div>
      {/* <div className=" flex flex-col items-center">
        <img
          src={displayedImage}
          className={`w-full h-full ${objectFitClass}`}
        />
      </div> */}
    </div>
  );
};
