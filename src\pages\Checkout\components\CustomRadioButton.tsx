import React from 'react';

interface CustomRadioButtonProps {
  id: string;
  name: string;
  value: string;
  checked: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  label: string;
  description?: string;
  icon?: React.ReactNode;
}

export const CustomRadioButton: React.FC<CustomRadioButtonProps> = ({
  id,
  name,
  value,
  checked,
  onChange,
  label,
  description,
  icon,
  method
}) => {
  return (
    <label
      className={`
        relative flex items-center p-10 rounded-lg transition-colors border-2
        ${checked ? 'border-[#15765B] bg-[#F9FEFC]' : 'border-[#E6E9E8] hover:bg-[#F9FBFA]'}
      `}
    >
      <input
        type="radio"
        id={id}
        value={value}
        checked={checked}
        onChange={onChange}
        className="absolute opacity-0 w-0 h-0"
        aria-labelledby={`${id}-label`}
        aria-describedby={description ? `${id}-description` : undefined}
      />
            
        <div className=" text-[#414947]">
         <img src={method.icon}/>
        </div>
      
      <div className="ml-3 text-sm">
        <label
          htmlFor={id}
          id={`${id}-label`}
          className="font-medium text-xl text-colGreen"
        >
          {method.name}
        </label>
        
        {method.description && (
          <p
            id={`${id}-description`}
            className=""
          >
            {method.description}
          </p>
        )}
      </div>
    </label>
  );
};
