import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { PopularCategoriesMobile } from '@/components/Home/PopularCategoriesMobile';
import { SaleProducts } from '@/components/Home/SaleProducts';
import { useModal } from '@/features/modals/model/context';
import { useGetMainPageDataQuery } from '@/redux/api/contentEndpoints';
import { scrollToTop } from '@/shared/lib/scrollToTop';
import { Advantages } from '@components/Home/Advantages';
import { Banner } from '@components/Home/Banner';
import { Brands } from '@components/Home/Brands';
import { News } from '@components/Home/News';
import { PopularCategories } from '@components/Home/PopularCategories';
import { Promotions } from '@components/Home/Promotions';
import { RoomProducts } from '@components/Home/RoomProducts';
import { SaleBanner } from '@components/Home/SaleBanner';

import HomeSkeleton from './HomeSkeleton';

const Home = () => {
  const { data, isLoading, isSuccess } = useGetMainPageDataQuery();
  const location = useLocation();
  const navigate = useNavigate();
  const { showModal } = useModal();

  // This effect handles the redirect from ProtectedRoute
  useEffect(() => {
    // Check if we were redirected from a protected route
    const from = location.state?.from?.pathname;
    if (from) {
      console.log(`[Home] Redirected from protected route: ${from}. Showing auth modal.`);
      // Show the authentication modal
      showModal({
        type: 'auth',
        content: 'checkAuth',
        from: location.state.from,
      });
      // Clear the location state to prevent the modal from re-opening on refresh
      navigate(location.pathname, { replace: true });
    }
  }, [location, navigate, showModal]);

  const componentMap = {
    slider: Banner,
    newProducts: News,
    staticBaner: SaleBanner,
    // suggestions: Suggestions,
    popularCategories: PopularCategories,
    promo: Promotions,
    withDiscount: SaleProducts,
    roomCategries: RoomProducts,
    // furnitureFittings: FurnitureFittings,
    brands: Brands,
    cooperate: Advantages,
  };

  useEffect(() => {
    scrollToTop();
  }, []);

  return (
    <>
      {isSuccess ? (
        <>
          <PopularCategoriesMobile />
          {Object.keys(data).map((key) => {
            const Component = componentMap[key];
            if (Component) {
              return (
                <div key={key} className="content">
                  <Component />
                </div>
              );
            }
            return null;
          })}
        </>
      ) : null}
      {isLoading ? <HomeSkeleton /> : null}
    </>
  );
};

export default Home;
