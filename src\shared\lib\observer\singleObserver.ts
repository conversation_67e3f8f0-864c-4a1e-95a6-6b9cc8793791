export class SingleObserver<T> {
  private value: T;
  private subscribers: Array<(data: T) => void> = [];

  subscribe(observer: (data: T) => void): () => void {
    this.subscribers.push(observer);
    observer(this.value);

    return () => {
      const index = this.subscribers.indexOf(observer);
      if (index !== -1) this.subscribers.splice(index, 1);
    };
  }

  setValue(newValue: T) {
    this.value = newValue;
    this.notify();
  }

  private notify() {
    this.subscribers.forEach((subscriber) => subscriber(this.value));
  }
}
