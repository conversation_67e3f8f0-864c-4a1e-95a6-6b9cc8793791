import React, { useEffect, useRef, useState } from 'react';

import { useLocation } from 'react-router-dom';
import { useIntersection } from 'react-use';
import { toast } from 'sonner';
import {
  CartDetail,
  CartOrderInfo,
  MobileToCheckoutBar,
} from '@/features/cart';
import { useCart } from '@/features/cart';
import { useCartSelection } from '@/features/cart/model/hooks/useCartSelection';
import { useModal } from '@/features/modals/model/context';
import { RecentItemsSection } from '@/features/recent-items';
import ErrorEmpty from '@/shared/ui/ErrorEmpty';
import docIcon from '@/shared/assets/icons/download-pdf.svg';
import shareIcon from '@/shared/assets/icons/share.svg';
import { scrollToTop } from '@/shared/lib/scrollToTop';
import { CSearchField } from '@/shared/ui/inputs/CSearchField';
import { Breadcrumbs } from '@/widgets/breadcrumbs';

import type { CartProduct } from '@/features/cart';
import { Icon } from '@iconify-icon/react';

export const Cart = (): JSX.Element => {
  // Use the cart hook and refresh cart data when the page loads
  const { cart, isLoading, refreshCart } = useCart();
  const { selectedItems } = useCartSelection();

  // Refresh cart data when the component mounts
  useEffect(() => {
    refreshCart();
  }, [refreshCart]);

  useEffect(() => {
    scrollToTop();
  }, []);

  const [filteredCart, setFilteredCart] = useState<CartProduct[]>([]);

  const handleFilter = (event) => {
    const filterValue = event.target.value;

    const filteredCart = cart?.cart?.filter(
      (product) =>
        product.name.toLowerCase().includes(filterValue.toLowerCase()) ||
        product.groupName.toLowerCase().includes(filterValue.toLowerCase()) ||
        product.sku.toString().includes(filterValue)
    );

    setFilteredCart(filteredCart);
  };

  useEffect(() => {
    setFilteredCart(cart?.cart);
    // Log cart state when it changes
    console.log('[Cart] Updated cart state:', {
      totalItems: cart?.cart?.length,
      selectedItems: selectedItems?.length,
      selectedItemIds: selectedItems?.map(item => item.id)
    });
  }, [cart, selectedItems]);

  const orderInfo = useRef(null);
  const orderInfoVisible = useIntersection(orderInfo, {
    root: null,
    rootMargin: '0px',
    threshold: 1,
  });
  const { showModal } = useModal();

  const location = useLocation();

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const cartCode = params.get('cart');

    if (cartCode) {
      showModal({ type: 'showSharedCart', code: cartCode });
    }
  }, [location.search]);

  return (
    <div className="content pb-6 lining-nums proportional-nums">
      <Breadcrumbs />
      <h1 className="block text-2xl md:text-[40px] font-semibold text-colBlack">
        Корзина
      </h1>
      {cart?.cart?.length > 0 ? (
        <>
          <div className="hidden lg:flex justify-between items-end">
            <div className="flex max-w-[460px] w-full pt-3">
              <CSearchField
                label="Введите наименование или код товара"
                name="search"
                type="search"
                handleChange={handleFilter}
              />
            </div>
            <div className="flex justify-end items-center space-x-4 ">
              
              <button
                        
                        onClick={() => {
                          if (selectedItems.length === 0) {
                            toast('Выберите товары, которыми хотите поделиться');
                            return;
                          }
                          showModal({ type: 'shareCart', showLink: true });
                        }}
                        className='flex items-center gap-1'
                      >
                        <div className="icon-btn icon-btn-gray-bg">
                        <Icon icon="solar:share-bold" width={24} height={24} className="icon-btn-icon"/>
                        </div>
                        <span className="">
                          Поделиться
                        </span>
                      </button>
                        {/* <button
                        className='flex items-center gap-1'
                      >
                        <div className="icon-btn icon-btn-gray-bg">
                        <Icon icon="solar:document-bold" width={24} height={24} className="icon-btn-icon"/>
                        </div>
                        <span className="">
                        Скачать PDF заказа
                        </span>
                      </button> */}
            </div>
          </div>
          <div className="flex flex-wrap gap-10 py-5">
            <div className="lg:basis-[calc(70%-20px)] basis-full">
              <CartDetail
                cart={cart}
                isLoading={isLoading}
                filteredCart={filteredCart}
                selected={selectedItems}
              />
            </div>

            <div
              ref={orderInfo}
              className="lg:basis-[calc(30%-20px)] basis-full"
            >
              <CartOrderInfo cart={cart} selected={selectedItems} />
            </div>
          </div>

          {orderInfoVisible && orderInfoVisible.intersectionRatio < 1 ? (
            <MobileToCheckoutBar cart={cart} selected={selectedItems} />
          ) : null}
        </>
      ) : null}
      {cart?.cart?.length === 0 ? (
        <ErrorEmpty
          title="Корзина пуста!"
          desc="Воспользуйтесь поиском, чтобы найти всё, что нужно."
          height="230px"
          hideBtn={true}
        />
      ) : null}
      <RecentItemsSection />
    </div>
  );
};
