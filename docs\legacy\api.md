# Legacy Documentation: `/src/api/` (Root Level)

This directory contains legacy API interaction logic that bypasses the standard RTK Query setup.

## Modules & Refactoring Targets

1.  **`axios.js`**
    *   **Purpose:** Defines a pre-configured Axios instance (`request`) with the base URL and potentially default headers (including Authorization). It's noted as being "Use only for get requests".
    *   **Target FSD:** This setup is largely superseded by RTK Query's `fetchBaseQuery` defined in `src/shared/api/api.ts`.
    *   **Refactoring:**
        *   Identify where `request` from `axios.js` is still being used.
        *   Replace those usages with corresponding RTK Query hooks (`useSomeQuery`).
        *   Ensure the `prepareHeaders` function in `src/shared/api/api.ts` correctly handles Authorization tokens for RTK Query requests.
        *   Remove `axios.js` once all usages are migrated.

2.  **`searchProducts.js`**
    *   **Purpose:** Contains functions (`fetchSearchResults`, `fetchSearchFilters`) for making direct API calls related to product search and retrieving search filters, likely using the legacy `request` from `axios.js`. It includes logic for constructing query parameters based on filter values.
    *   **Target FSD:** This functionality should be handled by RTK Query endpoints.
    *   **Refactoring:**
        *   The logic maps directly to the `searchProducts` and `getSearchFilters` mutations already defined (though perhaps needing refinement) in `src/features/search/api/searchApi.ts`.
        *   Replace calls to `fetchSearchResults` and `fetchSearchFilters` with triggers for the corresponding RTK Query mutations.
        *   The parameter construction logic should happen in the component/hook *before* triggering the mutation, passing the structured request object defined in the API types.
        *   Remove `searchProducts.js` once migration is complete.