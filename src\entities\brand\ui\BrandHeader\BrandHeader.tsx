import { memo } from 'react';
// Remove direct API query import
// import { useGetBrandQuery } from '../../api/brandApi';

// Use a static implementation that doesn't make API calls
export interface BrandHeaderProps {
  brandId: string;
  className?: string;
}

export const BrandHeader = memo(({ brandId, className }: BrandHeaderProps) => {
  // Instead of making API calls, we'll display a simpler header
  // The actual brand data will come from the filters response
  
  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      <div className="flex items-center">
        <div>
          <h1 className="text-2xl font-bold text-colBlack capitalize">
            {brandId.replace(/-/g, ' ')}
          </h1>
          <div className="mt-2 text-gray-600">
            Products from this brand
          </div>
        </div>
      </div>
    </div>
  );
});

BrandHeader.displayName = 'BrandHeader';
