import { FilterCheckbox, Forbidden } from '@/shared/ui';
import { GroupFilterColor } from './model/context';

interface GroupBodyColorProps {
  data: GroupFilterColor;
}

export const GroupBodyColor: React.FC<GroupBodyColorProps> = ({ data }) => {
  return (
    <div className='flex flex-col gap-[8px] mt-2'>
      {data.values.map((item) => (
        <FilterCheckbox {...item}>
          {!item.color && !item.second_color ? (
            <Forbidden size={19} color='#B5B5B5' />
          ) : (
            <span className='size-[18px] flex rounded-full overflow-hidden'>
              <span className={`flex  w-full h-full`} style={{ background: item.color }} />
              {item.second_color && (
                <span className={` flex w-full h-full`} style={{ background: item.second_color }} />
              )}
            </span>
          )}
          {item.text}
        </FilterCheckbox>
      ))}
    </div>
  );
};
