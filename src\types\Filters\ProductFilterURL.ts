import { ProductFilterInputType } from './ProductFilter';

export interface ProductFilterURLBase {
  type: ProductFilterInputType;
  parentId: number | string;
  title: string;
  value: number | string | boolean | [number, number];
}

export interface ProductFilterURLMultiple extends ProductFilterURLBase {
  type: 'multiple';
  text: string;
  id: number | string;
}

export interface ProductFilterURLRange extends ProductFilterURLBase {
  type: 'range';
  value: [number, number] | null;
}
export interface ProductFilterURLToggle extends ProductFilterURLBase {
  type: 'range';
  value: boolean;
}

type ProductFilterURLMap = {
  multiple: ProductFilterURLMultiple;
  range: ProductFilterURLRange;
  toggle: ProductFilterURLToggle;
};

export type ProductFilterURL = ProductFilterURLMap[keyof ProductFilterURLMap];
