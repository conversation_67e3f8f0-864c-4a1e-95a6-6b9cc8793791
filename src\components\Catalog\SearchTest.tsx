import { Breadcrumbs } from '@/widgets/breadcrumbs';
import StickyBox from 'react-sticky-box';
import { CatalogSidebarFilters } from './CatalogSidebarFilters';

import { SadBasket } from '@/shared/ui';
import {
  ProductCard,
  ProductCardLine,
  ProductCardLineSkeleton,
  ProductCardSkeleton
} from '@/widgets/product-card';
import { ChevronUp } from 'lucide-react';
import { NavLink } from 'react-router-dom';
import CardTypeControls from './CatalogContent/CardTypeControls';
import { CatalogSort } from './CatalogSort';
import { CategoryChain, useSearchTest } from './hooks/useSearchTest';

export const SearchTest = () => {
  const {
    filters,
    filtersLoading,
    onChangeFilter,
    onResetFilters,
    handleOpenModalAllFilters,
    isLoadingProducts,
    handleSortChange,
    currentSort,
    sortBy,
    cardType,
    setTypeCard,
    products,
    categories,
    onCategory,
    prevQ,
    hasMore,
    loadMoreRef,
    sentinelRef,
    isVisible,
    scrollToTop
  } = useSearchTest();

  return (
    <div className='content lining-nums proportional-nums p-4 relative'>
      {!products.length && !isLoadingProducts ? (
        <div className='content flex flex-col items-center gap-10 '>
          <SadBasket />
          <div className='font-semibold text-4xl'>К сожалению, таких товаров не обнаружено :(</div>
          <div className='text-xl'>Давайте поищем что-то другое или вернёмся на главную</div>
          <div className='flex gap-10 mb-10'>
            <NavLink to='/'>
              <button className='py-3 px-8 bg-colGreen rounded cursor-pointer text-white font-semibold'>
                На главную
              </button>
            </NavLink>
            <NavLink to='/catalog'>
              <button className='py-3 px-8 border border-colGreen rounded cursor-pointer text-colGreen font-semibold'>
                В каталог
              </button>
            </NavLink>
          </div>
        </div>
      ) : (
        <>
          <div ref={sentinelRef} className='h-px absolute top-0' />
          {isVisible && (
            <button
              onClick={scrollToTop}
              className='flex jusctify-center items-center p-[8px] fixed bottom-5 right-5 bg-colGreen text-white rounded-[8px] text-xl flex items-center justify-center cursor-pointer transition-all duration-300 opacity-70 hover:opacity-100 hover:scale-110 z-50'
              title='Наверх'
            >
              <ChevronUp />
            </button>
          )}

          <Breadcrumbs />
          <div className='flex items-end rounded-lg gap-[10px] mb-6 mt-2'>
            <h3 className='font-semibold text-3xl mm:text-4xl text-[#222222]'>{prevQ.current}</h3>
            <span className='text-[16px] text-[#222222]'>найдено 8 товаров</span>
          </div>
          {!!categories?.length && (
            <div className='flex flex-wrap gap-[16px] p-[4px] mb-4 overflow-auto scrollable2'>
              {categories?.map((cat, index) => {
                if ('chain' in cat) {
                  const chain = cat?.chain[cat?.chain?.length - 1];
                  const active = 'is_selected' in cat && cat.is_selected;
                  return (
                    <button
                      key={`chain-${index}`}
                      className={`border border-[1px] shrink-[0]  rounded-[8px] p-[8px] flex items-center gap-[10px] cursor-pointer ${active ? 'bg-colGreen text-[white] border-colGreen' : 'border-[#F5F5F5] hover:border-colGreen'}`}
                      onClick={() => (active ? onCategory(null) : onCategory(chain.slug))}
                    >
                      {!!chain.image && (
                        <span className='w-[32px] h-[32px] justify-center items-center '>
                          <img src={chain.image[0].tiny} alt='' className='object-cover' />
                        </span>
                      )}
                      <span className='leading-[32px]'>
                        {chain.name} ({cat.count})
                      </span>
                    </button>
                  );
                } else {
                  const chain: CategoryChain = cat;
                  return (
                    <button
                      key={`chain-${index}`}
                      className='border border-[1px] shrink-[0] border-[#F5F5F5] hover:border-colGreen bg-colGreen text-[white] rounded-[8px] p-[8px] flex items-center gap-[10px] cursor-pointer'
                      onClick={() => onCategory(null)}
                    >
                      {!!chain.image && (
                        <span className='w-[32px] h-[32px] justify-center items-center '>
                          <img src={chain.image[0].tiny} alt='' className='object-cover' />
                        </span>
                      )}
                      <span className='leading-[32px]'>{chain.name}</span>
                    </button>
                  );
                }
              })}
            </div>
          )}
          <div className='flex gap-[20px] items-start'>
            <StickyBox offsetTop={90} offsetBottom={20}>
              <CatalogSidebarFilters
                filters={filters}
                onChangeFilter={onChangeFilter}
                filtersLoading={filtersLoading}
                onResetFilters={onResetFilters}
                handleOpenModalAllFilters={handleOpenModalAllFilters}
              />
            </StickyBox>

            <div className='flex flex-col gap-[24px] w-full'>
              <div className='flex flex-col gap-[20px]'>
                <div className='flex justify-between'>
                  <CatalogSort
                    currentSort={currentSort}
                    handleSortChange={handleSortChange}
                    sortBy={sortBy}
                  />
                  <CardTypeControls cardType={cardType} setTypeCard={setTypeCard} />
                </div>
              </div>

              {cardType === 'tile' ? (
                <div className='grid grid-cols-2 mm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 ll:grid-cols-3 gap-5 xl:grid-cols-4'>
                  {!!products.length
                    ? products.map((el, index) => (
                        <ProductCard key={`index-${index}-id-${el?.id}`} product={el} />
                      ))
                    : null}

                  {isLoadingProducts
                    ? Array.from({ length: 20 }).map((_, index) => (
                        <ProductCardSkeleton key={index} />
                      ))
                    : null}
                </div>
              ) : null}
              {cardType === 'line' ? (
                <div className='space-y-4'>
                  {isLoadingProducts
                    ? Array.from({ length: 20 }).map((_, index) => (
                        <ProductCardLineSkeleton key={index} />
                      ))
                    : null}
                  {!!products.length &&
                    products.map((el, index) => (
                      <ProductCardLine key={`index-${index}-id-${el?.id}`} product={el} />
                    ))}
                </div>
              ) : null}
              {isLoadingProducts && <div className='text-center'>Идет загрузка...</div>}
              {hasMore && <div ref={loadMoreRef} style={{ height: '20px' }} />}
              {!hasMore && <div>Больше нет товаров</div>}
            </div>
          </div>
        </>
      )}
    </div>
  );
};
