import React, { useState } from 'react';

import plural from 'plural-ru';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

import { useCreateOrderMutation } from '@/entities/order';
import type { CartProduct, LocalCartState } from '@/features/cart';

export type MobileToCheckoutBarProps = {
  cart: LocalCartState;
  selected: CartProduct[];
};

export const MobileToCheckoutBar = ({
  cart,
  selected,
}: MobileToCheckoutBarProps) => {
  const navigate = useNavigate();
  const [createOrder, { isLoading }] = useCreateOrderMutation();
  const [isCheckoutLoading, setIsCheckoutLoading] = useState(false);

  const hasZeroPriceItems =
    selected.filter((item) => item.price.base === null || item.price.base === 0)
      .length > 0;

  const handleCheckout = async () => {
    if (selected?.length === 0 || hasZeroPriceItems) {
      return;
    }

    try {
      setIsCheckoutLoading(true);
      
      // Format selected items into the expected request format
      const orderItems = selected.map((item) => ({
        id: item.id,
        quantity: parseFloat(item.quantity || '1')
      }));

      // Call the API to get checkout data
      const response = await createOrder({ order: orderItems }).unwrap();
      
      if (response && response.data) {
        // Store the checkout data in localStorage for the checkout page to use
        localStorage.setItem('checkout_order_data', JSON.stringify(response));
        
        // Navigate to checkout page
        navigate('/checkout');
      } else {
        toast.error('Ошибка при получении данных заказа');
      }
    } catch (error) {
      console.error('[MobileToCheckoutBar] Error getting checkout data:', error);
      toast.error('Произошла ошибка при подготовке заказа');
    } finally {
      setIsCheckoutLoading(false);
    }
  };

  return (
    <div className="lg:hidden z-10 fixed bottom-[72px] left-0 w-[100vw] bg-white">
      <div className="flex justify-between px-5 py-3">
        <div className="flex flex-col gap-2 justify-between w-full">
          <div className="flex justify-between items-center">
            {selected?.length === 0 ? (
              <div className="text-center text-[#828282] text-lg font-medium">
                Выберите товары, которые хотите заказать
              </div>
            ) : (
              <>
                <span className="text-xl font-semibold text-colBlack">
                  {cart?.selected?.price_before_discount} {cart.currency.symbol}
                </span>
                <span className="text-xl font-semibold text-colBlack">
                  {cart?.selected?.items_count}{' '}
                  {plural(
                    cart?.selected?.items_count,
                    'товар',
                    'товара',
                    'товаров'
                  )}
                </span>
              </>
            )}
          </div>
          {selected?.length === 0 ? (
            <button className="text-white cursor-auto font-semibold bg-colGray rounded w-full h-[50px] flex justify-center items-center">
              Перейти к оформлению
            </button>
          ) : null}
          {selected?.length !== 0 && hasZeroPriceItems ? (
            <>
              <button className="text-white cursor-not-allowed font-semibold bg-colGray rounded w-full h-[50px] flex justify-center items-center">
                Перейти к оформлению
              </button>
              <div className="text-sm mt-2 text-center">
                Нельзя оформить заказ на товары с не указанной ценой
              </div>
            </>
          ) : null}
          {selected?.length !== 0 && !hasZeroPriceItems ? (
            <button
              type="button"
              onClick={handleCheckout}
              disabled={isCheckoutLoading}
              className="text-white font-semibold bg-colGreen rounded w-full h-[50px] flex justify-center items-center"
            >
              {isCheckoutLoading ? (
                <span className="flex items-center justify-center gap-2">
                  <span className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
                  Подготовка...
                </span>
              ) : (
                'Перейти к оформлению'
              )}
            </button>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default MobileToCheckoutBar;