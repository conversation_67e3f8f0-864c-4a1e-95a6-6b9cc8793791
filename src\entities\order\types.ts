import type { Product } from '@/entities/product';

export interface OrderStatus {
  id: number;
  name: string;
  background_color: string;
  text_color: string;
}

export interface OrderProduct extends Product {
  quantity: string;
  store_id: number;
  status: OrderStatus;
}

export interface OrderTotals {
  amount: number;
  quantity: number;
  discount: number;
}

export interface Order {
  order_number: string;
  date: string;
  status: OrderStatus;
  items: OrderProduct[];
  total: OrderTotals;
  company_info: CompanyInfo;
}

export interface CompanyInfo {
  id: number;
  legal_name: string;
  short_name: string;
  inn: string;
  kpp: string;
  ogrn: string;
  is_individual: number;
  def_currency: string;
  tax_system: string;
  jur_address: string;
  post_address: string;
  email: string;
  phone: string;
  is_active: number;
  created_at: string;
  updated_at: string;
}