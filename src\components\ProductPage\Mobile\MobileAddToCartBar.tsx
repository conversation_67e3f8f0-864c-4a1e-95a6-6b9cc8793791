import React from 'react';

import { AddOutlined, RemoveOutlined } from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { NavLink, useNavigate } from 'react-router-dom';

import {
  addToCart,
  changeQuantity,
  QuantityControl,
  AddToCartButton,
} from '@/features/cart';
import type { RootState } from '@/app/providers/store';
import { LoadingSmall } from '@/shared/ui/Loader';
import { PriceDisplay } from '@/widgets/product-card';

const MobileAddToCartBar = ({ product }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Directly access the cart state from Redux
  const cartItems = useSelector((state: RootState) => state.cart.cart);

  // Find the product in the cart
  const productInCart = cartItems.find((item) => item.id === product?.id);

  return (
    <div className="lg:hidden z-10 fixed bottom-[72px] w-full bg-white lining-nums proportional-nums">
      <div className="flex max-h-[64px]  justify-between gap-2 px-5 py-3">
        {productInCart ? (
          <div className="flex  gap-2 justify-between w-full">
            <QuantityControl product={productInCart} enableRemove={true} />
            <button
              className="flex justify-center items-center text-colGreen font-semibold bg-white border-colGreen border rounded cursor-pointer basis-2/3"
              onClick={(e) => {
                e.preventDefault();
                navigate('/shopping-cart');
              }}
            >
              В корзину
            </button>
          </div>
        ) : null}

        {!productInCart ? (
          <div className="flex gap-3 w-full">
            <div className="flex flex-col items-start basis-1/3">
              <div className="flex gap-2 justify-between grow">
                {/* Always show the most accurate price available */}
                <PriceDisplay
                  price={productInCart?.price || product?.price}
                  variant={productInCart ? "total-product-card" : undefined}
                />
              </div>
            </div>
            <AddToCartButton product={product}>
              {({
                handleAddToCartClick,
                isLoading,
                isSuccess,
                buttonText,
                disabled,
              }) => (
                <button
                  disabled={disabled || isLoading}
                  onClick={handleAddToCartClick}
                  className={`transition-all flex justify-center items-center min-h-10 xs:text-sm sm:text-base duration-200 ${
                    disabled ? 'bg-colGray' : 'bg-colGreen cursor-pointer'
                  } text-white rounded-md p-2 font-semibold w-full ${
                    isLoading && !disabled ? 'cursor-wait' : ''
                  } lining-nums proportional-nums`}
                >
                  {isLoading && !isSuccess ? (
                    <LoadingSmall extraStyle="white" />
                  ) : (
                    buttonText
                  )}
                </button>
              )}
            </AddToCartButton>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default MobileAddToCartBar;
