// src/shared/ui/SyncIndicator/SyncIndicator.tsx
import { useState, useEffect } from 'react';
import { cn } from '@/shared/lib/cn';

interface SyncIndicatorProps {
  isVisible: boolean;
  message?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'bottom-center';
  showSpinner?: boolean;
  className?: string;
  onClose?: () => void;
}

/**
 * A non-intrusive indicator for background synchronization operations.
 * Shows in a corner of the screen without blocking UI interaction.
 */
export const SyncIndicator = ({
  isVisible,
  message = 'Синхронизация данных...',
  position = 'bottom-right',
  showSpinner = true,
  className,
  onClose,
}: SyncIndicatorProps) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [dots, setDots] = useState('');
  
  // Animation for dots
  useEffect(() => {
    if (isVisible) {
      setIsAnimating(true);
      
      // Animate the dots
      const intervalId = setInterval(() => {
        setDots(prev => {
          if (prev === '...') return '';
          if (prev === '..') return '...';
          if (prev === '.') return '..';
          return '.';
        });
      }, 500);
      
      return () => clearInterval(intervalId);
    } else {
      setIsAnimating(false);
      setDots('');
    }
  }, [isVisible]);
  
  // Don't render anything if not visible
  if (!isVisible && !isAnimating) {
    return null;
  }
  
  // Position classes
  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2',
  };
  
  return (
    <div
      className={cn(
        "fixed z-50 flex items-center",
        "px-4 py-2 rounded-lg shadow-md",
        "bg-white border border-colGreen/20",
        "text-colDarkGray text-sm transition-opacity duration-300",
        isVisible ? "opacity-95" : "opacity-0 pointer-events-none",
        positionClasses[position],
        className
      )}
    >
      {showSpinner && (
        <div className="mr-2 animate-spin w-4 h-4 border-2 border-colGreen/80 rounded-full border-t-transparent" />
      )}
      <span>
        {message}
        <span className="inline-block w-8">{dots}</span>
      </span>
      {onClose && (
        <button
          onClick={onClose}
          className="ml-2 text-colGray hover:text-colDarkGray"
          aria-label="Close"
        >
          ✕
        </button>
      )}
    </div>
  );
};
