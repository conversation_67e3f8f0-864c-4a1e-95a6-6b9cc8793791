import { modalFilterItemObserver } from '@/shared/lib/observer';
import { ModalFilterData, ObserverItem } from '@/shared/types/observer';
import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { ModalFactory } from './ModalFactory';

export const ModalObserver = () => {
  const [data, setData] = useState<ObserverItem<ModalFilterData>[]>([]);

  const subscribe = (data: ObserverItem<ModalFilterData>[]) => {
    setData(data);
  };

  useEffect(() => {
    const unsubscribe = modalFilterItemObserver.subscribe(subscribe);
    return () => unsubscribe();
  });

  if (!data.length) return null;

  return createPortal(
    <div className={'fixed inset-[0] z-[999]'}>
      <ModalFactory data={data} />
    </div>,
    document.body
  );
};
