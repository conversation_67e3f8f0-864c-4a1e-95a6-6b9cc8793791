{"name": "rostok", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,css,md}\"", "check-types": "tsc --noEmit", "validate": "npm run lint && npm run check-types", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@egjs/react-flicking": "^4.11.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@floating-ui/react": "^0.26.10", "@hookform/resolvers": "^3.10.0", "@mui/icons-material": "^5.15.4", "@mui/lab": "^5.0.0-alpha.166", "@mui/material": "^5.15.4", "@mui/x-date-pickers": "^6.19.0", "@pbe/react-yandex-maps": "^1.2.5", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.6", "@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.10", "js-cookie": "^3.0.5", "leaflet": "^1.9.4", "lightgallery": "^2.7.2", "lucide-react": "^0.474.0", "nuka-carousel": "^7.0.0", "plural-ru": "^2.0.2", "pure-react-carousel": "^1.30.1", "query-string": "^9.1.0", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.49.3", "react-image-gallery": "^1.3.0", "react-input-mask": "^2.0.4", "react-leaflet": "^5.0.0", "react-multi-carousel": "^2.8.4", "react-redux": "^9.1.0", "react-router-dom": "^6.21.1", "react-share": "^5.1.0", "react-slick": "^0.30.2", "react-sticky-box": "^2.0.5", "react-swipeable": "^7.0.1", "react-tabs": "^6.0.2", "react-use": "^17.5.0", "redux-logger": "^3.0.6", "redux-saga": "^1.3.0", "slick-carousel": "^1.8.1", "sonner": "^1.5.0", "styled-components": "^6.1.8", "swiper": "^11.1.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.6.3", "yet-another-react-lightbox": "^3.17.1", "zod": "^3.24.1"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.6", "@iconify-icon/react": "^3.0.0", "@siberiacancode/eslint": "^2.9.0", "@siberiacancode/prettier": "^1.3.0", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/experimental-addon-test": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@storybook/test": "^8.6.12", "@types/node": "^22.8.4", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "@vitejs/plugin-react": "^4.2.1", "@vitest/browser": "^3.1.2", "@vitest/coverage-v8": "^3.1.2", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.10.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.5", "eslint-plugin-storybook": "^0.12.0", "eslint-plugin-testing-library": "^7.1.1", "playwright": "^1.52.0", "postcss": "^8.4.33", "prettier": "^3.4.2", "shadcn-ui": "^0.9.4", "storybook": "^8.6.12", "tailwindcss": "^3.4.1", "video.js": "^8.10.0", "vite-tsconfig-paths": "^5.0.1", "vitest": "^3.1.2"}, "browserslist": ["> 5%", "last 2 versions", "not dead", "not ie 11"]}