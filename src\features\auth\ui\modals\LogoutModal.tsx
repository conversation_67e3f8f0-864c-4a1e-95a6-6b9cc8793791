import React, { useCallback } from 'react';

import { Box, Modal } from '@mui/material';
import { useNavigate } from 'react-router-dom';

import { useAuthContext } from '@/entities/user/model/AuthContext';
import { useModal } from '@/features/modals/model/context';

/**
 * LogoutModal component that uses the centralized AuthContext
 */
export const LogoutModal = () => {
  const { logout } = useAuthContext();
  const navigate = useNavigate();
  const { hideModal, modalContent, isModalVisible } = useModal();

  // Logout handler using the centralized AuthContext
  const handleLogout = useCallback(() => {
    try {
      // Use the logout function from AuthContext
      // The logout function now handles clearing all Redux state and session storage
      logout();

      // Redirect to home page
      navigate('/');
      hideModal();
    } catch (error) {
      console.error('Error during logout:', error);
      // Still try to navigate away even if there was an error
      navigate('/');
      hideModal();
    }
  }, [logout, navigate, hideModal]);

  // Guard clause: don't render if modal isn't visible or modalContent is null
  if (!isModalVisible || !modalContent) return null;

  // Additional check specifically for the logout type
  const isLogoutType = modalContent.type === 'logout';
  if (!isLogoutType) return null;

  return (
    <Modal
      open={isModalVisible && isLogoutType}
      onClose={hideModal}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <Box className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 lining-nums proportional-nums bg-white rounded-lg border-none outline-none px-6 py-8 max-w-[480px] w-[95%] mm:w-full">
        <span
          onClick={hideModal}
          className="absolute top-0 right-0 text-4xl text-colGray font-light cursor-pointer pr-4"
        >
          &times;
        </span>
        <h1 className="pt-1 text-2xl mm:text-3xl text-colBlack font-semibold">
          Выход из аккаунта
        </h1>
        <p className="text-colBlack my-2">
          Вы уверены, что хотите выйти из аккаунта?
        </p>
        <div className="flex space-x-3 pt-5">
          <button
            onClick={() => hideModal()}
            className="w-1/2 h-[38px] px-6 border border-colGreen bg-white rounded text-colGreen font-semibold"
          >
            Отменить
          </button>
          <button
            onClick={handleLogout}
            className="w-1/2 h-[38px] px-6 bg-colGreen rounded text-white font-semibold"
          >
            Да
          </button>
        </div>
      </Box>
    </Modal>
  );
};