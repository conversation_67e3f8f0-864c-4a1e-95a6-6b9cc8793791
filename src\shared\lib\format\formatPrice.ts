/**
 * Formats a price value with consistent decimal handling
 * @param value - The price value to format (number or string)
 * @returns Formatted price string with 2 decimal places if it has decimals, otherwise as integer
 */
export const formatPrice = (value: number | string | undefined | null): string => {
  if (value === undefined || value === null) return '';
  const num = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(num)) return '';
  
  // Check if the number has decimal places
  const hasDecimals = num % 1 !== 0;
  // Always show 2 decimal places if it has decimals, otherwise show as integer
  return hasDecimals ? num.toFixed(2) : num.toString();
};
