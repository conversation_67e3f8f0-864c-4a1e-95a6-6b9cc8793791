import { Accordion } from '@/shared/ui/accordion';
import { PrevTreeNode, TreeNode } from '@/types/CategorieTree';
import { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { ChevronLeft } from '../icons';
import { TreeItem } from './tree-item';

interface TreeViewProps {
  tree: TreeNode;
  prevTree: PrevTreeNode[];
}

export const TreeView: React.FC<TreeViewProps> = ({ tree: treeData, prevTree }) => {
  const [mode, setMode] = useState<'short' | 'full'>('short');
  const toFive = { ...treeData, children: treeData.children.slice(0, 5) };

  const handleOpenModal = () => {
    setMode((prev) => (prev === 'short' ? 'full' : 'short'));
  };

  return (
    <div className='flex flex-col'>
      <div className='flex flex-col'>
        {prevTree.map((node) => (
          <NavLink
            to={`/catalog/${node.link}`}
            className={`hover:text-colGreen flex text-start items-center left-0 top-0 text-[16px] w-full text-start hover:bg-[#D7DBDA] py-[4px] pl-0 px-[7px] rounded-[8px]`}
          >
            <ChevronLeft className='shrink-0' size={18} />{' '}
            <span className='truncate'>{node.label}</span>
          </NavLink>
        ))}
      </div>
      <span
        className={`ml-[18px] text-start font-semibold left-0 top-0 text-[16px] w-full text-start text-wrap `}
      >
        {treeData.label}
      </span>
      <div className='ml-[24px]'>
        {(mode === 'full' ? treeData.children : toFive.children).map((node) => (
          <Accordion type='multiple' className='w-full' key={node.id}>
            <TreeItem node={node} />
          </Accordion>
        ))}
      </div>
      {treeData.children.length > 5 && (
        <button
          onClick={handleOpenModal}
          className='pt-2 ml-[6px] w-full text-left text-colGreen font-semibold text-[15px]'
        >
          {mode === 'full' ? 'Свернуть' : 'Посмотреть ещё'}
        </button>
      )}
    </div>
  );
};
