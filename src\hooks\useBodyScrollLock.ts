import { disableBodyScroll, enableBodyScroll } from '@/shared/lib/bodyScroll';
import { bodyScrollLockSingleObserver } from '@/shared/lib/observer/body.observer';
import { useEffect, useState } from 'react';

export const useBodyScrollLock = () => {
  const [isLock, setIsLock] = useState(false);

  useEffect(() => {
    if (isLock) disableBodyScroll();
    else enableBodyScroll();
  }, [isLock]);

  useEffect(() => {
    const unsubscribe = bodyScrollLockSingleObserver.subscribe(setIsLock);

    return () => {
      unsubscribe();
      setIsLock(false);
    };
  }, []);
};
