// src/components/ProductPage/Attributes/ProductAttributeValue.tsx
import React, { useState } from "react";

import {
  useFloating,
  autoUpdate,
  offset,
  flip,
  shift,
  useHover,
  useFocus,
  useDismiss,
  useRole,
  useInteractions,
  FloatingPortal,
} from "@floating-ui/react";
import { checkCloseToWhite } from "@/shared/lib";
interface AttributeValue {
  value: string | number;
  text: string;
  current: boolean;
  available: boolean;
  color?: string;
  second_color?: string;
}

interface ProductAttributeValueProps {
  id: string | number;
  value: AttributeValue;
  handleChangeAttribute: (event: React.MouseEvent<HTMLDivElement>) => void;
}

const ProductAttributeValue: React.FC<ProductAttributeValueProps> = ({ id, value, handleChangeAttribute }) => {
  const [isOpen, setIsOpen] = useState(false);

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: setIsOpen,
    placement: "top",
    whileElementsMounted: autoUpdate,
    middleware: [
      offset(5),
      flip({
        fallbackAxisSideDirection: "start",
      }),
      shift(),
    ],
  });

  const hover = useHover(context, { move: false });
  const focus = useFocus(context);
  const dismiss = useDismiss(context);
  const role = useRole(context, { role: "tooltip" });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    hover,
    focus,
    dismiss,
    role,
  ]);
  return (
    <>
      <div
        data-id={id}
        data-value={value.value}
        data-text={value.text}
        onClick={handleChangeAttribute}
        ref={refs.setReference}
        {...getReferenceProps()}
        className={`h-12 p-1 ${
          value.current ? "border-colGreen" : "border-colLightGray"
        } hover:border-colGreen ${
          value.available ? "bg-transparent" : "bg-colLightGray"
        } rounded-[10px] border flex justify-center items-center cursor-pointer`}
      >
        {value.second_color ? (
          <>
            <div
              style={{ backgroundColor: `${value.color}` }}
              className={`w-5 h-10 rounded-l-full ${checkCloseToWhite(value.color) ? 'border border-colLightGray' : ''}`}
            ></div>
            <div
              style={{ backgroundColor: `${value.second_color}` }}
              className={`w-5 h-10 rounded-r-full ${checkCloseToWhite(value.second_color) ? 'border border-colLightGray' : ''}`}
            ></div>
          </>
        ) : value.color ? (
          <div
            style={{ backgroundColor: `${value.color}` }}
            className={`w-10 h-10 rounded-full ${checkCloseToWhite(value.color) ? 'border border-colLightGray' : ''}`}
          ></div>
        ) : (
          value.text
        )}
      </div>
      {value.color && (
        <FloatingPortal>
          {isOpen && (
            <div
              ref={refs.setFloating}
              {...getFloatingProps()}
              style={{ ...floatingStyles }}
              className="w-[100px] lg:flex hidden h-[100px] border border-colLightGray rounded-[10px] overflow-hidden"
            >
              {value.second_color ? (
                <>
                  <div
                    style={{ backgroundColor: `${value.color}` }}
                    className={`w-1/2 `}
                  ></div>
                  <div
                    style={{ backgroundColor: `${value.second_color}` }}
                    className={`w-1/2`}
                  ></div>
                </>
              ) : value.color ? (
                <div
                  style={{ backgroundColor: `${value.color}` }}
                  className={`w-full h-full`}
                ></div>
              ) : (
                value.text
              )}
            </div>
          )}
        </FloatingPortal>
      )}
    </>
  );
}

export default ProductAttributeValue;
