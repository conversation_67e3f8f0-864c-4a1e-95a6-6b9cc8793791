import { ProductFilterURLInput } from '@/components/Catalog/CatalogRoot';
import { FilterCheckbox, Forbidden } from '@/shared/ui';
import { ProductFilterColor } from '@/types/Filters/ProductFilter';
import { useRef } from 'react';

interface FilterOptionColorProps {
  data: ProductFilterColor;
  onChange: (data: ProductFilterURLInput) => void;
  disabled?: boolean;
}

export const FilterOptionColor: React.FC<FilterOptionColorProps> = ({
  data,
  onChange,
  disabled = false
}) => {
  const { current: uuid } = useRef(
    Date.now().toString(36) + Math.random().toString(36).substring(2)
  );

  const handleChange = (data: ProductFilterColor, itemId: number, bool: boolean, text: string) => {
    onChange({
      alone: {
        parentId: data.id,
        id: itemId,
        type: 'multiple',
        value: bool,
        title: data.name,
        text
      }
    });
  };

  return (
    <div className='flex flex-col gap-[8px] mt-2'>
      {data.values.map((item) => (
        <FilterCheckbox
          {...item}
          id={`${uuid}-${item.id}`}
          onChange={(bool) => handleChange(data, item.id, bool, item.text)}
          disabled={disabled || !item.is_active}
          key={item.id}
          className='overflow-hidden'
        >
          {!item.color && !item.second_color ? (
            <span className='size-5 flex rounded-[4px] border border-[#C8CCCB] justify-center items-center'>
              <Forbidden size={18} color='#B5B5B5' />
            </span>
          ) : (
            <span className={`size-5 flex rounded-[4px] border justify-center items-center`}>
              <span className='size-[16px] flex rounded-full overflow-hidden'>
                <span className={`flex  w-full h-full`} style={{ background: item.color }} />
                {item.second_color && (
                  <span
                    className={` flex w-full h-full`}
                    style={{ background: item.second_color }}
                  />
                )}
              </span>
            </span>
          )}
          <p className='truncate'>{item.text}</p>
        </FilterCheckbox>
      ))}
    </div>
  );
};
