// src/components/ProductPage/Attributes/ProductAttribute.tsx
import React from 'react';
import ProductAttributeValue from './ProductAttributeValue'; // Assuming ProductAttributeValue.tsx is in the same directory

// Re-using or adapting the AttributeValue interface from ProductAttributeValue.tsx
interface AttributeValue {
  value: string | number;
  text: string;
  current: boolean;
  available: boolean;
  color?: string;
  second_color?: string;
}

interface Attribute {
  name: string;
  values: AttributeValue[];
}

interface CurrentAttributeSelection {
  [key: string]: { // or number, depending on 'id'
    text: string;
  };
}

interface ProductAttributeProps {
  id: string | number;
  attribute: Attribute;
  current: CurrentAttributeSelection;
  handleChangeAttribute: (event: React.MouseEvent<HTMLDivElement>) => void;
}

const ProductAttribute: React.FC<ProductAttributeProps> = ({ id, attribute, current, handleChangeAttribute }) => {
  return (
    <>
      <div className="flex mb-2">
        <p className="text-colDarkGray mr-1">{attribute.name}:</p>
        {current[String(id)]?.text} {/* Ensure id is string for object key access */}
      </div>
      <div className="flex flex-wrap gap-2">
        {attribute.values.map((value) => (
          <ProductAttributeValue 
            key={`${id}-${value.value}`} // More robust key
            id={id} 
            value={value} 
            // 'current' prop for ProductAttributeValue seems to be a boolean based on its own value.current
            // The 'current' prop passed to ProductAttribute seems to be the overall selection state.
            // ProductAttributeValue itself determines its 'current' state based on value.current.
            // So, we don't need to pass the 'current' object from ProductAttribute here.
            handleChangeAttribute={handleChangeAttribute} 
          />
        ))}
      </div>
    </>
  );
};

export default ProductAttribute;
