// src/widgets/header/ui/MobileHeader.tsx
import React from 'react';
import { SearchBar } from '@/features/search';
import { Icon } from '@iconify-icon/react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/app/store';
import { toggleCatalog } from '@/app/store/ui/uiSlice';


export const MobileHeader: React.FC = () => {
  const dispatch = useDispatch();
  const showCatalog = useSelector((state: RootState) => state.ui.isCatalogVisible);
  return (
    <div className="sticky top-0 bg-white z-50 shadow-md w-full p-2">
      <div className="content flex items-center space-x-2">
        <button
          onClick={() => dispatch(toggleCatalog())}
          className="bg-colGreen text-white flex justify-center items-center w-10 h-10 rounded flex-shrink-0"
          aria-label={showCatalog ? 'Close catalog' : 'Open catalog'}
        >
          {!showCatalog ? (
            <Icon icon="solar:hamburger-menu-outline" width={28} height={28}/>
          ) : (
            <Icon icon="material-symbols:close-rounded" width={28} height={28}/>
          )}
        </button>
        <div className="flex-grow flex items-center">
          <SearchBar />
        </div>
      </div>
    </div>
  );
};
