import { Product } from '@/entities/product';

// Types
export interface Review {
  id: number;
  comment_user_name: string;
  rating: number;
  comment: string;
  pluses: string;
  minuses: string;
  created_at: string;
  is_active: number;
  item: Product;
  review_id: number;
  my_comment: number;
}

export interface ReviewResponse {
  success: string;
  comments: Review[];
  total_count: number;
  total_request_time: number;
  api_processing_time: number;
  sessid: string;
}

export interface ReviewSelfListResponse extends ReviewResponse {}

export interface ReviewDetailResponse {
  success: string;
  comment: Review;
}

export interface ReviewSetRequest {
  variant_id: number;
  rating: number;
  comment: string;
  pluses?: string;
  minuses?: string;
}

export interface ReviewUpdateRequest extends ReviewSetRequest {
  review_id: number;
}

export interface ReviewDeleteRequest {
  variant_id: number;
  review_id: number;
}

export interface ReviewSetResponse {
  success: string;
  comment: Review;
}
