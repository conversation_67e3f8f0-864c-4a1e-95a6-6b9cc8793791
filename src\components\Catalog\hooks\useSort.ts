import { buildQueryParams } from '@/shared/lib/catalog-filter/catalog-filter.utils';
import { getFiltersFromUrl } from '@/shared/lib/getFiltersFromUrl';
import { OrderByType, SortOrderType, SortType } from '@/types/Filters/Sort';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

const sorts: SortType[] = [
  {
    orderBy: 'popularity',
    sortOrder: 'desc',
    title: 'По популярности'
  },
  {
    orderBy: 'price',
    sortOrder: 'desc',
    title: 'Сначала дорогие'
  },
  {
    orderBy: 'price',
    sortOrder: 'asc',
    title: 'Сначала дешёвые'
  },
  {
    orderBy: 'rating',
    sortOrder: 'desc',
    title: 'Высокий рейтинг'
  },
  {
    orderBy: 'discount',
    sortOrder: 'desc',
    title: 'По размеру скидки'
  }
];

type SortParam = `${OrderByType}-${SortOrderType}`;

export function useSort() {
  const { filterParams } = getFiltersFromUrl();
  const navigate = useNavigate();

  const [currentSort, setCurrentSort] = useState<SortType>(defaultSort);

  const handleSortChange = (sort: SortParam, cb?: () => void) => {
    const { orderBy, sortOrder } = getSort(sort);

    if (orderBy === 'popularity') {
      setCurrentSort(sorts[0]);
      const paramsToArrayString = buildQueryParams(filterParams);
      navigate(`?${paramsToArrayString.join('&')}`, { replace: true });
    } else {
      const sort = sorts.find((sort) => sort.orderBy === orderBy && sort.sortOrder === sortOrder);
      setCurrentSort(sort);
      const paramsToArrayString = buildQueryParams(filterParams);
      const updateParams = [
        ...paramsToArrayString,
        `sort_orderBy=${sort.orderBy}`,
        `sort_sortOrder=${sort.sortOrder}`
      ];
      navigate(`?${updateParams.join('&')}`, { replace: true });
    }

    cb?.();
  };

  function getSort(sort: SortParam) {
    const [orderBy, sortOrder] = sort.split('-') as [OrderByType, SortOrderType];
    return { orderBy, sortOrder };
  }

  function defaultSort() {
    if ('sortOrder' in filterParams && 'sortby' in filterParams) {
      const { sortOrder, orderBy } = filterParams as {
        sortOrder: SortOrderType;
        orderBy: OrderByType;
      };
      return sorts.find((sort) => sort.orderBy === orderBy && sort.sortOrder === sortOrder);
    }
    return sorts[0];
  }

  useEffect(() => {
    if ('orderBy' in filterParams) {
      const { sortOrder, orderBy } = filterParams as {
        sortOrder: SortOrderType;
        orderBy: OrderByType;
      };
      const sort = sorts.find((sort) => sort.orderBy === orderBy && sort.sortOrder === sortOrder);
      setCurrentSort(sort);
    }
  }, []);

  const sortBy = sorts;
  return { currentSort, handleSortChange, sortBy };
}
