import React, { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';

import { Advantages } from '@/components/Home/Advantages';
import { Brands } from '@/components/Home/Brands';
import { Promotions } from '@/components/Home/Promotions';
import { useGetFiltersMutation } from '@/entities/filter';
import { useGetVariantsMutation } from '@/entities/product/api/productApi';
import { AllFiltersModal } from '@/features/modals';
import { scrollToTop } from '@/shared/lib/scrollToTop';
import { Breadcrumbs } from '@/widgets/breadcrumbs';
import { CategorySwitcher } from '@/widgets/category-switcher';

import CatalogContent from './CatalogContent/CatalogContent';
import CatalogSidebar from './CatalogSidebar/CatalogSidebar';

import type { FiltersState } from '@/types/Filters/FiltersState';
import type { ProductListCategoryChain } from '@/entities/category';

/**
 * Search component for displaying search results with filtering
 * 
 * This component is similar to the Brands and Tags components but focuses on:
 * 1. Handling search term as the primary filter
 * 2. Displaying search results with optional category filtering
 * 3. Providing filter controls specific to search context
 */
const SearchProducts = () => {
  console.log('============ SEARCH COMPONENT RENDER START ============');
  
  // Tracks state of filters modal (for mobile view)
  const [filtersModalOpen, setFiltersModalOpen] = useState(false);

  // Get search query from URL parameters
  const [searchParams] = useSearchParams();
  const searchQuery = searchParams.get('q') || '';
  console.log('Current search query:', searchQuery);

  // Routing hooks for navigation and URL parsing
  const navigate = useNavigate();
  const location = useLocation();
  console.log('Current location.search:', location.search);

  // For category switching in search results
  const [categories, setCategories] = useState<ProductListCategoryChain[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('');
  
  // Flag to prevent refetching categories when they're already loaded
  const categoriesLoaded = useRef(false);

  // ===== FILTERS STATE MANAGEMENT =====
  
  /**
   * Main filters state holding all available filters
   */
  const [filters, setFilters] = useState<FiltersState>({
    basics: {
      price: {
        min: 0,
        max: 0,
      },
      tags: [],
      brands: [],
      rating: [],
    },
    dynamics: [],
    more: [],
    category_chain: [],
  });
  
  // Loading states for filters
  const [filtersLoading, setFiltersLoading] = useState(false);
  const [filtersBlock, setFiltersBlock] = useState(false);
  
  /**
   * Trigger for filter updates
   */
  const [trigger, setTrigger] = useState('');
  console.log('Current trigger value:', trigger);
  
  // Reference to previous filters state for comparison
  const previousFilters = useRef({});

  // References for API request cancellation
  const getFiltersRef = useRef(null);
  const getProductsRef = useRef(null);

  // Get filter mutation hook from RTK Query
  const [
    getFilters,
    { isLoading: filtersIsLoading, isSuccess: filtersIsSuccess, error: filtersError },
  ] = useGetFiltersMutation();

  /**
   * Fetches new filter options based on current selection
   */
  const getNewFiltersList = async (sendObject, trigger) => {
    console.log('============ getNewFiltersList CALLED ============');
    console.log('Trigger type:', trigger);
    console.log('Send object:', JSON.stringify(sendObject));
    
    // Cancel any in-flight request
    if (getFiltersRef.current) {
      console.log('Cancelling previous filters request');
      getFiltersRef.current.abort();
    }
    
    // Create new abort controller
    const abortController = new AbortController();
    getFiltersRef.current = abortController;
    
    // Set appropriate loading state based on trigger type
    if (trigger === 'search' || trigger === 'category') {
      console.log('Setting filtersLoading to true for trigger:', trigger);
      setFiltersLoading(true);
    } else if (trigger === 'filters') {
      console.log('Setting filtersBlock to true for filter update');
      setFiltersBlock(true);
    }

    try {
      console.log('Calling filters API...');
      // Call the filter API endpoint with abort signal and search param
      const newFilters = await getFilters({
        ...sendObject,
        search: searchQuery,
        signal: abortController.signal
      });

      console.log('Filters API response received');
      
      // Type guard to check if response has data
      if ('data' in newFilters) {
        console.log('Valid filters data received:', Object.keys(newFilters.data));
        
        // Process "more" filters by marking them as additional
        const more = newFilters.data.more.map((obj) => ({
          ...obj,
          additional_filter: true,
        }));
        console.log('Processed "more" filters:', more.length);

        // Combine regular dynamic filters with "more" filters
        const newDynamics = newFilters.data.dynamics.concat(more);
        console.log('Combined dynamics count:', newDynamics.length);

        // Create the updated filters state
        const newFiltersState = {
          ...filters,
          basics: newFilters.data.basics,
          dynamics: newDynamics,
        };
        
        console.log('New filter state prepared');
        console.log('Price min/max:', 
          newFiltersState?.basics?.price?.min,
          newFiltersState?.basics?.price?.max);
        console.log('Tags count:', newFiltersState?.basics?.tags?.length);
        console.log('Brands count:', newFiltersState?.basics?.brands?.length);

        // Update the categories for the CategorySwitcher only on initial load
        if (newFilters.data.categories && !categoriesLoaded.current) {
          setCategories(newFilters.data.categories);
          categoriesLoaded.current = true;
          console.log('Categories initially loaded:', newFilters.data.categories.length);
        }

        // Update URL with new filter parameters
        const queryParams = buildQueryParams(getSendFiltersObject2(newFiltersState), sort, page);
        console.log('Building URL with query params:', queryParams);
        
        // Add search param to URL
        const newQueryParams = queryParams ? `q=${searchQuery}&${queryParams}` : `q=${searchQuery}`;
        
        navigate(
          `?${newQueryParams}`,
          { replace: true }
        );

        // Update references and state
        console.log('Updating previousFilters reference');
        previousFilters.current = newFiltersState;
        
        console.log('Setting new filters state');
        setFilters(newFiltersState);
        
        console.log('Setting trigger to:', trigger);
        setTrigger(trigger);
      } else {
        console.error('Filters API returned unexpected format:', newFilters);
      }
    } catch (error) {
      // Only log error if it's not an abort error
      if (error.name !== 'AbortError') {
        console.error('Failed to fetch filters:', error);
      } else {
        console.log('Filters request was aborted');
      }
    } finally {
      // Reset loading states
      if (trigger === 'search' || trigger === 'category') {
        console.log('Resetting filtersLoading to false');
        setFiltersLoading(false);
      } else if (trigger === 'filters') {
        console.log('Resetting filtersBlock to false');
        setFiltersBlock(false);
      }
      
      // Clear reference if this controller is still the current one
      if (getFiltersRef.current === abortController) {
        console.log('Clearing getFiltersRef');
        getFiltersRef.current = null;
      }
      console.log('============ getNewFiltersList COMPLETED ============');
    }
  };

  // ===== PRODUCTS STATE MANAGEMENT =====
  
  // Get products mutation hook from RTK Query
  const [
    getVariants,
    { isLoading: getVariantsIsLoading, isSuccess: getVariantsIsSuccess, error: getVariantsError },
  ] = useGetVariantsMutation();

  // Products state and loading indicator
  const [products, setProducts] = useState([]);
  const [productsLoading, setProductsLoading] = useState(false);
  
  /**
   * Fetches products based on current filters, sort, pagination, and search query
   */
  const getProducts = async (sendObject) => {
    console.log('============ getProducts CALLED ============');
    console.log('Send object:', JSON.stringify(sendObject));
    
    // Cancel any in-flight request
    if (getProductsRef.current) {
      console.log('Cancelling previous products request');
      getProductsRef.current.abort();
    }
    
    // Create new abort controller
    const abortController = new AbortController();
    getProductsRef.current = abortController;
    
    console.log('Setting productsLoading to true');
    setProductsLoading(true);
    
    try {
      console.log('Calling products API...');
      const productsResponse = await getVariants({
        ...sendObject,
        search: searchQuery, // Add search parameter
        signal: abortController.signal
      });
      
      console.log('Products API response received');
      
      if (productsResponse.data?.success === 'ok') {
        console.log('Valid products data received, count:', productsResponse.data?.data?.length);
        setProducts(productsResponse.data);
      } else {
        console.error('Products API returned unexpected format:', productsResponse);
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Error fetching products:', error);
      } else {
        console.log('Products request was aborted');
      }
    } finally {
      console.log('Setting productsLoading to false');
      setProductsLoading(false);
      
      if (getProductsRef.current === abortController) {
        console.log('Clearing getProductsRef');
        getProductsRef.current = null;
      }
      console.log('============ getProducts COMPLETED ============');
    }
  };
  
  /**
   * Flag to track first load for URL parameter handling
   */
  const isFirstLoad = useRef(true);
  console.log('isFirstLoad.current:', isFirstLoad.current);

  // ===== FILTER CHANGE EFFECT =====
  
  /**
   * Effect that triggers when filters change
   */
  useEffect(() => {
    console.log('============ FILTERS CHANGE EFFECT ============');
    console.log('Filters object changed');
    console.log('Current state of isFirstLoad:', isFirstLoad.current);
    
    if (isFirstLoad.current) {
      console.log('First load detected, skipping filters effect');
      return;
    }
    
    // Compare with previous filters
    const currentFiltersJson = JSON.stringify(filters);
    const previousFiltersJson = JSON.stringify(previousFilters.current);
    const hasChanged = currentFiltersJson !== previousFiltersJson;
    
    console.log('Has filters changed?', hasChanged);
    
    // Only trigger API calls if filters actually changed
    if (hasChanged) {
      console.log('Filters have changed, preparing API calls');
      const sendObject = getSendFiltersObject();
      console.log('getSendFiltersObject result:', sendObject);
      
      // Fetch updated filter options
      console.log('Calling getNewFiltersList with trigger: filters');
      getNewFiltersList(
        {
          ...sendObject,
        },
        'filters'
      );
      
      // Fetch products with updated filters
      console.log('Calling getProducts with updated filters');
      getProducts({
        ...sendObject,
        page: 1,
        limit: 20,
        orderBy: sort.sortBy || 'popularity',
        sortOrder: sort.sortOrder || 'desc',
      });
    } else {
      console.log('Filters have not changed, skipping API calls');
    }
    console.log('============ END FILTERS CHANGE EFFECT ============');
  }, [filters]);

  // ===== SEARCH CHANGE EFFECT =====
  
  /**
   * Effect that triggers when search query changes
   */
  useEffect(() => {
    console.log('============ SEARCH CHANGE EFFECT ============');
    console.log('Search query changed to:', searchQuery);
    
    if (!searchQuery) {
      console.log('Empty search query, skipping API calls');
      return;
    }
    
    isFirstLoad.current = false;
    
    // Parse URL query parameters
    const queryParams = parseQueryParams(location.search);
    console.log('Parsed query params:', queryParams);
    
    // Initialize category if it's in the URL
    if (queryParams.filtersObject.category_id) {
      setSelectedCategory(queryParams.filtersObject.category_id);
    }

    // Set up search filter object
    const searchFilters = {
      category_id: selectedCategory || queryParams.filtersObject.category_id || '',
      min_price: queryParams.filtersObject.min_price || null,
      max_price: queryParams.filtersObject.max_price || null,
      brands: queryParams.filtersObject.brands || [],
      tags: queryParams.filtersObject.tags || [],
      filters: queryParams.filtersObject.filters || {},
      last_changed: {},
    };
    
    // Reset the categoriesLoaded flag to ensure we can load categories at least once
    categoriesLoaded.current = false;
    
    // Fetch filters for this search
    console.log('Calling getNewFiltersList with search params');
    getNewFiltersList(searchFilters, 'search');

    // Fetch products for this search
    console.log('Calling getProducts with search params');
    getProducts({
      ...searchFilters,
      page: queryParams.page || 1,
      limit: 20,
      orderBy: queryParams.sortObject.sortBy || sort.sortBy || 'popularity',
      sortOrder: queryParams.sortObject.sortOrder || sort.sortOrder || 'desc',
    });

    console.log('Setting page to 1');
    setPage(1);
    
    console.log('Scrolling to top');
    scrollToTop();
    console.log('============ END SEARCH CHANGE EFFECT ============');
  }, [searchQuery]);

  // ===== CATEGORY SELECTION EFFECT =====
  
  /**
   * Effect to handle category selection in search results
   * Now also fetches filters when a category is selected
   */
  useEffect(() => {
    console.log('============ CATEGORY SELECTION EFFECT ============');
    console.log('Selected category changed to:', selectedCategory);
    
    if (isFirstLoad.current || !searchQuery) {
      console.log('First load or no search query, skipping category effect');
      return;
    }
    
    // Prepare API call parameters with the updated category
    const sendObject = {
      ...getSendFiltersObject(),
      category_id: selectedCategory,
    };
    
    // Fetch filters when a category changes (whether selected or deselected)
    console.log('Calling getNewFiltersList with category trigger');
    getNewFiltersList(sendObject, 'category');
    
    // Fetch products with the updated category
    console.log('Calling getProducts with updated category');
    getProducts({
      ...sendObject,
      page: 1,
      limit: 20,
      orderBy: sort.sortBy || 'popularity',
      sortOrder: sort.sortOrder || 'desc',
    });
    
    // Update URL with new filter parameters
    const queryParams = buildQueryParams({ ...sendObject, category_id: selectedCategory }, sort, 1);
    console.log('Building URL with query params:', queryParams);
    
    // Add search param to URL
    const newQueryParams = queryParams ? `q=${searchQuery}&${queryParams}` : `q=${searchQuery}`;
    
    navigate(
      `?${newQueryParams}`,
      { replace: true }
    );
    
    console.log('============ END CATEGORY SELECTION EFFECT ============');
  }, [selectedCategory]);

  // ===== URL PARAMS CHANGE EFFECT =====
  
  /**
   * Effect to handle URL parameter changes
   */
  useEffect(() => {
    console.log('============ SEARCH PARAMS CHANGE EFFECT ============');
    console.log('location.search changed:', location.search);
    
    if (isFirstLoad.current || !searchQuery) {
      console.log('First load or no search query, will be handled by search effect');
      return;
    }
    
    const queryParams = parseQueryParams(location.search);
    console.log('Parsed query params:', queryParams);
    
    // Add additional URL parameter handling if needed
    
    console.log('============ END SEARCH PARAMS CHANGE EFFECT ============');
  }, [location.search]);
  
  // ===== FILTER RESET FUNCTIONALITY =====
  
  /**
   * Resets all filters to their default values while maintaining the search query
   */
  const resetFilters = async () => {
    console.log('============ resetFilters CALLED ============');
    console.log('Resetting all filters except search query');
    
    // If a category is selected, keep it
    const categoryToKeep = selectedCategory || '';
    
    console.log('Calling getNewFiltersList with reset values');
    getNewFiltersList(
      {
        category_id: categoryToKeep,
        min_price: null,
        max_price: null,
        brands: [],
        tags: [],
        filters: {},
        last_changed: {},
      },
      categoryToKeep ? 'category' : 'search'
    );

    console.log('Calling getProducts with reset values');
    getProducts({
      page: page,
      limit: 20,
      orderBy: sort.sortBy,
      sortOrder: sort.sortOrder,
      category_id: categoryToKeep,
      min_price: null,
      max_price: null,
      brands: [],
      tags: [],
      filters: {},
      last_changed: {},
    });
    
    console.log('Scrolling to top');
    scrollToTop();
    console.log('============ resetFilters COMPLETED ============');
  };
  
  // ===== PAGINATION STATE =====
  
  // Current page state
  const [page, setPage] = useState(1);
  console.log('Current page:', page);
  
  /**
   * Handles pagination changes
   */
  const handlePagination = (e, p) => {
    console.log('============ handlePagination CALLED ============');
    console.log('Changing page from', page, 'to', p);
    
    setPage(p);
    
    console.log('Calling getProducts for new page');
    getProducts({
      ...getSendFiltersObject(),
      page: p,
      limit: 20,
      orderBy: sort.sortBy,
      sortOrder: sort.sortOrder,
    });
    
    console.log('Scrolling to top');
    scrollToTop();
    console.log('============ handlePagination COMPLETED ============');
  };

  // ===== SORTING STATE =====
  
  // Sort state with default sorting by popularity descending
  const [sort, setSort] = useState({
    sortBy: 'popularity',
    sortOrder: 'desc',
  });
  console.log('Current sort:', sort);
  
  // Reference to previous sort state for comparison
  const sortPrevious = useRef(sort);
  
  /**
   * Effect that triggers when sort changes
   */
  useEffect(() => {
    console.log('============ SORT CHANGE EFFECT ============');
    console.log('Sort changed:', sort);
    
    const currentSortJson = JSON.stringify(sort);
    const previousSortJson = JSON.stringify(sortPrevious.current);
    const hasChanged = currentSortJson !== previousSortJson;
    
    console.log('Has sort changed?', hasChanged);
    
    if (hasChanged) {
      console.log('Sort has changed, calling getProducts');
      getProducts({
        ...getSendFiltersObject(),
        page: page,
        limit: 20,
        orderBy: sort.sortBy,
        sortOrder: sort.sortOrder,
      });
      
      console.log('Updating sortPrevious reference');
      sortPrevious.current = sort;
    } else {
      console.log('Sort has not changed, skipping API call');
    }
    console.log('============ END SORT CHANGE EFFECT ============');
  }, [sort]);

  // ===== UTILITY FUNCTIONS =====
  
  /**
   * Transforms filters state into an object for API requests
   */
  const getSendFiltersObject2 = (filters) => {
    console.log('============ getSendFiltersObject2 CALLED ============');
    console.log('Input filters:', Object.keys(filters));
    
    // Extract selected brands
    const brands = filters?.basics?.brands?.reduce((acc, brand) => {
      if (brand.is_selected) {
        acc.push(brand.id);
      }
      return acc;
    }, []);
    console.log('Selected brands:', brands);

    // Extract selected tags
    const tags = filters?.basics?.tags?.reduce((acc, tag) => {
      if (tag.is_selected) {
        acc.push(tag.tag);
      }
      return acc;
    }, []);
    console.log('Selected tags:', tags);

    // Extract selected dynamic filters
    const dynamicFilters = filters?.dynamics
      ?.filter((filter) => filter.values.some((value) => value.is_selected))
      .reduce((acc, filter) => {
        acc[filter.id] = filter.values
          .filter((value) => value.is_selected)
          .map((value) => value.id);
        return acc;
      }, {});
    console.log('Dynamic filters:', dynamicFilters);

    // Create the final request object
    const result = {
      category_id: selectedCategory || '',
      min_price: filters?.basics?.price?.current_values?.min || null,
      max_price: filters?.basics?.price?.current_values?.max || null,
      brands: brands || [],
      tags: tags || [],
      filters: dynamicFilters || {},
      last_changed: filters?.lastChanged || {},
    };
    
    console.log('getSendFiltersObject2 result:', result);
    console.log('============ getSendFiltersObject2 COMPLETED ============');
    return result;
  };

  /**
   * Transforms the current filters state into an object for API requests
   */
  const getSendFiltersObject = () => {
    console.log('============ getSendFiltersObject CALLED ============');
    
    const brands = filters?.basics?.brands?.reduce((acc, brand) => {
      if (brand.is_selected) {
        acc.push(brand.id);
      }
      return acc;
    }, []);
    console.log('Selected brands:', brands);

    const tags = filters?.basics?.tags?.reduce((acc, tag) => {
      if (tag.is_selected) {
        acc.push(tag.tag);
      }
      return acc;
    }, []);
    console.log('Selected tags:', tags);

    const dynamicFilters = filters?.dynamics
      ?.filter((filter) => filter.values.some((value) => value.is_selected))
      .reduce((acc, filter) => {
        acc[filter.id] = filter.values
          .filter((value) => value.is_selected)
          .map((value) => value.id);
        return acc;
      }, {});
    console.log('Dynamic filters:', dynamicFilters);

    const result = {
      category_id: selectedCategory || '',
      min_price: filters?.basics?.price?.current_values?.min || null,
      max_price: filters?.basics?.price?.current_values?.max || null,
      brands: brands || [],
      tags: tags || [],
      filters: dynamicFilters || {},
      last_changed: filters?.lastChanged || {},
    };
    
    console.log('getSendFiltersObject result:', result);
    console.log('============ getSendFiltersObject COMPLETED ============');
    return result;
  };

  /**
   * Parses URL query parameters into filter, sort, and pagination objects
   */
  const parseQueryParams = (queryString) => {
    console.log('============ parseQueryParams CALLED ============');
    console.log('Query string:', queryString);
    
    const params = new URLSearchParams(queryString);
    const filtersObject = {
      min_price: null,
      max_price: null,
      brands: [],
      tags: [],
      filters: {},
      last_changed: {},
      category_id: '',
    };
    const sortObject = {
      sortBy: undefined,
      sortOrder: undefined,
    };
    let page = undefined;

    // Parse min_price and max_price
    if (params.has('min_price')) {
      filtersObject.min_price =
        params.get('min_price') === 'null'
          ? null
          : parseInt(params.get('min_price'), 10);
      console.log('Parsed min_price:', filtersObject.min_price);
    }
    if (params.has('max_price')) {
      filtersObject.max_price =
        params.get('max_price') === 'null'
          ? null
          : parseInt(params.get('max_price'), 10);
      console.log('Parsed max_price:', filtersObject.max_price);
    }

    // Parse category_id
    if (params.has('category_id')) {
      filtersObject.category_id = params.get('category_id');
      console.log('Parsed category_id:', filtersObject.category_id);
    }

    // Parse brands
    if (params.has('brands')) {
      filtersObject.brands = params.get('brands').split(',').map(Number);
      console.log('Parsed brands:', filtersObject.brands);
    }

    // Parse tags
    if (params.has('tags')) {
      filtersObject.tags = params.get('tags').split(',');
      console.log('Parsed tags:', filtersObject.tags);
    }

    // Parse dynamic filters (filter_*)
    params.forEach((value, key) => {
      if (key.startsWith('filter_')) {
        const filterId = key.replace('filter_', '');
        filtersObject.filters[filterId] = value.split(',').map(Number);
        console.log(`Parsed filter_${filterId}:`, filtersObject.filters[filterId]);
      }
    });

    // Parse last_changed
    if (params.has('last_changed_type')) {
      filtersObject.last_changed.type = params.get('last_changed_type');
      console.log('Parsed last_changed_type:', filtersObject.last_changed.type);
    }
    if (params.has('last_changed_filter')) {
      filtersObject.last_changed.filter = parseInt(
        params.get('last_changed_filter'),
        10
      );
      console.log('Parsed last_changed_filter:', filtersObject.last_changed.filter);
    }

    // Parse sortBy and sortOrder
    if (params.has('sort_by')) {
      sortObject.sortBy = params.get('sort_by');
      console.log('Parsed sort_by:', sortObject.sortBy);
    }
    if (params.has('sort_order')) {
      sortObject.sortOrder = params.get('sort_order');
      console.log('Parsed sort_order:', sortObject.sortOrder);
    }

    // Parse page
    if (params.has('page')) {
      page = parseInt(params.get('page'), 10);
      console.log('Parsed page:', page);
    }

    const result = {
      filtersObject,
      sortObject,
      page,
    };
    
    console.log('parseQueryParams result:', result);
    console.log('============ parseQueryParams COMPLETED ============');
    return result;
  };

  /**
   * Builds URL query parameters from filter, sort, and pagination objects
   */
  const buildQueryParams = (filtersObject, sortObject, page) => {
    console.log('============ buildQueryParams CALLED ============');
    console.log('Filters object:', filtersObject);
    console.log('Sort object:', sortObject);
    console.log('Page:', page);
    
    const params = new URLSearchParams();

    // Category ID if selected
    if (filtersObject.category_id) {
      params.set('category_id', filtersObject.category_id);
      console.log('Added category_id to URL:', filtersObject.category_id);
    }

    // Price range
    if (filtersObject.min_price !== undefined) {
      params.set('min_price', filtersObject.min_price);
      console.log('Added min_price to URL:', filtersObject.min_price);
    }
    if (filtersObject.max_price !== undefined) {
      params.set('max_price', filtersObject.max_price);
      console.log('Added max_price to URL:', filtersObject.max_price);
    }

    // Brands
    if (filtersObject.brands?.length) {
      params.set('brands', filtersObject.brands.join(','));
      console.log('Added brands to URL:', filtersObject.brands.join(','));
    }

    // Tags
    if (filtersObject.tags?.length) {
      params.set('tags', filtersObject.tags.join(','));
      console.log('Added tags to URL:', filtersObject.tags.join(','));
    }

    // Dynamic Filters
    if (
      filtersObject.filters &&
      Object.keys(filtersObject.filters).length > 0
    ) {
      for (const key in filtersObject.filters) {
        params.set(`filter_${key}`, filtersObject.filters[key].join(','));
        console.log(`Added filter_${key} to URL:`, filtersObject.filters[key].join(','));
      }
    }

    // Last Changed
    if (filtersObject.last_changed.filter !== undefined) {
      params.set('last_changed_type', filtersObject.last_changed.type);
      params.set('last_changed_filter', filtersObject.last_changed.filter);
      console.log('Added last_changed to URL');
    }

    // Sort parameters
    if (sortObject.sortBy !== undefined) {
      params.set('sort_by', sortObject.sortBy);
      console.log('Added sort_by to URL:', sortObject.sortBy);
    }
    if (sortObject.sortOrder !== undefined) {
      params.set('sort_order', sortObject.sortOrder);
      console.log('Added sort_order to URL:', sortObject.sortOrder);
    }

    // Pagination
    if (page) {
      params.set('page', page);
      console.log('Added page to URL:', page);
    }

    const result = params.toString();
    console.log('Final URL query params:', result);
    console.log('============ buildQueryParams COMPLETED ============');
    return result;
  };

  // ===== CATEGORY CHANGE HANDLER =====
  
  /**
   * Handles category selection change
   */
  const handleCategoryChange = (categoryId) => {
    console.log('============ handleCategoryChange CALLED ============');
    console.log('Category changed to:', categoryId);
    
    setSelectedCategory(categoryId === selectedCategory ? '' : categoryId);
    console.log('============ handleCategoryChange COMPLETED ============');
  };

  // Clean up any pending requests when component unmounts
  useEffect(() => {
    console.log('============ CLEANUP EFFECT ============');
    return () => {
      console.log('Component unmounting, cleaning up pending requests');
      if (getFiltersRef.current) {
        console.log('Aborting filters request');
        getFiltersRef.current.abort();
      }
      if (getProductsRef.current) {
        console.log('Aborting products request');
        getProductsRef.current.abort();
      }
    };
  }, []);

  // Check if we have dynamic filters that should only be shown when a category is selected
  const hasDynamicFilters = filters?.dynamics?.length > 0;

  console.log('============ COMPONENT RENDER END ============');
  
  // ===== COMPONENT RENDER =====
  return (
    <div className="content lining-nums proportional-nums">
      {/* Breadcrumbs navigation */}
      <Breadcrumbs />
      
      {/* Search header with search term and category switcher */}
      <div className="bg-gray-100 rounded-lg p-3 mm:p-4 mb-8 mt-4">
        <h3 className="font-semibold text-2xl mm:text-4xl text-colBlack pb-2">
          {searchQuery ? `Результаты поиска: ${searchQuery}` : 'Поиск'}
        </h3>
        {categories?.length > 0 && (
          <CategorySwitcher
            categories={categories}
            selectedCategory={selectedCategory}
            onCategoryChange={handleCategoryChange}
          />
        )}
      </div>

      {/* Main catalog layout with sidebar and content */}
      <div className="flex pb-10 min-h-[420px]">
        {/* Sidebar with filters - always visible on desktop */}
        <div className="md:block hidden basis-1/4 mr-5">
          <CatalogSidebar
            setFiltersModalOpen={setFiltersModalOpen}
            filters={{
              ...filters,
              // Only show dynamic filters when a category is selected
              dynamics: selectedCategory ? filters.dynamics : []
            }}
            setFilters={setFilters}
            trigger={trigger}
            setTrigger={setTrigger}
            resetFilters={resetFilters}
            filtersIsLoading={filtersLoading}
            filtersBlock={filtersBlock}
          />
        </div>
        
        {/* Main content with products */}
        <CatalogContent
          setFiltersModalOpen={setFiltersModalOpen}
          products={products}
          getVariantsIsLoading={productsLoading}
          page={page}
          handlePagination={handlePagination}
          sort={sort}
          setSort={setSort}
        />
      </div>
      
      {/* Additional promotional components */}
      <Promotions />
      <Brands />
      <Advantages />
      
      {/* Mobile filters modal - with or without dynamic filters based on category selection */}
      <AllFiltersModal
        open={filtersModalOpen}
        setOpen={setFiltersModalOpen}
        filters={{
          ...filters,
          // Only show dynamic filters when a category is selected
          dynamics: selectedCategory ? filters.dynamics : []
        }}
        setFilters={setFilters}
        trigger={trigger}
        setTrigger={setTrigger}
        resetFilters={resetFilters}
        filtersIsLoading={filtersLoading}
        filtersBlock={filtersBlock}
      />
    </div>
  );
};

export default SearchProducts;