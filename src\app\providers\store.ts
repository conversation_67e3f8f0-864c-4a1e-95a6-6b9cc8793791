import { combineReducers, configureStore } from '@reduxjs/toolkit';
import { useDispatch } from 'react-redux';

import organizationsReducer from '@/entities/organization/model/organizationsSlice';
import userReducer from '@/entities/user/model/userSlice';
import uiReducer from '../store/ui/uiSlice';
// import { catalogReducer } from '@/widgets/catalog'; // Temporarily commented out
// import { productReducer } from '@/widgets/product-detail'; // Temporarily commented out
import cartReducer from '@/features/cart/model/cartSlice';
import { comparisonReducer } from '@/features/comparison/model/comparisonSlice';
import favoriteReducer from '@/features/favorite/model/favoriteSlice';
import recentItemsReducer from '@/features/recent-items/model/recentItemsSlice';
import searchReducer from '@/features/search/model/slice';
import { api } from '@/shared/api/api';

const rootReducer = combineReducers({
  ui: uiReducer,
  user: userReducer,
  organizations: organizationsReducer,
  recentItems: recentItemsReducer,
  // catalog: catalogReducer, // Temporarily commented out
  // product: productReducer, // Temporarily commented out
  cart: cartReducer,
  comparison: comparisonReducer,
  favorite: favoriteReducer,
  search: searchReducer,
  [api.reducerPath]: api.reducer,
});

export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }).concat(api.middleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export const useAppDispatch = useDispatch.withTypes<AppDispatch>();

// Export the store as the default export
export default store;