# Files and Folders for Deletion

This list tracks items to be deleted after refactoring and testing.

- [x] `src/components/Profile/Organizations/OrgCard.jsx` - Converted to `src/components/Profile/Organizations/OrgCard.tsx`. Typed. (Sub-component of `Organizations.tsx`).

*Note: `ProtectedRoute.tsx` was created in `src/features/auth/` instead of `src/features/auth/guard/` due to file creation issues. This can be revisited.*

- [x] `src/pages/Wholesale/Wholesale.jsx` - Replaced by `src/pages/WholesalePage.tsx`. Router import confirmed. **Old file deleted.**
- [x] `src/pages/Wholesale/` - **DELETED.**

*Note: `WholesalePage.tsx` was temporarily created in `src/pages/` instead of `src/pages/wholesale-page/ui/` due to persistent file/directory creation issues. This needs to be revisited to establish proper FSD structure.*

- [x] `src/components/About/WantToBePartnerFrom.jsx` - Refactored to `src/features/feedback/ui/SupplierRequestForm.tsx`. Imports updated. **Old file deleted.**
- [x] `src/components/About/WeInContactForm.jsx` - Unused component. **Old file deleted.**
- [x] `src/components/About/` - **DELETED.**
- [x] `src/components/ProtectedRoute/ProtectedRoute.jsx` - Obsolete. Replaced by `src/features/auth/guard/ProtectedRoute.tsx`. **Old file deleted.**
- [x] `src/components/ProtectedRoute/` - **DELETED.**
- [x] `src/components/Profile/ChangePassword/ChangePassword.jsx` - Refactored to `src/features/change-password/ui/ChangePasswordForm.tsx`. Imports updated. **Old file deleted.**
- [x] `src/components/Profile/ChangePassword/` - **DELETED.**
- [x] `src/components/Profile/Organizations/Organizations.jsx` - Converted to `src/components/Profile/Organizations/Organizations.tsx`. Typed. (Future: Consider moving to FSD `widgets` or `features` slice).
- [x] `src/shared/ui/gx-widget-react/components/CountrySelector/CountrySelector.jsx` - Converted to `CountrySelector.tsx`. Typed. Imports updated. **Old file deleted.**
- [x] `src/shared/ui/gx-widget-react/components/VerificationInput.jsx` - Converted to `VerificationInput.tsx`. Typed. **Old file deleted.**
- [x] `src/shared/ui/gx-widget-react/components/GxPhoneInput/GxPhoneInput.jsx` - Converted to `GxPhoneInput.tsx`. Typed. Imports updated. **Old file deleted.**
- [ ] `src/components/ProductPage/Attributes/PreviewPopover.jsx` - **DELETED (unused component).**
- [x] `src/components/ProductPage/Attributes/ProductAttributeValue.jsx` - Converted to `ProductAttributeValue.tsx`. Typed. **Old file deleted.**
- [x] `src/components/ProductPage/Attributes/ProductAttribute.jsx` - Converted to `ProductAttribute.tsx`. Typed. **Old file deleted.**
- [x] `src/components/ProductPage/ProductTabs/CharactersticsTab.jsx` - Converted to `CharactersticsTab.tsx`. Typed. **Old file deleted.**
- [x] `src/components/ProductPage/ProductTabs/FilesTab.jsx` - Converted to `FilesTab.tsx`. Typed. **Old file deleted.**
- [x] `src/components/ProductPage/ProductTabs/File.jsx` - Converted to `File.tsx`. Typed. **Old file deleted.**
- [x] `src/components/ProductPage/ProductTabs/InfoTab.jsx` - Converted to `InfoTab.tsx`. Typed. **Old file deleted.**
- [x] `src/components/ProductPage/ProductTabs/ReviewsTab.jsx` - Converted to `ReviewsTab.tsx`. Typed. **Old file deleted.**
- [x] `src/components/ProductPage/Mobile/MobileNameBar.jsx` - Converted to `MobileNameBar.tsx`. Typed. **Old file deleted.**
- [x] `src/components/ProductPage/Mobile/MobileTopBar.jsx` - Converted to `MobileTopBar.tsx`. Typed. **Old file deleted.**
- [x] `src/components/ProductPage/Mobile/MobileProductInfo/MobileCharacteristics.jsx` - Converted to `MobileCharacteristics.tsx`. Typed. **Old file deleted.**
- [x] `src/components/ProductPage/Mobile/MobileProductInfo/MobileProductInfo.jsx` - Converted to `MobileProductInfo.tsx`. Typed. **Old file deleted.**
- [x] `src/components/ProductPage/Mobile/MobileProductInfo/MobileFiles.jsx` - Converted to `MobileFiles.tsx`. Typed. **Old file deleted.**
- [x] `src/components/ProductPage/Mobile/MobileProductInfo/MobileReviews.jsx` - Converted to `MobileReviews.tsx`. Typed. **Old file deleted.**
- [x] `src/components/ProductPage/Mobile/MobileProductInfo/MobileInfo.jsx` - Converted to `MobileInfo.tsx`. Typed. **Old file deleted.**
- [x] `src/components/ProductPage/ProductTabs/ProductTabs.jsx` - Converted to `ProductTabs.tsx`. Typed. Placeholder props for `current` and `group`. Imports for child tabs will need updating as they are converted. **Old file deleted.**

- `src/widgets/footer/FooterLogo.tsx` (New sub-component, temporarily in `src/widgets/footer/` instead of `src/widgets/footer/ui/`).
- `src/widgets/footer/FooterNewsletter.tsx` (New sub-component, temporarily in `src/widgets/footer/` instead of `src/widgets/footer/ui/`).
- `src/widgets/footer/FooterMobileNav.tsx` (New sub-component, temporarily in `src/widgets/footer/` instead of `src/widgets/footer/ui/`).
- `src/widgets/footer/FooterDesktopNav.tsx` (New sub-component, temporarily in `src/widgets/footer/` instead of `src/widgets/footer/ui/`).
- `src/widgets/footer/FooterCopyrightAndSocials.tsx` (New sub-component, temporarily in `src/widgets/footer/` instead of `src/widgets/footer/ui/`).

### Shared Components / Helpers

- [x] `src/helpers/Errors/ErrorEmpty.jsx` - Replaced by `src/shared/ui/ErrorEmpty.tsx`. All imports updated. **Safe to delete.**
- `src/shared/ui/ErrorEmpty.tsx` (New .tsx version, temporarily in `src/shared/ui/` instead of `src/shared/ui/ErrorEmpty/`)
- [x] `src/helpers/FAQStyledAccordion.jsx` - Replaced by `src/shared/ui/StyledAccordion.tsx`. Imports updated. **Old file deleted.**
- [x] `src/helpers/QuestionForm.tsx` - Refactored to `src/features/feedback/ui/FeedbackForm.tsx`. Imports updated. **Old file deleted.**
- [x] `src/helpers/` - **DELETED.**

- [x] `src/pages/PageNotFound/PageNotFound.jsx` - Replaced by `src/pages/PageNotFound.tsx`. Router import confirmed. **Old file deleted.**
- [x] `src/pages/PageNotFound/` - **DELETED.**
- [x] `src/helpers/Errors/ErrorServer.jsx` - Converted to `src/shared/ui/ErrorServer.tsx`. Imports updated. **Old file deleted.**
- [x] `src/helpers/Errors/` - **DELETED.**

- `src/pages/Contacts/Contacts.tsx`
- `src/pages/Contacts/` (if empty after deleting the .tsx file)

*Note: `ContactsPage.tsx` was temporarily created in `src/pages/` instead of `src/pages/contacts-page/ui/` due to persistent file/directory creation issues. This needs to be revisited to establish proper FSD structure.*

- `src/pages/FAQ/FAQ.tsx`
- `src/pages/FAQ/` (if empty after deleting the .tsx file)

*Note: `FAQPage.tsx` was temporarily created in `src/pages/` instead of `src/pages/faq-page/ui/` due to persistent file/directory creation issues. This needs to be revisited to establish proper FSD structure.*

- `src/pages/Reviews/ReviewsPage.jsx`
- `src/pages/Reviews/` (if empty after deleting the .jsx file)

*Note: `ReviewsPage.tsx` was temporarily created in `src/pages/` instead of `src/pages/reviews-page/ui/` due to persistent file/directory creation issues. This needs to be revisited to establish proper FSD structure.*

- `src/pages/Comparison/Comparison.tsx`
- `src/pages/Comparison/` (if empty after deleting the .tsx file)

*Note: `ComparisonPage.tsx` was temporarily created in `src/pages/` instead of `src/pages/comparison-page/ui/` due to persistent file/directory creation issues. This needs to be revisited to establish proper FSD structure.*

- `src/pages/Favorites/Favorites.tsx`
- `src/pages/Favorites/` (if empty after deleting the .tsx file)

*Note: `FavoritesPage.tsx` was temporarily created in `src/pages/` instead of `src/pages/favorites-page/ui/` due to persistent file/directory creation issues. This needs to be revisited to establish proper FSD structure.*

- `src/pages/Checkout/components/CheckoutPage.tsx`
- `src/pages/Checkout/components/` (if empty after refactoring all its components)
- `src/pages/Checkout/` (if empty after refactoring all its components and subdirectories)

*Note: `CheckoutPage.tsx` was temporarily created in `src/pages/` instead of `src/pages/checkout-page/ui/` due to persistent file/directory creation issues. The local imports within it (e.g., `./Checkout/components/CheckoutSummary`) are temporarily adjusted to reflect its new location relative to their old one and will need to be updated once those sub-components are refactored into their FSD slices. This needs to be revisited to establish proper FSD structure.*

- `src/pages/About/About.tsx`
- `src/pages/About/` (if empty after deleting the .tsx file)

*Note: `AboutUsPage.tsx` was temporarily created in `src/pages/` instead of `src/pages/about-us-page/ui/` due to persistent file/directory creation issues. This needs to be revisited to establish proper FSD structure.*

- `src/pages/PaymentDelivery/PaymentDelivery.tsx` (This file is being refactored into `PaymentPage.tsx`. The original path should be deleted after successful refactor and move).
- `src/pages/PaymentDelivery/` (if empty after deleting/moving `PaymentDelivery.tsx`)

*Note: `DeliveryPage.tsx` was temporarily created in `src/pages/` by extracting content from `PaymentDelivery.tsx`, instead of `src/pages/delivery-page/ui/`, due to persistent file/directory creation issues. This needs to be revisited to establish proper FSD structure.

*Note: `PaymentPage.tsx` was temporarily created in `src/pages/` by refactoring `PaymentDelivery.tsx`, instead of `src/pages/payment-page/ui/`, due to persistent file/directory creation issues. The original `src/pages/PaymentDelivery/PaymentDelivery.tsx` and its directory `src/pages/PaymentDelivery/` are marked for deletion. This needs to be revisited to establish proper FSD structure.*

- `src/pages/Contacts/Contacts.tsx` (To be deleted after `ContactsPage.tsx` is confirmed and moved/created).
- `src/pages/Contacts/` (Directory to be deleted if it becomes empty after `Contacts.tsx` is removed).

*Note: `ContactsPage.tsx` was found correctly refactored and already present in `src/pages/` as its temporary location (instead of `src/pages/contacts-page/ui/`) due to persistent file/directory creation issues. The original `src/pages/Contacts/Contacts.tsx` and its directory are marked for deletion. This needs to be revisited to establish proper FSD structure.*

- `src/pages/FAQ/FAQ.tsx` (To be deleted after `FaqPage.tsx` is confirmed and moved/created).
- `src/pages/FAQ/` (Directory to be deleted if it becomes empty after `FAQ.tsx` is removed).

*Note: `FaqPage.tsx` was found correctly refactored and already present in `src/pages/` as its temporary location (instead of `src/pages/faq-page/ui/`) due to persistent file/directory creation issues. The original `src/pages/FAQ/FAQ.tsx` and its directory are marked for deletion. This needs to be revisited to establish proper FSD structure.*

*Note: `ReviewsPage.tsx` was found correctly refactored (already a .tsx file, named with Page suffix, and typed as React.FC) and already present in `src/pages/` as its temporary location (instead of `src/pages/reviews-page/ui/`) due to persistent file/directory creation issues. This needs to be revisited to establish proper FSD structure. The original `src/pages/Reviews/ReviewsPage.jsx` and its directory `src/pages/Reviews/` are marked for deletion.*

- `src/pages/Reviews/ReviewsPage.jsx` (To be deleted as `ReviewsPage.tsx` is the refactored version).
- `src/pages/Reviews/` (Directory to be deleted if it becomes empty after `ReviewsPage.jsx` is removed).

*Note: `CheckoutPage.tsx` was found correctly refactored (already a .tsx file, named with Page suffix, and typed as React.FC) and already present in `src/pages/` as its temporary location (instead of `src/pages/checkout-page/ui/`) due to persistent file/directory creation issues. This needs to be revisited to establish proper FSD structure.*
- `src/pages/Checkout/components/CheckoutPage.tsx` (Investigate: Likely an old version or duplicate of `src/pages/CheckoutPage.tsx`. Mark for deletion if confirmed redundant).
- `src/pages/Checkout/CartCheckout.tsx` (Investigate: Potentially an old or related component. Mark for deletion if confirmed redundant or obsolete).
- `src/pages/Checkout/` (Directory to be reviewed for cleanup after its contents like `CheckoutPage.tsx`, `CartCheckout.tsx`, and sub-components like `CheckoutSummary.tsx`, `ContactForm.tsx` etc. are refactored/moved to FSD structure. Sub-components will need their own FSD slice considerations, e.g., as features or widgets).

- `src/pages/Favorites/Favorites.tsx` (To be deleted after `FavoritesPage.tsx` is confirmed and moved/created).
- `src/pages/Favorites/` (Directory to be deleted if it becomes empty after `Favorites.tsx` is removed).

*Note: `FavoritesPage.tsx` was found correctly refactored (already a .tsx file, named with Page suffix, and typed as React.FC) and already present in `src/pages/` as its temporary location (instead of `src/pages/favorites-page/ui/`) due to persistent file/directory creation issues. The original `src/pages/Favorites/Favorites.tsx` and its directory are marked for deletion. This needs to be revisited to establish proper FSD structure.*

- `src/pages/Comparison/Comparison.tsx` (To be deleted after `ComparisonPage.tsx` is confirmed and moved/created).
- `src/pages/Comparison/` (Directory to be deleted if it becomes empty after `Comparison.tsx` is removed).
- [x] `src/entities/organization/model/types.ts` - Created with `Organization` and `OrganizationFormData` interfaces.

*Note: `ComparisonPage.tsx` was found correctly refactored (already a .tsx file, named with Page suffix, and typed as React.FC) and already present in `src/pages/` as its temporary location (instead of `src/pages/comparison-page/ui/`) due to persistent file/directory creation issues. Minor adjustments to loading/error messages and conditional logic were made. The original `src/pages/Comparison/Comparison.tsx` and its directory are marked for deletion. This needs to be revisited to establish proper FSD structure.*
