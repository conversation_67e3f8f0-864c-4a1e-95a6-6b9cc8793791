import { Pagination, Stack } from '@mui/material';

interface CustomPaginationProps {
  page: number;
  count: number;
  handlePagination: (event: React.ChangeEvent<unknown>, page: number) => void;
}

export const CustomPagination = ({ page, count, handlePagination }: CustomPaginationProps) => {
  const totalPages = Math.ceil(count / 20);
  
  return (
    <Stack spacing={2} className='pt-8'>
      <Pagination
        onChange={handlePagination}
        count={totalPages}
        page={page}
        variant='outlined'
        shape='rounded'
        classes={{
          ul: 'pagination-mui',
        }}
        className='flex justify-end'
      />
    </Stack>
  );
};