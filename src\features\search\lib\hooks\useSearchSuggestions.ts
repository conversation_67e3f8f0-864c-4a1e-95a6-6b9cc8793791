import { useState, useEffect, useCallback } from 'react';
import { useGetSearchSuggestionsMutation } from '../../api/searchApi';

export interface SearchSuggestion {
  variants?: any[];
  categories?: any[];
  history?: any[];
}

export interface SearchSuggestionsHookResult {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  suggestions: SearchSuggestion | null;
  isLoading: boolean;
  isError: boolean;
  getSuggestions: (term: string) => Promise<void>;
  clearSuggestions: () => void;
}

/**
 * Hook for fetching search suggestions 
 * Handles debouncing, loading states, and API communication
 */
export const useSearchSuggestions = (): SearchSuggestionsHookResult => {
  const [getSearchSuggestions, { isLoading, isError }] = useGetSearchSuggestionsMutation();
  const [searchTerm, setSearchTerm] = useState('');
  const [suggestions, setSuggestions] = useState<SearchSuggestion | null>(null);

  // Function to clear suggestions
  const clearSuggestions = useCallback(() => {
    setSuggestions(null);
  }, []);

  // Function to fetch suggestions
  const getSuggestions = useCallback(async (term: string) => {
    setSearchTerm(term);
    
    if (!term || term.length === 0) {
      setSuggestions(null);
      return;
    }
    
    try {
      const response = await getSearchSuggestions({
        text: term,
      });
      
      // Handle both API response formats
      const result = 'data' in response ? (response.data?.data || response.data) : null;
      
      if (result) {
        // Create a new object instead of modifying the original
        const newResult = { ...result };
        
        // Add the search term to history if no history entries exist
        if (!newResult.history || !newResult.history.length) {
          newResult.history = [{ text: term }];
        } else {
          // Check if term already exists in history
          const termExists = newResult.history.some((h: any) => h.text === term);
          if (!termExists) {
            // Create a new array instead of modifying the original
            newResult.history = [{ text: term }, ...newResult.history];
          }
        }
        
        // Truncate lists to maximum 10 items
        if (newResult.variants && newResult.variants.length > 10) {
          newResult.variants = newResult.variants.slice(0, 10);
        }
        if (newResult.categories && newResult.categories.length > 10) {
          newResult.categories = newResult.categories.slice(0, 10);
        }
        if (newResult.history && newResult.history.length > 10) {
          newResult.history = newResult.history.slice(0, 10);
        }
        
        setSuggestions(newResult);
      } else {
        // If no results, still create a basic suggestion object with the term in history
        setSuggestions({
          variants: [],
          categories: [],
          history: [{ text: term }]
        });
      }
    } catch (error) {
      console.error('Error fetching search suggestions:', error);
      // Even on error, provide at least the search term in history
      setSuggestions({
        variants: [],
        categories: [],
        history: [{ text: term }]
      });
    }
  }, [getSearchSuggestions]);

  // Automatically fetch suggestions when search term changes
  useEffect(() => {
    if (searchTerm.length > 0) {
      const debounceTimer = setTimeout(() => {
        getSuggestions(searchTerm);
      }, 300);
      return () => clearTimeout(debounceTimer);
    } else {
      setSuggestions(null);
    }
  }, [searchTerm, getSuggestions]);

  return {
    searchTerm,
    setSearchTerm,
    suggestions,
    isLoading,
    isError,
    getSuggestions,
    clearSuggestions
  };
};
