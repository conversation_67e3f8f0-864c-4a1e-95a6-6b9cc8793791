import { Advantages } from '@/components/Home/Advantages';
import { Brands } from '@/components/Home/Brands';
import { Promotions } from '@/components/Home/Promotions';
import { AllFiltersModal } from '@/features/modals';
import { Breadcrumbs } from '@/widgets/breadcrumbs';

import CatalogContent from './CatalogContent/CatalogContent';
import CatalogSidebar from './CatalogSidebar/CatalogSidebar';

import SidebarCategoryTree from './CatalogSidebar/SidebarCategoryTree';
import { useCatalog } from './hooks/useCatalog';

/**
 * Main catalog component handling product browsing, filtering, and sorting
 *
 * This component orchestrates several key functionalities:
 * 1. Category tree loading via useGetCategoryTreeQuery
 * 2. Filters management with state updates and API synchronization
 * 3. Product fetching with filter, sort, and pagination parameters
 * 4. URL parameter synchronization for shareable/bookmarkable states
 */
const CatProducts = () => {
  const {
    categoryTreeIsLoading,
    categoryTreeIsSuccess,
    categoryTree,
    setFiltersModalOpen,
    filters,
    setFilters,
    setTrigger,
    resetFilters,
    filtersLoading,
    filtersBlock,
    products,
    productsLoading,
    page,
    handlePagination,
    sort,
    setSort,
    filtersModalOpen,
    trigger
  } = useCatalog();

  return (
    <div className='content lining-nums proportional-nums'>
      <Breadcrumbs />

      {/* Category header with product count */}
      <div className='flex gap-3'>
        <h3 className='font-semibold text-xl mm:text-2xl lg:text-4xl text-colBlack pb-5'>
          {!categoryTreeIsLoading && categoryTreeIsSuccess ? categoryTree?.category?.name : null}{' '}
          <span className='text-colDarkGray font-normal text-sm '>
            {categoryTree?.category?.product_count}
          </span>
        </h3>
      </div>

      {/* Main catalog layout with sidebar and content */}
      <div className='flex pb-10 min-h-[420px]'>
        {/* Sidebar with filters - hidden on mobile */}
        <div className='md:block hidden basis-1/4 mr-5'>
          <SidebarCategoryTree />

          <CatalogSidebar
            setFiltersModalOpen={setFiltersModalOpen}
            filters={filters}
            setFilters={setFilters}
            trigger={setFilters}
            setTrigger={setTrigger}
            resetFilters={resetFilters}
            filtersIsLoading={filtersLoading}
            filtersBlock={filtersBlock}
          />
        </div>

        {/* Main content with products */}
        <CatalogContent
          setFiltersModalOpen={setFiltersModalOpen}
          products={products}
          getVariantsIsLoading={productsLoading}
          page={page}
          handlePagination={handlePagination}
          sort={sort}
          setSort={setSort}
        />
      </div>

      {/* Additional promotional components */}
      <Promotions />
      <Brands />
      <Advantages />

      {/* Mobile filters modal */}
      <AllFiltersModal
        categoryTree={categoryTree}
        open={filtersModalOpen}
        setOpen={setFiltersModalOpen}
        filters={filters}
        setFilters={setFilters}
        trigger={trigger}
        setTrigger={setTrigger}
        resetFilters={resetFilters}
        filtersIsLoading={filtersLoading}
        filtersBlock={filtersBlock}
      />
    </div>
  );
};

export default CatProducts;
