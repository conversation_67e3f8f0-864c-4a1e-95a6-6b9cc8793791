// Components
import {GxPhoneInput} from './components/GxPhoneInput/GxPhoneInput';
import VerificationInput from './components/VerificationInput';
import CountrySelector from './components/CountrySelector';

// Hooks
import { useCountries } from './hooks/useCountries';
import { usePhoneVerification } from './hooks/usePhoneVerification';
import { usePhoneMask } from './hooks/usePhoneMask';

// Utilities
import * as formatters from './utils/formatters';

// Export components
export {
  // Components
  GxPhoneInput,
  VerificationInput,
  CountrySelector,
  
  // Hooks
  useCountries,
  usePhoneVerification,
  usePhoneMask,
  
  // Utilities
  formatters
};

// DEPRECATED: Use PhoneInput directly instead
// Will be removed in a future update
export default PhoneInput; // Changed default export to PhoneInput for new code