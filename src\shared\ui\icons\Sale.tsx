import React from 'react';

interface SvgIconProps {
  size?: number;
  color?: string;
  stroke?: number;
}

export const Sale: React.FC<SvgIconProps> = ({ size = 24, color = 'currentColor', stroke = 2 }) => {
  return (
    <svg
      width={size + 'px'}
      height={size + 'px'}
      viewBox='0 0 48 48'
      fill='none'
      color={color}
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M21.1872 4.63766C22.966 3.78745 25.034 3.78745 26.8128 4.63766C27.5856 5.00703 28.2937 5.61102 29.2742 6.44735C29.3251 6.49076 29.3767 6.53479 29.4291 6.57945C29.9053 6.98524 30.0552 7.11 30.207 7.2117C30.5929 7.47038 31.0263 7.64991 31.4821 7.7399C31.6613 7.77528 31.8556 7.79309 32.4792 7.84285C32.5479 7.84833 32.6155 7.8537 32.6822 7.85899C33.9669 7.96093 34.8947 8.03455 35.7023 8.31981C37.5613 8.97644 39.0236 10.4387 39.6802 12.2977C39.9655 13.1053 40.0391 14.0331 40.141 15.3178C40.1463 15.3845 40.1517 15.4521 40.1572 15.5208C40.2069 16.1444 40.2247 16.3387 40.2601 16.5179C40.3501 16.9737 40.5296 17.4071 40.7883 17.793C40.89 17.9448 41.0148 18.0947 41.4206 18.5709C41.4652 18.6233 41.5093 18.675 41.5527 18.7259C42.389 19.7063 42.993 20.4144 43.3623 21.1872C44.2126 22.966 44.2126 25.034 43.3623 26.8128C42.993 27.5856 42.389 28.2937 41.5527 29.2741C41.5093 29.325 41.4652 29.3767 41.4206 29.4291C41.0148 29.9053 40.89 30.0552 40.7883 30.207C40.5296 30.5929 40.3501 31.0263 40.2601 31.4821C40.2247 31.6613 40.2069 31.8556 40.1572 32.4792C40.1517 32.5479 40.1463 32.6155 40.141 32.6822C40.0391 33.9669 39.9655 34.8947 39.6802 35.7023C39.0236 37.5613 37.5613 39.0236 35.7023 39.6802C34.8947 39.9655 33.9669 40.0391 32.6822 40.141C32.6155 40.1463 32.5479 40.1517 32.4792 40.1572C31.8556 40.2069 31.6613 40.2247 31.4821 40.2601C31.0263 40.3501 30.5929 40.5296 30.207 40.7883C30.0552 40.89 29.9053 41.0148 29.4291 41.4206C29.3767 41.4652 29.3251 41.5093 29.2742 41.5527C28.2937 42.389 27.5856 42.993 26.8128 43.3623C25.034 44.2126 22.966 44.2126 21.1872 43.3623C20.4144 42.993 19.7063 42.389 18.7258 41.5526C18.6749 41.5092 18.6233 41.4652 18.5709 41.4206C18.0947 41.0148 17.9448 40.89 17.793 40.7883C17.4071 40.5296 16.9737 40.3501 16.5179 40.2601C16.3387 40.2247 16.1444 40.2069 15.5208 40.1572C15.4521 40.1517 15.3845 40.1463 15.3178 40.141C14.0331 40.0391 13.1053 39.9655 12.2977 39.6802C10.4387 39.0236 8.97644 37.5613 8.31981 35.7023C8.03455 34.8947 7.96093 33.9669 7.85899 32.6822C7.8537 32.6155 7.84833 32.5479 7.84285 32.4792C7.79309 31.8556 7.77528 31.6613 7.7399 31.4821C7.64991 31.0263 7.47038 30.5929 7.2117 30.207C7.11 30.0552 6.98524 29.9053 6.57945 29.4291C6.53479 29.3767 6.49076 29.3251 6.44735 29.2742C5.61103 28.2937 5.00703 27.5856 4.63766 26.8128C3.78745 25.034 3.78745 22.966 4.63766 21.1872C5.00703 20.4144 5.61102 19.7063 6.44734 18.7258C6.49075 18.6749 6.53479 18.6233 6.57945 18.5709C6.98524 18.0947 7.11 17.9448 7.2117 17.793C7.47038 17.4071 7.64991 16.9737 7.7399 16.5179C7.77528 16.3387 7.79309 16.1444 7.84285 15.5208C7.84833 15.4521 7.8537 15.3845 7.85899 15.3178C7.96093 14.0331 8.03455 13.1053 8.31981 12.2977C8.97644 10.4387 10.4387 8.97644 12.2977 8.31981C13.1053 8.03455 14.0331 7.96093 15.3178 7.85899C15.3845 7.8537 15.4521 7.84833 15.5208 7.84285C16.1444 7.79309 16.3387 7.77528 16.5179 7.7399C16.9737 7.64991 17.4071 7.47038 17.793 7.2117C17.9448 7.11 18.0947 6.98524 18.5709 6.57945C18.6233 6.53479 18.6749 6.49075 18.7258 6.44734C19.7063 5.61102 20.4144 5.00703 21.1872 4.63766ZM25.5146 7.35379C24.5568 6.89598 23.4432 6.89598 22.4854 7.35379C22.1151 7.53079 21.7248 7.84703 20.5235 8.87074C20.5038 8.88755 20.4843 8.90414 20.4651 8.92052C20.0709 9.25661 19.7831 9.50194 19.4691 9.71239C18.7524 10.1928 17.9475 10.5262 17.101 10.6933C16.7301 10.7665 16.3531 10.7965 15.8367 10.8377C15.8116 10.8397 15.7861 10.8417 15.7602 10.8437C14.187 10.9693 13.6873 11.0217 13.3003 11.1584C12.2993 11.5119 11.5119 12.2993 11.1584 13.3003C11.0217 13.6873 10.9693 14.187 10.8437 15.7602C10.8417 15.7861 10.8397 15.8116 10.8377 15.8367C10.7965 16.3531 10.7665 16.7301 10.6933 17.101C10.5262 17.9475 10.1928 18.7524 9.71239 19.4691C9.50194 19.7831 9.2566 20.0709 8.92051 20.4651C8.90414 20.4843 8.88755 20.5038 8.87074 20.5235C7.84703 21.7248 7.53079 22.1151 7.35379 22.4854C6.89598 23.4432 6.89598 24.5568 7.35379 25.5146C7.53079 25.8849 7.84703 26.2752 8.87074 27.4765C8.88755 27.4962 8.90414 27.5157 8.92052 27.5349C9.25661 27.9291 9.50194 28.2169 9.71239 28.5309C10.1928 29.2476 10.5262 30.0525 10.6933 30.899C10.7665 31.2699 10.7965 31.6469 10.8377 32.1633C10.8397 32.1884 10.8417 32.2139 10.8437 32.2398C10.9693 33.813 11.0217 34.3127 11.1584 34.6997C11.5119 35.7007 12.2993 36.4881 13.3003 36.8416C13.6873 36.9783 14.187 37.0307 15.7602 37.1563L15.8367 37.1623C16.3532 37.2035 16.7301 37.2335 17.101 37.3067C17.9475 37.4738 18.7524 37.8072 19.4691 38.2876C19.7831 38.4981 20.0709 38.7434 20.4651 39.0795L20.5235 39.1293C21.7248 40.153 22.1151 40.4692 22.4854 40.6462C23.4432 41.104 24.5568 41.104 25.5146 40.6462C25.8849 40.4692 26.2752 40.153 27.4765 39.1293L27.5349 39.0795C27.9291 38.7434 28.2169 38.4981 28.5309 38.2876C29.2476 37.8072 30.0525 37.4738 30.899 37.3067C31.2699 37.2335 31.6469 37.2035 32.1633 37.1623L32.2398 37.1563C33.813 37.0307 34.3127 36.9783 34.6997 36.8416C35.7007 36.4881 36.4881 35.7007 36.8416 34.6997C36.9783 34.3127 37.0307 33.813 37.1563 32.2398L37.1623 32.1633C37.2035 31.6468 37.2335 31.2699 37.3067 30.899C37.4738 30.0525 37.8072 29.2476 38.2876 28.5309C38.4981 28.2169 38.7434 27.9291 39.0795 27.5349L39.1293 27.4765C40.153 26.2752 40.4692 25.8849 40.6462 25.5146C41.104 24.5568 41.104 23.4432 40.6462 22.4854C40.4692 22.1151 40.153 21.7248 39.1293 20.5235L39.0794 20.4651C38.7434 20.0709 38.4981 19.7831 38.2876 19.4691C37.8072 18.7524 37.4738 17.9475 37.3067 17.101C37.2335 16.7301 37.2035 16.3532 37.1623 15.8367L37.1563 15.7602C37.0307 14.187 36.9783 13.6873 36.8416 13.3003C36.4881 12.2993 35.7007 11.5119 34.6997 11.1584C34.3127 11.0217 33.813 10.9693 32.2398 10.8437C32.2139 10.8417 32.1884 10.8397 32.1633 10.8377C31.6469 10.7965 31.2699 10.7665 30.899 10.6933C30.0525 10.5262 29.2476 10.1928 28.5309 9.71239C28.2169 9.50194 27.9291 9.2566 27.5349 8.92051C27.5157 8.90413 27.4962 8.88755 27.4765 8.87074C26.2752 7.84703 25.8849 7.53079 25.5146 7.35379ZM32.0887 17.9183C32.6765 18.5061 32.6765 19.4591 32.0887 20.047L22.0539 30.0817C21.4661 30.6696 20.513 30.6696 19.9252 30.0817L15.9113 26.0678C15.3235 25.48 15.3235 24.527 15.9113 23.9391C16.4991 23.3513 17.4522 23.3513 18.04 23.9391L20.9896 26.8887L29.96 17.9183C30.5478 17.3304 31.5009 17.3304 32.0887 17.9183Z'
        fillRule='evenodd'
        clipRule='evenodd'
        fill='currentColor'
      />
    </svg>
  );
};

// stroke='currentColor'
// strokeWidth={stroke}
// strokeLinecap='round'
// strokeLinejoin='round'
