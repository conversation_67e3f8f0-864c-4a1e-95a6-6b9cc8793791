# Entity: Review

## Purpose

This entity represents customer **Reviews** for products. It defines the structure of a review, manages review data fetching and submission via API, and provides UI components for displaying ratings and individual reviews.

## Key Data Points (`api/types.ts` - `Review` interface)

*   `id` / `review_id`: Unique identifier for the review.
*   `comment_user_name`: Name of the user who left the review.
*   `rating`: Numerical rating (e.g., 1-5).
*   `comment`: Main text content of the review.
*   `pluses`: Stated advantages.
*   `minuses`: Stated disadvantages.
*   `created_at`: Timestamp of when the review was created.
*   `is_active`: Moderation status.
*   `item`: Associated product (`Product` entity).
*   `my_comment`: Flag indicating if the review belongs to the currently logged-in user.
*   `files` (Optional): Associated images uploaded with the review.

## Structure

*   **`model/types.ts`:** (No specific file, types are in `api/types.ts`) Should ideally define the core `Review` interface here.
*   **`api/reviewApi.ts`:** Defines RTK Query endpoints:
    *   `getReviews`: Query to fetch a list of reviews for a specific product variant ID. Provides tags for list and individual review caching.
    *   `setReview`: Mutation to create a new review. Invalidates list tags.
    *   `getSelfReviews`: Query to fetch reviews left by the currently authenticated user. Provides tags specific to user reviews.
    *   `getSelfReview`: Query to fetch a specific review left by the current user.
    *   `updateSelfReview`: Mutation to update an existing review written by the current user. Invalidates relevant tags.
    *   `deleteSelfReview`: Mutation to delete a review written by the current user. Invalidates relevant tags.
*   **`api/types.ts`:** Defines request (`ReviewSetRequest`, `ReviewUpdateRequest`, etc.) and response (`ReviewResponse`, `ReviewDetailResponse`, etc.) types for the review API.
*   **`ui/`:** Contains UI components related to reviews:
    *   `RatingStars.jsx`: Displays star ratings (static or interactive).
    *   `Review/Review.jsx`: Displays a single review card with user details, rating, text, pros/cons, and actions.
    *   `Review/ReviewActions.tsx`: Displays edit/delete actions for reviews owned by the current user.
    *   `ReviewModal.tsx`: The modal form used for creating or editing a review (interacts with `features/modals`).
*   **`lib/hooks/useHasUserReview.ts`:** Custom hook to efficiently check if the current user has already reviewed a specific product.
*   **`index.ts`:** Public API for the entity.

## Usage

*   **Displaying Reviews:** Product pages (`pages/ProductPage`, `components/ProductPage/ProductTabs/ReviewsTab.jsx`, `components/ProductPage/Mobile/MobileProductInfo/MobileReviews.jsx`) use `useGetReviewsQuery` to fetch and display reviews using the `Review` component. Average ratings are displayed using `RatingStars`.
*   **Creating/Editing Reviews:** The "Leave Review" button triggers the `ReviewModal` (via `features/modals`). The modal uses `useSetReviewMutation` or `useUpdateSelfReviewMutation` upon form submission.
*   **User Profile:** The `pages/Profile/UserReviews.tsx` page uses `useGetSelfReviewsQuery` to display a list of reviews authored by the logged-in user.

## Related Entities/Features

*   `entities/product`: Reviews are associated with specific product variants.
*   `features/modals`: Used to display the `ReviewModal`.
*   `entities/user`: User authentication determines if a user can leave/edit/delete reviews.

## Migration Notes

*   Consider moving the `Review` and `RatingStars` components fully into the `entities/review/ui` directory if they are intended as the canonical representation. `ReviewActions` could be part of `Review.jsx` or a separate component within `ui`.
*   The `ReviewModal` interacts heavily with the `modals` feature but conceptually belongs to the review entity/feature logic. Its current location in `entities/review/ui` is acceptable, though it could also be argued to belong in `features/review/ui` if a dedicated feature slice existed.
