import { useGetCategoryTreeQuery } from '@/entities/category';
import { useGetFiltersMutation, useGetFiltersNewMutation } from '@/entities/filter';
import { useGetVariantsMutation } from '@/entities/product';
import { scrollToTop } from '@/shared/lib';
import { FiltersState } from '@/types/Filters/FiltersState';
import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

export const useCatalog = () => {
  const [filtersModalOpen, setFiltersModalOpen] = useState(false);

  const { categoryId } = useParams();

  const navigate = useNavigate();
  const location = useLocation();

  // Fetch category tree data for the selected category
  const {
    data: categoryTree,
    isSuccess: categoryTreeIsSuccess,
    isLoading: categoryTreeIsLoading
  } = useGetCategoryTreeQuery(categoryId);

  // ===== FILTERS STATE MANAGEMENT =====

  /**
   * Main filters state holding all available filters
   * - basics: Core filters like price, tags, brands
   * - dynamics: Dynamic attribute filters specific to category
   * - more: Additional filters that are toggled in "All Filters" modal
   * - category_chain: Hierarchy path for the current category
   */
  const [filters, setFilters] = useState<FiltersState>({
    basics: {
      price: {
        min: 0,
        max: 0
      },
      tags: [],
      brands: [],
      rating: []
    },
    dynamics: [],
    more: [],
    category_chain: []
  });

  // Loading states for filters
  const [filtersLoading, setFiltersLoading] = useState(false);
  const [filtersBlock, setFiltersBlock] = useState(false);

  /**
   * Trigger for filter updates that controls special behavior:
   * - Prevents redundant API calls on component mount
   * - Controls price filter initialization behavior
   * - Manages category change flow
   */
  const [trigger, setTrigger] = useState('');

  // Reference to previous filters state for comparison
  const previousFilters = useRef({});

  // References for API request cancellation
  const getFiltersRef = useRef(null);
  const getProductsRef = useRef(null);

  // Get filter mutation hook from RTK Query
  const [getFilters] = useGetFiltersMutation();

  const [getNewFilters] = useGetFiltersNewMutation();

  useEffect(() => {
    getNewFilters({
      category_id: categoryId,
      filters: filters
    });
  }, [categoryId]);

  /**
   * Fetches new filter options based on current selection
   * This updates the available filter options based on current filter state
   *
   * @param {Object} sendObject - Filter parameters to send to API
   * @param {string} trigger - Action that triggered the filter update
   */
  const getNewFiltersList = async (sendObject, trigger) => {
    // Cancel any in-flight request
    if (getFiltersRef.current) {
      getFiltersRef.current.abort();
    }

    // Create new abort controller
    const abortController = new AbortController();
    getFiltersRef.current = abortController;

    // Set appropriate loading state based on trigger type
    if (trigger === 'categoryId') {
      setFiltersLoading(true);
    } else if (trigger === 'filters') {
      setFiltersBlock(true);
    }

    try {
      // Call the filter API endpoint with abort signal
      const newFilters = await getFilters({
        ...sendObject,
        signal: abortController.signal
      });

      // Type guard to check if response has data
      if ('data' in newFilters) {
        // Process "more" filters by marking them as additional
        const more = newFilters.data.more.map((obj) => ({
          ...obj,
          additional_filter: true
        }));

        // Combine regular dynamic filters with "more" filters
        const newDynamics = newFilters.data.dynamics.concat(more);

        // Create the updated filters state
        const newFiltersState = {
          ...filters,
          basics: newFilters.data.basics,
          dynamics: newDynamics
        };

        // Update URL with new filter parameters
        const queryParams = buildQueryParams(getSendFiltersObject2(newFiltersState), sort, page);

        navigate(`?${queryParams}`, { replace: true });

        // Update references and state
        previousFilters.current = newFiltersState;

        setFilters(newFiltersState);

        setTrigger(trigger);
      } else {
        console.error('Filters API returned unexpected format:', newFilters);
      }
    } catch (error) {
      // Only log error if it's not an abort error
      if (error.name !== 'AbortError') {
        console.error('Failed to fetch filters:', error);
      } else {
        console.log('Filters request was aborted');
      }
    } finally {
      // Reset loading states
      if (trigger === 'categoryId') {
        console.log('Resetting filtersLoading to false');
        setFiltersLoading(false);
      } else if (trigger === 'filters') {
        console.log('Resetting filtersBlock to false');
        setFiltersBlock(false);
      }

      // Clear reference if this controller is still the current one
      if (getFiltersRef.current === abortController) {
        getFiltersRef.current = null;
      }
    }
  };

  // ===== PRODUCTS STATE MANAGEMENT =====

  // Get products mutation hook from RTK Query
  const [getVariants] = useGetVariantsMutation();

  // Products state and loading indicator
  const [products, setProducts] = useState([]);
  const [productsLoading, setProductsLoading] = useState(false);

  /**
   * Fetches products based on current filters, sort, and pagination
   * Implements request cancellation to prevent race conditions
   *
   * @param {Object} sendObject - Parameters to send to API (filters, sort, pagination)
   */
  const getProducts = async (sendObject) => {
    // Cancel any in-flight request
    if (getProductsRef.current) {
      getProductsRef.current.abort();
    }

    // Create new abort controller
    const abortController = new AbortController();
    getProductsRef.current = abortController;

    setProductsLoading(true);

    try {
      const productsResponse = await getVariants({
        ...sendObject,
        signal: abortController.signal
      });

      if (productsResponse.data?.success === 'ok') {
        setProducts(productsResponse.data);
      } else {
        console.error('Products API returned unexpected format:', productsResponse);
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Error fetching products:', error);
      } else {
        console.log('Products request was aborted');
      }
    } finally {
      console.log('Setting productsLoading to false');
      setProductsLoading(false);

      if (getProductsRef.current === abortController) {
        console.log('Clearing getProductsRef');
        getProductsRef.current = null;
      }
    }
  };

  /**
   * Flag to track first load for URL parameter handling
   * Used to determine if we should use URL parameters on initial load
   */
  const isFirstLoad = useRef(true);

  // ===== FILTER CHANGE EFFECT =====

  /**
   * Effect that triggers when filters change
   * Updates both available filters and product list
   */
  useEffect(() => {
    if (isFirstLoad.current) {
      console.log('First load detected, skipping filters effect');
      isFirstLoad.current = false;
      return;
    }

    // Compare with previous filters
    const currentFiltersJson = JSON.stringify(filters);
    const previousFiltersJson = JSON.stringify(previousFilters.current);
    const hasChanged = currentFiltersJson !== previousFiltersJson;

    // Only trigger API calls if filters actually changed
    if (hasChanged) {
      const sendObject = getSendFiltersObject();

      // Fetch updated filter options
      getNewFiltersList(
        {
          ...sendObject
          // Additional params commented out but logged for debugging
          // min_rating, orderBy, sortOrder
        },
        'filters'
      );

      // Fetch products with updated filters
      getProducts({
        ...sendObject,
        page: 1,
        limit: 20,
        orderBy: sort.sortBy || 'popularity',
        sortOrder: sort.sortOrder || 'desc'
      });
    } else {
      console.log('Filters have not changed, skipping API calls');
    }
  }, [filters]);

  // ===== CATEGORY CHANGE EFFECT =====

  /**
   * Effect that triggers when category changes
   * Resets filters and loads new category data
   */
  useEffect(() => {
    // Parse URL query parameters
    const queryParams = parseQueryParams(location.search);

    // If it's the first load and we have query parameters, use them
    if (isFirstLoad.current && queryParams) {
      getNewFiltersList({ ...queryParams.filtersObject, category_id: categoryId }, 'categoryId');

      setPage(queryParams.page || 1);

      getProducts({
        ...queryParams.filtersObject,
        orderBy: queryParams.sortObject.sortBy || 'popularity',
        sortOrder: queryParams.sortObject.sortOrder || 'desc',
        page: queryParams.page || 1,
        limit: 20,
        category_id: categoryId
      });

      isFirstLoad.current = false;
    } else {
      getNewFiltersList(
        {
          category_id: categoryId,
          min_price: null,
          max_price: null,
          brands: [],
          tags: [],
          filters: {},
          last_changed: {}
        },
        'categoryId'
      );

      getProducts({
        page: 1,
        limit: 20,
        orderBy: sort.sortBy,
        sortOrder: sort.sortOrder,
        category_id: categoryId,
        min_price: null,
        max_price: null,
        brands: [],
        tags: [],
        filters: {},
        last_changed: {}
      });

      setPage(1);
    }

    scrollToTop();
  }, [categoryId]);

  // ===== URL PARAMS CHANGE EFFECT =====

  /**
   * Effect to handle URL parameter changes
   */
  useEffect(() => {
    if (isFirstLoad.current) {
      return;
    }

    // Add additional URL parameter handling if needed
  }, [location.search]);

  // ===== FILTER RESET FUNCTIONALITY =====

  /**
   * Resets all filters to their default values
   * Triggered by "Clear filter" button
   */
  const resetFilters = async () => {
    getNewFiltersList(
      {
        category_id: categoryId,
        min_price: null,
        max_price: null,
        brands: [],
        tags: [],
        filters: {},
        last_changed: {}
      },
      'categoryId'
    );

    getProducts({
      page: page,
      limit: 20,
      orderBy: sort.sortBy,
      sortOrder: sort.sortOrder,
      category_id: categoryId,
      min_price: null,
      max_price: null,
      brands: [],
      tags: [],
      filters: {},
      last_changed: {}
    });

    scrollToTop();
  };

  // ===== PAGINATION STATE =====

  // Current page state
  const [page, setPage] = useState(1);

  /**
   * Handles pagination changes
   * Updates page state and fetches new product page
   *
   * @param {Event} e - Event object (unused)
   * @param {number} p - New page number
   */
  const handlePagination = (e, p) => {
    console.log('Changing page from', page, 'to', p);

    setPage(p);

    getProducts({
      ...getSendFiltersObject(),
      page: p,
      limit: 20,
      orderBy: sort.sortBy,
      sortOrder: sort.sortOrder
    });

    scrollToTop();
  };

  // ===== SORTING STATE =====

  // Sort state with default sorting by popularity descending
  const [sort, setSort] = useState({
    sortBy: 'popularity',
    sortOrder: 'desc'
  });

  // Reference to previous sort state for comparison
  const sortPrevious = useRef(sort);

  /**
   * Effect that triggers when sort changes
   * Fetches new product list with updated sort parameters
   */
  useEffect(() => {
    const currentSortJson = JSON.stringify(sort);
    const previousSortJson = JSON.stringify(sortPrevious.current);
    const hasChanged = currentSortJson !== previousSortJson;

    if (hasChanged) {
      getProducts({
        ...getSendFiltersObject(),
        page: page,
        limit: 20,
        orderBy: sort.sortBy,
        sortOrder: sort.sortOrder
      });

      sortPrevious.current = sort;
    } else {
      console.log('Sort has not changed, skipping API call');
    }
  }, [sort]);

  // ===== UTILITY FUNCTIONS =====

  /**
   * Transforms filters state into an object for API requests
   * Extracts selected filters from the complete filters state
   *
   * @param {FiltersState} filters - Filters state object
   * @returns {Object} Object formatted for API request
   */
  const getSendFiltersObject2 = (filters) => {
    const brands = filters?.basics?.brands?.reduce((acc, brand) => {
      if (brand.is_selected) {
        acc.push(brand.id);
      }
      return acc;
    }, []);

    // Extract selected tags
    const tags = filters?.basics?.tags?.reduce((acc, tag) => {
      if (tag.is_selected) {
        acc.push(tag.tag);
      }
      return acc;
    }, []);

    // Extract selected dynamic filters
    const dynamicFilters = filters?.dynamics
      ?.filter((filter) => filter.values.some((value) => value.is_selected))
      .reduce((acc, filter) => {
        acc[filter.id] = filter.values
          .filter((value) => value.is_selected)
          .map((value) => value.id);
        return acc;
      }, {});

    // Create the final request object
    const result = {
      category_id: categoryId,
      min_price: filters?.basics?.price?.current_values?.min || null,
      max_price: filters?.basics?.price?.current_values?.max || null,
      brands: brands || [],
      tags: tags || [],
      filters: dynamicFilters || {},
      last_changed: filters?.lastChanged || {}
    };

    return result;
  };

  /**
   * Transforms the current filters state into an object for API requests
   * Similar to getSendFiltersObject2 but uses the component state directly
   *
   * @returns {Object} Object formatted for API request
   */
  const getSendFiltersObject = () => {
    const brands = filters?.basics?.brands?.reduce((acc, brand) => {
      if (brand.is_selected) {
        acc.push(brand.id);
      }
      return acc;
    }, []);

    const tags = filters?.basics?.tags?.reduce((acc, tag) => {
      if (tag.is_selected) {
        acc.push(tag.tag);
      }
      return acc;
    }, []);

    const dynamicFilters = filters?.dynamics
      ?.filter((filter) => filter.values.some((value) => value.is_selected))
      .reduce((acc, filter) => {
        acc[filter.id] = filter.values
          .filter((value) => value.is_selected)
          .map((value) => value.id);
        return acc;
      }, {});

    const result = {
      category_id: categoryId,
      min_price: filters?.basics?.price?.current_values?.min || null,
      max_price: filters?.basics?.price?.current_values?.max || null,
      brands: brands || [],
      tags: tags || [],
      filters: dynamicFilters || {},
      last_changed: filters?.lastChanged || {}
    };

    return result;
  };

  /**
   * Parses URL query parameters into filter, sort, and pagination objects
   *
   * @param {string} queryString - URL query string
   * @returns {Object} Object containing parsed parameters
   */
  const parseQueryParams = (queryString) => {
    const params = new URLSearchParams(queryString);
    const filtersObject = {
      min_price: null,
      max_price: null,
      brands: [],
      tags: [],
      filters: {},
      last_changed: {}
    };
    const sortObject = {
      sortBy: undefined,
      sortOrder: undefined
    };
    let page = undefined;

    // Parse min_price and max_price
    if (params.has('min_price')) {
      filtersObject.min_price =
        params.get('min_price') === 'null' ? null : parseInt(params.get('min_price'), 10);
    }
    if (params.has('max_price')) {
      filtersObject.max_price =
        params.get('max_price') === 'null' ? null : parseInt(params.get('max_price'), 10);
    }

    // Parse brands
    if (params.has('brands')) {
      filtersObject.brands = params.get('brands').split(',').map(Number);
    }

    // Parse tags
    if (params.has('tags')) {
      filtersObject.tags = params.get('tags').split(',');
    }

    // Parse dynamic filters (filter_*)
    params.forEach((value, key) => {
      if (key.startsWith('filter_')) {
        const filterId = key.replace('filter_', '');
        filtersObject.filters[filterId] = value.split(',').map(Number);
      }
    });

    // Parse last_changed
    if (params.has('last_changed_type')) {
      filtersObject.last_changed.type = params.get('last_changed_type');
    }
    if (params.has('last_changed_filter')) {
      filtersObject.last_changed.filter = parseInt(params.get('last_changed_filter'), 10);
    }

    // Parse sortBy and sortOrder
    if (params.has('sort_by')) {
      sortObject.sortBy = params.get('sort_by');
    }
    if (params.has('sort_order')) {
      sortObject.sortOrder = params.get('sort_order');
    }

    // Parse page
    if (params.has('page')) {
      page = parseInt(params.get('page'), 10);
    }

    const result = {
      filtersObject,
      sortObject,
      page
    };

    return result;
  };

  /**
   * Builds URL query parameters from filter, sort, and pagination objects
   *
   * @param {Object} filtersObject - Filter parameters object
   * @param {Object} sortObject - Sort parameters object
   * @param {number} page - Current page number
   * @returns {string} URL query string
   */
  const buildQueryParams = (filtersObject, sortObject, page) => {
    const params = new URLSearchParams();

    // Price range
    if (filtersObject.min_price !== undefined) {
      params.set('min_price', filtersObject.min_price);
    }
    if (filtersObject.max_price !== undefined) {
      params.set('max_price', filtersObject.max_price);
    }

    // Brands
    if (filtersObject.brands?.length) {
      params.set('brands', filtersObject.brands.join(','));
    }

    // Tags
    if (filtersObject.tags?.length) {
      params.set('tags', filtersObject.tags.join(','));
    }

    // Dynamic Filters
    if (filtersObject.filters && Object.keys(filtersObject.filters).length > 0) {
      for (const key in filtersObject.filters) {
        params.set(`filter_${key}`, filtersObject.filters[key].join(','));
      }
    }

    // Last Changed
    if (filtersObject.last_changed.filter !== undefined) {
      params.set('last_changed_type', filtersObject.last_changed.type);
      params.set('last_changed_filter', filtersObject.last_changed.filter);
    }

    // Sort parameters
    if (sortObject.sortBy !== undefined) {
      params.set('sort_by', sortObject.sortBy);
    }
    if (sortObject.sortOrder !== undefined) {
      params.set('sort_order', sortObject.sortOrder);
    }

    // Pagination
    if (page) {
      params.set('page', page);
    }

    const result = params.toString();
    return result;
  };

  // Clean up any pending requests when component unmounts
  useEffect(() => {
    return () => {
      if (getFiltersRef.current) {
        getFiltersRef.current.abort();
      }
      if (getProductsRef.current) {
        getProductsRef.current.abort();
      }
    };
  }, []);

  return {
    categoryTreeIsLoading,
    categoryTreeIsSuccess,
    categoryTree,
    setFiltersModalOpen,
    filters,
    setFilters,
    setTrigger,
    resetFilters,
    filtersLoading,
    filtersBlock,
    products,
    productsLoading,
    page,
    handlePagination,
    sort,
    setSort,
    filtersModalOpen,
    trigger
  };
};
