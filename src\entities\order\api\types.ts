import type { AdditionalServerResponseData } from '@/shared/types/AdditionalServerResponseData';

/*
export interface Order {
  // Define order properties here
}
*/

export interface VariantPay {
  name: string;
  code: string;
  is_default: number; // 0 or 1
}

export interface CompanyInfo {
  id: number;
  legal_name: string;
  short_name: string;
  inn: string;
  kpp: string;
  ogrn: string;
  variants_pay: VariantPay[];
  // Add other company_info fields from JSON as needed
}

export interface ApiOrderItemPrice {
  base: number;
  final: number;
  discount: number | null;
  unit: string;
  currency: {
    code: string;
    title: string;
    symbol: string;
  };
  total: number;
}

/**
 * Represents a delivery point for an order item
 */
export interface DeliveryPoint {
  /** Unique identifier for the pickup point */
  uuid: string;
  /** Name of the pickup point */
  name: string;
  /** Region/state/province where the pickup point is located */
  region: string;
  /** City where the pickup point is located */
  city: string;
  /** Full address of the pickup point */
  address: string;
  /** Latitude coordinate for map display */
  lat: string;
  /** Longitude coordinate for map display */
  lon: string;
}

/**
 * Represents delivery information for an order item
 */
export interface DeliveryInfo {
  /** Type of delivery (pickup, courier, etc.) */
  type: 'pickup' | 'courier' | string;
  /** Pickup point information if type is 'pickup' */
  point?: DeliveryPoint;
  /** Address information if type is 'courier' */
  address?: string;
}

export interface ApiOrderItem {
  id: number;
  groupId: number;
  sku: string;
  article: number | string;
  slug: string;
  name: string;
  groupName: string;
  fullName: string;
  // availability: any; // Define if needed
  // description: string;
  files: any[]; // Define file structure if needed
  category: {
    id: number;
    name: string;
    slug: string;
  };
  price: ApiOrderItemPrice;
  // tags: any[]; // Define if needed
  // brand?: any; // Define if needed
  // attributes: any[]; // Define if needed
  quantity: number | null; // Quantity of this item in the order
  /** Delivery information for this item */
  delivery?: DeliveryInfo;
  // status: any; // Define if needed
}

export interface OrderDetailsData {
  order_number: string;
  date: string;
  status: {
    id: number;
    name: string;
    background_color: string;
    text_color: string;
  };
  items: ApiOrderItem[];
  company_info: CompanyInfo;
  total: {
    amount: number;
    quantity: number;
    discount: number;
  };
}

export interface GetOrderResponse extends AdditionalServerResponseData {
  data: OrderDetailsData;
}

export interface OrderRequest {
  // Define order request properties here
}

export interface FeedbackRequest {
  // Define feedback request properties here
}

export interface FeedbackResponse {
  // Define feedback response properties here
}

export interface GetUserOrdersResponse extends AdditionalServerResponseData {
  data: OrderDetailsData[]; // Assuming GetUserOrdersResponse returns a list of full order details
}

// Type for the orderPayment mutation (OLD - needs review based on new payment flow)
export interface OrderPaymentRequest {
  order_number: string[];
  payment_id: number; // This ID might no longer be relevant with 'code' based payment methods
}

export interface OrderPaymentResponseData {
  bill?: {
    link?: string;
    // other bill properties
  };
  // other properties for each order in the response
}

export interface OrderPaymentResponse extends AdditionalServerResponseData {
  data?: OrderPaymentResponseData[];
}

export interface CheckoutItemDelRequest {
  order_number: string;
  item_id: number;
}

export interface CheckoutItemReturnCardRequest {
  order_number: string;
  item_id: number;
}
