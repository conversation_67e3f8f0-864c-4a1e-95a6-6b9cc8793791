# Refactoring & Code Cleanup Plan

This document outlines the plan for migrating the Rostok Frontend codebase towards a cleaner, more maintainable structure based on **Feature-Sliced Design (FSD)**.

## Goal

The primary goal is to improve code organization, reduce coupling, enhance testability, and make the codebase easier to understand and navigate for both current and future developers (and LLMs). This involves moving existing code from legacy locations into the appropriate FSD layers and slices.

## Legacy Directories & Files

Code requiring refactoring primarily resides in these locations:

*   **`src/components/`:** This directory contains a large mix of UI components.
    *   **Target:** Move components to `shared/ui` (reusable), `features/*/ui` (feature-specific), `widgets/*/ui` (widget parts), `entities/*/ui` (entity representations), or `pages/*/ui` (page-specific layouts).
    *   **Examples:** `ProductCard` -> `widgets/product-card/ui`, form components -> `features/{feature}/ui` or `shared/ui`, layout elements -> `pages` or `widgets`.
*   **`src/helpers/`:** Contains utility components (e.g., `QuestionForm.tsx`, `ErrorEmpty.jsx`) and potentially helper functions.
    *   **Target:** Move UI helpers to `shared/ui` or feature/widget UI segments. Move utility functions to `shared/lib` or feature/entity `lib` segments.
*   **`src/hooks/`:** Contains top-level custom hooks (e.g., `useModificationAttributesManager.ts`, `useProductAttributes.ts`).
    *   **Target:** Relocate hooks to the `model` or `lib` segment of the FSD slice (feature/entity) they most closely relate to. If truly generic, move to `shared/lib/hooks`.
*   **`src/api/` (Root Level):** Contains direct Axios instance (`axios.js`) and specific API call functions (`searchProducts.js`).
    *   **Target:** Replace these with RTK Query endpoints defined in the `api` segment of the relevant feature or entity slice. The base Axios instance setup (if still needed alongside RTK Query for specific cases) should be part of `shared/api`.
*   **`src/redux/` (Root Level):** Contains some RTK Query endpoint definitions (`contentEndpoints.ts`, `walletEndpoints.ts`).
    *   **Target:** Move these endpoint definitions into the `api` segment of the most relevant entity or feature slice (e.g., content endpoints might relate to multiple entities, wallet endpoints to a potential `wallet` entity/feature). The core API setup (`createApi`) should remain in `shared/api/api.ts`.
*   **`src/types/` (Root Level):** Contains various global type definitions.
    *   **Target:** Co-locate types with the code they define:
        *   Entity models -> `src/entities/{entityName}/model/types.ts` or `src/entities/{entityName}/types.ts`.
        *   Feature-specific types -> `src/features/{featureName}/model/types.ts`.
        *   API request/response types -> `src/entities/{entityName}/api/types.ts` or `src/features/{featureName}/api/types.ts`.
        *   Truly shared, generic types -> `src/shared/types/`.

## Refactoring Strategy

Refactoring will be an ongoing, incremental process integrated with regular development:

1.  **Identify & Analyze:** When working on a feature or fixing a bug, identify any related code located in legacy directories. Understand its purpose, dependencies, and how it fits into the application flow.
2.  **Determine FSD Location:** Choose the most appropriate FSD layer (`pages`, `widgets`, `features`, `entities`, `shared`) and slice (e.g., `product`, `user`, `cart`) for the code. Consider the [FSD Layer descriptions](../architecture/fsd-overview.md).
3.  **Move & Refactor:**
    *   Move the file(s) to the target FSD directory and segment (e.g., `ui`, `model`, `api`, `lib`). Create new directories/files as needed.
    *   Update all import paths in other files that referenced the old location.
    *   Refactor the code to align with FSD principles:
        *   Separate UI presentation from business logic/state management.
        *   Ensure dependencies only flow downwards (e.g., a feature cannot import from a widget).
        *   Use the slice's public API (`index.ts`) for exports.
        *   Replace direct API calls (`axios`, `fetch`) with RTK Query hooks.
        *   Replace legacy state patterns with Redux Toolkit slices or RTK Query state where appropriate.
        *   Update styling to prefer Tailwind/Shadcn UI where practical.
4.  **Update Documentation:** If the refactoring significantly changes how a feature or entity works, update its corresponding README.md (if one exists).
5.  **Testing (Aspirational):** As code is refactored, consider adding unit or integration tests to ensure its functionality and prevent regressions.

## Specific Focus Areas & Priorities

1.  **API Migration:** Convert legacy API calls (`src/api/`, `src/redux/api/`) to RTK Query endpoints first. This yields immediate benefits in caching, loading state management, and code reduction.
2.  **Component Relocation:** Systematically move components from `src/components/` to their correct FSD layers/slices. Start with easily identifiable shared UI (`shared/ui`) or components clearly belonging to a single feature/widget.
3.  **Hook Relocation:** Move custom hooks from `src/hooks/` to the appropriate `model` or `lib` segments.
4.  **Type Co-location:** Move types from `src/types/` alongside the code they describe.
5.  **Helper Migration:** Relocate items from `src/helpers/`.
6.  **Styling Consolidation:** Review MUI usage and standardize towards Tailwind/Shadcn where feasible.

This process should be gradual. Focus on refactoring code related to the specific task or feature being worked on, rather than attempting a large-scale rewrite at once.