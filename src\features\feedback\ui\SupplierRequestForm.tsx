// src/features/feedback/ui/SupplierRequestForm.tsx
import React from 'react';
import { Controller, useForm, SubmitHandler } from 'react-hook-form';

import image from '@/shared/assets/images/want-to-be-partner.png';
import { CPhoneField } from '@/shared/ui/inputs/CPhoneField';
import { CTextField } from '@/shared/ui/inputs/CTextField';

interface ISupplierRequestFormData {
  name: string;
  email: string;
  phone: string;
  comment: string;
}

const SupplierRequestForm: React.FC = () => {
  const {
    control,
    handleSubmit,
    reset,
    formState: { isValid },
  } = useForm<ISupplierRequestFormData>({
    mode: 'onChange',
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      comment: '',
    },
  });

  const onSubmit: SubmitHandler<ISupplierRequestFormData> = async (data) => {
    // TODO: Implement actual submission logic (e.g., API call)
    reset();
  };

  const InputProps = {
    sx: {
      '& .MuiInputBase-input::placeholder': { color: 'white', opacity: 1 },
      '&::placeholder': { color: 'white', opacity: 1 },
      '& .MuiOutlinedInput-notchedOutline': { borderWidth: '1px', borderColor: '#FFF' },
      '&:hover .MuiOutlinedInput-notchedOutline': { borderWidth: '1px', borderColor: '#FFF' },
      '&.Mui-focused .MuiOutlinedInput-notchedOutline': { borderColor: '#FFF', borderWidth: '1px' },
      '.css-1n4twyu-MuiInputBase-input-MuiOutlinedInput-input': { paddingRight: '14px' },
      color: 'white',
    },
  };

  const InputLabelProps = {
    sx: {
      '&.Mui-focused': { color: '#FFF' },
      color: '#FFF',
    },
  };

  return (
    <div className='flex flex-col md:flex-row gap-5 my-10 md:my-[70px] px-4 md:px-0'>
      <div className='w-full md:basis-[calc(30%-20px/2)] rounded-2xl bg-colGreen p-5 md:p-7'>
        <div className='text-white text-2xl md:text-4xl font-semibold mb-2 md:mb-3'>Хотите стать поставщиком?</div>
        <div className='text-white text-sm md:text-base mb-3'>Оставьте заявку и менеджер свяжется с вами в ближайшее время, чтобы обсудить все условия</div>
        <form onSubmit={handleSubmit(onSubmit)} className='flex flex-col gap-3'>
          <Controller
            name='name'
            control={control}
            rules={{ required: 'Поле Имя обязательно!' }}
            render={({ field, fieldState: { error } }) => (
              <CTextField
                label='Имя'
                type='text'
                required={true}
                {...field}
                error={!!error}
                helperText={error?.message}
                InputProps={InputProps}
                InputLabelProps={InputLabelProps}
                size="small"
              />
            )}
          />
          <Controller
            name='email'
            control={control}
            rules={{
              required: 'Поле Email обязательно!',
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Введите корректный email',
              },
            }}
            render={({ field, fieldState: { error } }) => (
              <CTextField
                label='Электронная почта'
                type='email'
                required={true}
                {...field}
                error={!!error}
                helperText={error?.message}
                InputProps={InputProps}
                InputLabelProps={InputLabelProps}
                size="small"
              />
            )}
          />
          <Controller
            name='phone'
            control={control}
            rules={{
              required: 'Поле Телефон обязательно!',
              pattern: {
                value: /^(\+?\d{1,3}[\s-]?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/,
                message: 'Введите корректный номер телефона',
              },
            }}
            render={({ field, fieldState: { error } }) => (
              <CPhoneField
                label='Телефон'
                {...field}
                required={true}
                error={!!error}
                helperText={error?.message}
                InputProps={InputProps}
                InputLabelProps={InputLabelProps}
                size="small"
              />
            )}
          />
          <Controller
            name='comment'
            control={control}
            render={({ field }) => (
              <CTextField
                multiline
                minRows={3}
                label='Комментарий'
                type='text'
                {...field}
                InputProps={InputProps}
                InputLabelProps={InputLabelProps}
                size="small"
              />
            )}
          />
          <button
            type='submit'
            disabled={!isValid}
            className='bg-white rounded p-3 text-colGreen font-semibold text-sm md:text-base hover:bg-opacity-90 transition-all disabled:opacity-50 disabled:cursor-not-allowed'
          >
            Оставить заявку
          </button>
        </form>
      </div>
      <div className='hidden md:block md:basis-[calc(70%-20px/2)] rounded-2xl overflow-hidden'>
        <img src={image} alt="Стать поставщиком Furnica" className='w-full h-full object-cover' />
      </div>
    </div>
  );
};

export default SupplierRequestForm;
