import React, { useState, useEffect } from 'react';

import { FormProvider, useForm } from 'react-hook-form';
import { NavLink, useNavigate, useLocation } from 'react-router-dom';
import { YMaps, Map, Placemark } from '@pbe/react-yandex-maps';
import { toast } from 'sonner';

import { useSendOrderMutation, useOrderPaymentMutation } from '@/entities/order';
import { useAuthContext, useGetUserDataQuery } from '@/entities/user';
import { removeFromCart, useSendCartMutation } from '@/features/cart';
import { useModal } from '@/features/modals/model/context';

import arrow from '@/shared/assets/icons/arrow-icon.svg';
import markerIcon from '@/shared/assets/icons/address.svg';
import { CTextField } from '@/shared/ui/inputs/CTextField';

const PENDING_ORDER_KEY = 'rostok_pending_order';
const ORDER_EXPIRY_TIME = 30 * 60 * 1000; // 30 minutes in milliseconds

const CartCheckout = () => {
  const { isAuthenticated } = useAuthContext();
  const { showModal } = useModal();
  const location = useLocation();
  const navigate = useNavigate();

  const [orderData, setOrderData] = useState(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('1'); // Default to card payment
  const [loading, setLoading] = useState(true);
  const [processingPendingOrder, setProcessingPendingOrder] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: user } = useGetUserDataQuery();
  const [sendCart] = useSendCartMutation();
  const [sendOrder, { isLoading: sendOrderIsLoading }] = useSendOrderMutation();
  const [orderPayment, { isLoading: orderPaymentIsLoading }] = useOrderPaymentMutation();

  const methods = useForm({
    mode: 'onChange',
    defaultValues: {
      name: user?.name || '',
      phone: user?.phone || '',
      email: user?.email || '',
      comment: ''
    }
  });

  // Update form defaults when user data loads
  useEffect(() => {
    if (user) {
      methods.reset({
        name: user.name || '',
        phone: user.phone || '',
        email: user.email || '',
        comment: methods.getValues('comment') || ''
      });
    }
  }, [user, methods]);

  // Fetch order data upon mounting
  useEffect(() => {
    const fetchOrderData = async () => {
      try {
        setLoading(true);
        // Access the JSON data from localStorage that was stored during cart checkout navigation
        const orderDataString = localStorage.getItem('checkout_order_data');

        if (orderDataString) {
          const parsedData = JSON.parse(orderDataString);
          setOrderData(parsedData);
          console.log('[Checkout] Loaded order data:', parsedData);
        } else {
          console.error('[Checkout] No order data found in localStorage');
          // Redirect back to cart if no data
          navigate('/shopping-cart');
        }
      } catch (error) {
        console.error('[Checkout] Error loading order data:', error);
        toast.error('Ошибка загрузки данных заказа');
        navigate('/shopping-cart');
      } finally {
        setLoading(false);
      }
    };

    fetchOrderData();
  }, [navigate]);

  // Process any pending orders when authentication changes
  useEffect(() => {
    const processPendingOrder = async () => {
      if (isAuthenticated) {
        try {
          const pendingOrderString = localStorage.getItem(PENDING_ORDER_KEY);
          if (pendingOrderString) {
            const pendingOrderData = JSON.parse(pendingOrderString);

            // Check if the order has expired
            const currentTime = new Date().getTime();
            if (currentTime - pendingOrderData.timestamp > ORDER_EXPIRY_TIME) {
              console.log('[Checkout] Pending order expired, removing');
              localStorage.removeItem(PENDING_ORDER_KEY);
              return;
            }

            // Set processing flag to show loading state
            setProcessingPendingOrder(true);

            // Clear the pending order from localStorage
            localStorage.removeItem(PENDING_ORDER_KEY);

            // Extract the actual order data
            const orderPayloadData = pendingOrderData.orderData;

            // Submit the order
            console.log('[Checkout] Submitting pending order:', orderPayloadData);
            try {
              const response = await orderPayment(orderPayloadData).unwrap();

              console.log('[Checkout] Order response:', response);
              if (response.success === true || response.success === 'ok') {
                await handleOrderSuccess(response);
              } else {
                console.error('[Checkout] Pending order was not successful:', response);
                toast.error('Ошибка при оформлении заказа');
              }
            } catch (error) {
              console.error('[Checkout] Error sending pending order:', error);
              toast.error('Ошибка при оформлении заказа');
            }
          }
        } catch (error) {
          console.error('[Checkout] Error processing pending order:', error);
        } finally {
          setProcessingPendingOrder(false);
        }
      }
    };

    processPendingOrder();
  }, [isAuthenticated, orderPayment]);

  /**
   * Handle navigation based on the order response
   */
  const handleOrderSuccess = async (response) => {
    console.log('[Checkout] Order successful, removing ordered items...');
    toast.success('Заказ успешно оформлен!');

    // Check if there's a payment link to navigate to
    if (response.bill && response.bill.link) {
      console.log('[Checkout] Navigating to payment page:', response.bill.link);

      // Check if it should open in a new tab
      if (response.bill.link_opening_location === 'new_tab') {
        window.open(response.bill.link, '_blank');
        // Navigate to orders page in current tab
        navigate('/profile/orders');
      } else {
        // Navigate to the payment page in the current tab
        window.location.href = response.bill.link;
      }
    } else {
      // No payment link, navigate to orders page
      console.log('[Checkout] Navigating to orders page...');
      // Delay navigation slightly so the success toast can be seen
      setTimeout(() => {
        navigate('/profile/orders');
      }, 1500);
    }
  };

  // Directly handle the submit button click
  const handleSubmitOrder = async () => {
    try {
      // Manual validation check
      const formData = methods.getValues();
      console.log("Form data:", formData);

      // Basic validation
      if (!formData.name || !formData.phone || !formData.email) {
        toast.error('Пожалуйста, заполните все обязательные поля');

        // Trigger validation errors to show in the form
        methods.trigger();
        return;
      }

      if (!orderData) {
        toast.error('Данные заказа не загружены');
        return;
      }

      setIsSubmitting(true);

      // Get all order numbers from the orderData
      const orderNumbers = orderData.data.map(order => order.order_number);

      // Prepare order payload with just order numbers and payment method
      const orderPayloadData = {
        order_number: orderNumbers,
        payment_id: Number(selectedPaymentMethod)
      };

      console.log("Sending order payment with data:", orderPayloadData);

      // If user is not authenticated, store order data and show auth modal
      if (!isAuthenticated) {
        console.log('[Checkout] User not authenticated, storing order and showing auth modal');

        // Store the pending order data with timestamp
        const pendingOrderData = {
          orderData: orderPayloadData,
          timestamp: new Date().getTime()
        };

        localStorage.setItem(PENDING_ORDER_KEY, JSON.stringify(pendingOrderData));

        // Show the auth modal
        showModal({
          type: 'auth',
          content: 'checkAuth',
          from: location,
          redirect: '/checkout' // This will redirect back to checkout page after auth
        });

        // Exit early - don't proceed with order submission yet
        setIsSubmitting(false);
        return;
      }

      // Direct API call using fetch for maximum control and visibility
      const API_URL = '/api/ProductOrders/checkoutPayment';

      // First, try with the RTK Query hook
      try {
        console.log("Calling orderPayment API using RTK Query...");
        const response = await orderPayment(orderPayloadData).unwrap();
        console.log("API Response:", response);

        if (response && (response.success === true || response.success === 'ok')) {
          await handleOrderSuccess(response);
        } else {
          throw new Error("API returned unsuccessful response");
        }
      } catch (error) {
        console.error("RTK Query approach failed:", error);

        // Fallback to direct fetch if RTK Query fails
        try {
          console.log("Trying direct fetch approach...");

          const response = await fetch(API_URL, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(orderPayloadData),
            credentials: 'include'
          });

          const data = await response.json();
          console.log("Direct fetch response:", data);

          if (data && (data.success === true || data.success === 'ok')) {
            await handleOrderSuccess(data);
          } else {
            toast.error('Ошибка при оформлении заказа');
          }
        } catch (fetchError) {
          console.error("Direct fetch also failed:", fetchError);
          toast.error('Ошибка при оформлении заказа. Попробуйте позже.');
        }
      }
    } catch (error) {
      console.error('[Checkout] Order submission failed:', error);
      toast.error('Ошибка при оформлении заказа');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading overlay if processing a pending order
  if (loading || processingPendingOrder || sendOrderIsLoading || orderPaymentIsLoading) {
    return (
      <div className="content pb-6 flex items-center justify-center min-h-[300px]">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-colGreen mb-4"></div>
          <p className="text-colGreen font-medium">
            {processingPendingOrder || sendOrderIsLoading || orderPaymentIsLoading
              ? 'Обрабатываем ваш заказ...'
              : 'Загрузка данных заказа...'}
          </p>
        </div>
      </div>
    );
  }

  if (!orderData || !orderData.data || orderData.data.length === 0) {
    return (
      <div className="content pb-6 flex items-center justify-center min-h-[300px]">
        <div className="text-center">
          <p className="text-colRed font-medium mb-4">Данные заказа не найдены</p>
          <NavLink to="/shopping-cart" className="text-white font-semibold bg-colGreen rounded py-2 px-4">
            Вернуться в корзину
          </NavLink>
        </div>
      </div>
    );
  }

  // Calculate total amount from all orders
  const totalOrderAmount = orderData.data.reduce(
    (sum, order) => sum + order.total.amount,
    0
  );

  // Calculate total quantity from all orders
  const totalOrderQuantity = orderData.data.reduce(
    (sum, order) => sum + order.total.quantity,
    0
  );

  return (
    <div className="content pb-16 lining-nums proportional-nums overflow-auto">
      <FormProvider {...methods}>
        <div>
          <NavLink to="/shopping-cart">
            <div className="flex mb-[10px]">
              <img src={arrow} alt="" />
              <div className="font-semibold">Вернуться к корзине</div>
            </div>
          </NavLink>
          <div className="font-semibold text-[40px] mb-6">Оформление заказа ({orderData.data.length})</div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left column - Order details */}
            <div className="lg:col-span-2">
              <div className="space-y-6">
                {/* Render each order */}
                {orderData.data.map((order, orderIndex) => (
                  <div key={order.order_number} className="bg-white rounded-lg shadow-sm p-4 mb-6">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="font-bold text-lg">
                        Заказ #{orderIndex + 1} от {order.company_info.name}
                      </h3>
                      <div className="text-right">
                        <p className="text-xs text-gray-500">Номер заказа:</p>
                        <p className="font-semibold">{order.order_number}</p>
                      </div>
                    </div>

                    <div className="mb-4">
                      <div className="inline-flex items-center px-2 py-1 rounded-md bg-[#51F9FF] text-[#343434]">
                        <span className="text-sm font-medium">{order.status.name}</span>
                      </div>
                    </div>

                    {/* Order items */}
                    <div className="space-y-4 mb-6">
                      {order.items.map((item) => (
                        <div key={item.id} className="border-b pb-4">
                          <div className="flex gap-4 items-start">
                            {/* Item image */}
                            <div className="w-20 h-20 flex-shrink-0 bg-gray-100 rounded overflow-hidden">
                              {item.files && item.files.length > 0 ? (
                                <img
                                  src={item.files[0].compact}
                                  alt={item.name}
                                  className="w-full h-full object-contain"
                                />
                              ) : (
                                <div className="w-full h-full bg-gray-200 flex items-center justify-center text-xs text-gray-500">
                                  Нет фото
                                </div>
                              )}
                            </div>

                            {/* Item details */}
                            <div className="flex-grow">
                              <h4 className="font-semibold text-sm">{item.fullName}</h4>
                              <p className="text-xs text-gray-500 mt-1">Артикул: {item.sku}</p>
                              <div className="flex justify-between items-center mt-2">
                                <div className="text-sm">
                                  <span className="font-medium">
                                    {item.price.final} {item.price.currency.symbol}
                                  </span>
                                  {item.price.unit && (
                                    <span className="text-gray-500 text-xs ml-1">за {item.price.unit}</span>
                                  )}
                                </div>
                                <div className="text-sm">
                                  <span className="text-gray-500 mr-2">Кол-во:</span>
                                  <span className="font-medium">{item.quantity}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Order total */}
                    <div className="bg-gray-50 rounded p-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">Всего по заказу:</span>
                        <span className="font-bold">
                          {order.total.amount} {order.items[0]?.price.currency.symbol || '₽'}
                        </span>
                      </div>
                      <div className="flex justify-between items-center text-sm text-gray-500 mt-1">
                        <span>Количество товаров:</span>
                        <span>{order.total.quantity} шт.</span>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Contact Information Form */}
                <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
                  <h3 className="font-bold text-lg mb-4">Контактные данные</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <CTextField
                      label="Имя*"
                      placeholder="Введите имя"
                      {...methods.register('name', { required: 'Имя обязательно' })}
                      error={methods.formState.errors.name?.message}
                      required
                    />
                    <CTextField
                      label="Телефон*"
                      placeholder="+7 (___) ___-__-__"
                      {...methods.register('phone', { required: 'Телефон обязателен' })}
                      error={methods.formState.errors.phone?.message}
                      required
                    />
                    <CTextField
                      label="Email*"
                      placeholder="<EMAIL>"
                      {...methods.register('email', { required: 'Email обязателен' })}
                      error={methods.formState.errors.email?.message}
                      required
                    />
                  </div>
                </div>

                {/* Delivery */}
                <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
                  <h3 className="font-bold text-lg mb-4">Пункт самовывоза</h3>
                  <div className="flex items-start gap-3 mb-4">
                    <img src={markerIcon} alt="Адрес" className="mt-1" />
                    <div>
                      <p className="font-medium">г. Кузнецк, ТЦ Росток, ул. Ленина, д. 15 к2 секция 4</p>
                      <p className="text-sm text-gray-500 mt-1">Пн-Вс: с 8 до 21</p>
                      <button
                        type="button"
                        className="text-colGreen text-sm mt-2"
                        onClick={() => showModal({ type: 'pickupPoint' })}
                      >
                        Посмотреть на карте
                      </button>
                    </div>
                  </div>

                  {/* Map preview */}
                  <div className="relative h-[200px] rounded-lg overflow-hidden mb-4">
                    <YMaps>
                      <Map
                        defaultState={{ center: [53.119335, 46.601165], zoom: 15 }}
                        width="100%"
                        height="100%"
                      >
                        <Placemark geometry={[53.119335, 46.601165]} />
                      </Map>
                    </YMaps>
                  </div>
                </div>

                {/* Comment */}
                <div className="bg-white rounded-lg shadow-sm p-4">
                  <h3 className="font-medium mb-2">Комментарий к заказу</h3>
                  <CTextField
                    placeholder="Добавьте комментарий к заказу"
                    {...methods.register('comment')}
                    multiline
                    rows={3}
                  />
                </div>
              </div>
            </div>

            {/* Right column - Payment and summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-4 mb-6 sticky top-4">
                <h3 className="font-bold text-lg mb-4">Способ оплаты</h3>

                {/* Payment methods */}
                <div className="space-y-3 mb-6">
                  {orderData.variants_pay.map(method => (
                    <div
                      key={method.payment_id}
                      className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                        selectedPaymentMethod === method.payment_id.toString()
                          ? 'border-colGreen bg-green-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedPaymentMethod(method.payment_id.toString())}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                          selectedPaymentMethod === method.payment_id.toString()
                            ? 'border-colGreen'
                            : 'border-gray-300'
                        }`}>
                          {selectedPaymentMethod === method.payment_id.toString() && (
                            <div className="w-3 h-3 rounded-full bg-colGreen"></div>
                          )}
                        </div>
                        <span className="font-medium">{method.name}</span>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Order summary */}
                <div className="border-t pt-4 mb-6">
                  <h3 className="font-bold text-lg mb-3">Ваш заказ</h3>

                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Товары ({totalOrderQuantity}):</span>
                      <span className="font-medium">{totalOrderAmount} ₽</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Доставка:</span>
                      <span className="font-medium text-green-600">Бесплатно</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center pt-3 border-t">
                    <span className="font-bold">Итого:</span>
                    <span className="font-bold text-xl">{totalOrderAmount} ₽</span>
                  </div>
                </div>

                {/* Direct submit button instead of form submit */}
                <button
                  type="button"
                  onClick={handleSubmitOrder}
                  className="w-full bg-colGreen text-white py-3 rounded-lg font-semibold hover:bg-green-600 transition-colors"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <span className="flex items-center justify-center">
                      <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                      Обработка...
                    </span>
                  ) : (
                    'Оформить заказ'
                  )}
                </button>
                <p className="text-xs text-gray-500 text-center mt-3">
                  Нажимая на кнопку, вы подтверждаете согласие с условиями оферты
                </p>
              </div>
            </div>
          </div>
        </div>
      </FormProvider>
    </div>
  );
};

export default CartCheckout;