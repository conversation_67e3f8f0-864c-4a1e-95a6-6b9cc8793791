# SEO Feature

This feature handles all SEO-related functionality including meta tags, Open Graph tags, and route-based metadata for the Furnica application.

## Implementation Details

### Data Flow

1. When a user navigates to a route, React Router calls the `routeLoader` function
2. The loader fetches route-specific metadata from the `/route/info` endpoint
3. The data is made available to components via React Router's data APIs
4. The `SEO` component consumes this data and updates the document head

## Components

### `SEO` Component

The main component that renders all meta tags. It should be included in your root layout.

```tsx
import { SEO } from '@/features/SEO/SEO';

function App() {
  return (
    <>
      <SEO />
      {/* Rest of your app */}
    </>
  );
}
```

## Hooks

### `useRouteData()`

Hook to access the current route's metadata in any component:

```tsx
import { useRouteData } from '@/features/SEO/hooks/useRouteData';

function MyComponent() {
  const routeData = useRouteData();
  // Use routeData.meta, routeData.page, etc.
}
```

## Configuration

### Router Setup

Your router should be configured to use the `routeLoader`:

```tsx
import { routeLoader } from '@/features/SEO/routeLoader';

const router = createBrowserRouter([
  {
    path: '/',
    element: <RootLayout />,
    loader: routeLoader,
    children: [
      // Your routes
    ],
  },
]);
```

### RTK Query Setup

Make sure to add the `routeInfoApi` to your store:

```ts
import { configureStore } from '@reduxjs/toolkit';
import { routeInfoApi } from '@/features/SEO/api/routeInfoApi';

export const store = configureStore({
  reducer: {
    [routeInfoApi.reducerPath]: routeInfoApi.reducer,
    // ...other reducers
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(routeInfoApi.middleware),
});
```

## Best Practices

1. **Fallback Values**: Always provide default values for all meta tags
2. **Error Handling**: The loader gracefully handles API errors
3. **Performance**: Data is cached for 5 minutes to reduce API calls
4. **Type Safety**: Fully typed with TypeScript

## Testing

You can test the SEO implementation by:

1. Checking the page source to verify meta tags
2. Using browser dev tools to inspect the document head
3. Testing with social media sharing tools
4. Using SEO audit tools like Google's Rich Results Test
