# Authentication Flow & Token Management

This document describes the authentication architecture, token handling, and initialization flow in the application.

*Last updated: 2025-04-24 09:38 UTC*

## Overview

The application uses a simple token-based authentication with cookies for token storage. The authentication flow involves:

1. User authentication (via phone verification)
2. Token generation and storage in cookies
3. Application initialization and data synchronization
4. Authenticated API calls

## Token Management

### Simplified Token Approach

The application uses a single, direct token cookie:

**Furnica <PERSON>ken (`furnica_token`)**: Set directly in cookies after successful authentication.

### Token Storage

Tokens are managed in `src/entities/user/lib/cookies.ts`:

```typescript
// Token key constant
export const FURNICA_TOKEN_KEY = 'furnica_token';

// Get token from cookies
export const getTokenFromCookies = (): string | null => {...}

// Set token in cookies
export const setTokenInCookies = (token: string, days: number = 7): void => {...}

// Remove token from cookies
export const removeTokenFromCookies = (): void => {...}

// Check if token exists in cookies
export const hasTokenInCookies = (): boolean => {...}
```

## Authentication Flow

### Phone Authentication

1. User enters phone number in `AuthModal.tsx`
2. GX widget sends verification code to the phone
3. User enters verification code
4. GX widget verifies code and calls the backend API
5. Backend returns user data and token
6. Our code saves the token directly in `furnica_token` cookie via `setTokenInCookies()`
7. Redux state is updated with token and user data
8. Application synchronizes data with the server (cart, favorites, etc.)

## Protected Routes

Protected routes use the `ProtectedRoute` component to guard access:

```jsx
<Route
  path='profile'
  element={
    <ProtectedRoute>
      <Profile />
    </ProtectedRoute>
  }
>
```

This component:

1. Uses `hasTokenInCookies()` to check authentication status
2. Redirects to home and shows auth modal if not authenticated
3. Renders protected content when authenticated

## API Integration

The API base query in `src/shared/api/api.ts` automatically:

1. Gets token from cookies using `getTokenFromCookies()`
2. Adds Authorization header to all requests: `Bearer ${token}`
3. Falls back to Redux state if cookie is not found
4. Handles API-level errors
5. Shows appropriate toast notifications for errors

## Initialization Process

The application's initialization logic is in `InitializationProvider.tsx`:

1. On load, the app checks for existing token in cookies via `initializeAuthAsync()`
2. If token exists, sets authentication state to true in Redux
3. Fetches user data if authenticated
4. Triggers data synchronization (cart, favorites, comparison, recent items)
5. Shows loading overlay during initialization and synchronization

## Key Files

The following files are involved in the authentication system:

1. **Token Management:**
   - `src/entities/user/lib/cookies.ts` - Cookie handling functions
   - `src/entities/user/model/userSlice.ts` - Redux state for authentication

2. **Authentication Flow:**
   - `src/shared/ui/gx-widget-react/hooks/usePhoneVerification.js` - Phone verification logic
   - `src/features/auth/ui/modals/AuthModal.tsx` - Authentication modal

3. **Protected Routes:**
   - `src/components/ProtectedRoute/ProtectedRoute.jsx` - Route protection logic
   - `src/app/routing/router.tsx` - Application routes definition

4. **Initialization & Data Sync:**
   - `src/app/providers/InitializationProvider/InitializationProvider.tsx` - App initialization
   - `src/app/providers/InitializationProvider/lib/useInitialDataSync.ts` - Data synchronization

5. **API Integration:**
   - `src/shared/api/api.ts` - API base query configuration

## Troubleshooting

1. **Authentication Issues:**
   - Make sure `furnica_token` cookie is being set correctly after login
   - Check browser's application tab to verify cookie existence
   - Verify that API requests include Authorization header

2. **Initial Data Sync Issues:**
   - If cart/favorites counters are not showing after login, check network requests
   - Verify that data sync is triggered in InitializationProvider after authentication
   - Check for errors in browser console during data synchronization

3. **Protected Route Issues:**
   - If protected routes are not accessible, check that `hasTokenInCookies()` is working
   - Verify that the cookie is properly set and not expired
   - Check if authentication flow is completing properly

## Future Improvements

1. Add token refresh mechanism for longer sessions
2. Implement silent authentication for returning users
3. Add secure and HttpOnly flags to cookies for better security
4. Add CSRF protection for authentication endpoints
