import { modalFilterItemObserver } from '@/shared/lib/observer';
import { ObserverItem } from '@/shared/types/observer';
import { ModalFilterItem } from '@/shared/types/observer/modal.observer';
import { Button } from '@/shared/ui';
import { ProductFilter } from '../product-filter';
import { useModalAnimation } from './hook/useModalAnimation';
import { useModalItemFilter } from './hook/useModalItemFilter';

interface ModalItemProps {
  data: ObserverItem<ModalFilterItem>;
}

export const ModalItem: React.FC<ModalItemProps> = ({ data }) => {
  const { isUnvisible } = useModalAnimation(data.observerDismiss);
  const { handleClick, isSuccess, selects } = useModalItemFilter(data);

  return (
    <>
      <div
        className={`absolute inset-0 bg-black/40 transition-opacity duration-200 ease-in-out ${!isUnvisible ? 'opacity-100' : 'opacity-0'}`}
        style={{
          zIndex: `${data.observerId - 1}`
        }}
      />
      <div
        style={{
          zIndex: `${data.observerId}`,
          maxHeight: '90vh'
        }}
        className={`flex h-[500px] w-[320px]  overflow-hidden gap-4 absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-white p-4 rounded-lg shadow-lg transition-all duration-200 flex flex-col ${
          !isUnvisible ? 'opacity-100 scale-100' : 'opacity-0 scale-[.2]'
        }`}
      >
        <div className='flex-grow overflow-hidden'>
          <ProductFilter data={data.data} mode='alone' onChange={handleClick} />
        </div>

        <Button
          variant='primary'
          onClick={() => {
            data.onAccept({ multiple: selects });
            modalFilterItemObserver.dissmissObserver(data.observerId);
          }}
          className='mb-[-8px]'
          disabled={!isSuccess}
        >
          Применить
        </Button>
        <Button
          variant='secondary'
          onClick={() => {
            modalFilterItemObserver.dissmissObserver(data.observerId);
            data.onClose();
          }}
        >
          Закрыть
        </Button>
      </div>
    </>
  );
};
