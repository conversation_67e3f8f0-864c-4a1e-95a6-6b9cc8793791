# Legacy Documentation: `/src/components/`

This document describes modules currently located in the `/src/components/` directory that need refactoring into the appropriate Feature-Sliced Design (FSD) layers (`pages`, `widgets`, `features`, `entities`, `shared`).

## Overview

The `/src/components/` directory contains a mixture of components performing different roles:

*   **Page-Level Components:** Components that structure entire pages (e.g., Catalog, SearchResults, Profile sections).
*   **Widget-Level Components:** Composite UI blocks used within pages (e.g., Sidebars, Content areas, Forms).
*   **Feature-Specific UI:** UI elements tied directly to a specific feature's interaction (e.g., checkout form parts).
*   **UI Helpers:** Smaller, potentially reusable display components.

## Modules & Refactoring Targets

1.  **`/src/components/About/`**
    *   **Contains:** `WantToBePartnerFrom.jsx`, `WeInContactForm.jsx` (empty).
    *   **Purpose:** UI related to the "About Us" page, including a contact form.
    *   **Target FSD:**
        *   The form itself (`WantToBePartnerFrom`) might be a `feature/partnership-application/ui` component.
        *   The overall composition belongs in `pages/AboutPage.tsx`.

2.  **`/src/components/Catalog/`**
    *   **Contains:** A large number of components related to browsing products (`Brands.tsx`, `Catalog.tsx`, `Search.tsx`, `Tags.tsx`, `CatalogRoot.tsx`, `CatalogMainContent.tsx`, `CatalogMainSidebar.tsx`, `CategoryCard.tsx`), sidebar/content structure (`CatalogSidebar/`, `CatalogContent/`), and associated hooks/utils.
    *   **Purpose:** Handles product listing, filtering, sorting, and category navigation for various contexts (category, brand, tag, search).
    *   **Target FSD:**
        *   `Catalog.tsx`, `Brands.tsx`, `Search.tsx`, `Tags.tsx`, `CatalogRoot.tsx`: These act as **Pages** and should move to `src/pages/`, possibly under `src/pages/catalog/`.
        *   `CatalogSidebar/`, `CatalogContent/`, `SRSidebar`, `SRContent`: These are **Widgets** or parts of widgets responsible for layout and composition on catalog/search pages. Move to `src/widgets/catalog/` or `src/widgets/search/`.
        *   `CategoryCard.tsx`: Likely a reusable display component, target `shared/ui` or `entities/category/ui`.
        *   Filtering components within `CatalogSidebar/SidebarFilters/`: Target `entities/filter/ui` (generic parts like PriceRange) or a new `features/product-filtering/ui` (specific implementations like BrandsFilter, DynamicFilters).
        *   Hooks (`hooks/`): Target `features/product-filtering/model` or `lib`.
        *   Utils (`utils/`): Target `features/product-filtering/lib` or `shared/lib`.

3.  **`/src/components/Checkout/`**
    *   **Contains:** Various form components (`FizlicoLoggedInForm.jsx`, `UrlicoNotLoggedForm.jsx`, etc.).
    *   **Purpose:** Provides UI forms for collecting user and order details during checkout.
    *   **Target FSD:** These are UI parts of the **Checkout Feature**. Move to `src/features/checkout/ui/`.

4.  **`/src/components/Comparison/`**
    *   **Contains:** `ComparisonDetail.tsx`, `ComparisonProductCard.tsx`.
    *   **Purpose:** UI for displaying the product comparison table and individual product columns within it.
    *   **Target FSD:**
        *   `ComparisonDetail.tsx`: Likely a **Widget** (`widgets/comparison-table/ui`).
        *   `ComparisonProductCard.tsx`: A specialized product card view, could be `widgets/comparison-table/ui` or potentially `widgets/product-card/ui` as a variant.

5.  **`/src/components/Contacts/`**
    *   **Contains:** `BranchCard.tsx`, `RequisitesTable.tsx`.
    *   **Purpose:** UI components for displaying contact information (branches, company details).
    *   **Target FSD:** These are likely reusable display components. Target `shared/ui` or potentially `widgets/contact-info/ui` if they are always used together.

6.  **`/src/components/DevTools/`**
    *   **Contains:** `CatalogSwitcher.tsx`.
    *   **Purpose:** Developer utility for testing different implementations.
    *   **Target FSD:** Not part of the core application. Could remain as a dev-only tool, potentially within `src/app/dev/` or removed entirely for production builds.

7.  **`/src/components/ErrorBoundary/`**
    *   **Contains:** `ErrorBoundary.tsx`, `ErrorBoundaryWrapper.tsx`.
    *   **Purpose:** Global error handling mechanism.
    *   **Target FSD:** As a core application concern, move to `src/app/providers/error-boundary/` or potentially `src/shared/ui/error-boundary/` if considered a generic UI utility. The `errorService` within `ErrorBoundary.tsx` should move to `src/shared/api/` or `src/shared/lib/`.

8.  **`/src/components/Favorites/`**
    *   **Contains:** `FavDetail.tsx`, `FavSidebar.tsx`.
    *   **Purpose:** UI layout for the Favorites page.
    *   **Target FSD:** These are **Widgets** structuring the Favorites page. Move to `src/widgets/favorites/ui/`.

9.  **`/src/components/Footer/`**
    *   **Contains:** `Footer.tsx` and sub-components in `components/`.
    *   **Purpose:** Application footer.
    *   **Target FSD:** This is a **Widget**. Move to `src/widgets/footer/ui/`.

10. **`/src/components/Home/`**
    *   **Contains:** Components composing the home page (`Advantages.tsx`, `Banner.tsx`, `Brands.tsx`, etc.).
    *   **Purpose:** Specific sections displayed on the home page.
    *   **Target FSD:** These are mostly **Widgets**. Move relevant components to `src/widgets/home/<USER>/brands-carousel`). `MobileCategoryCard` could be `shared/ui`.

11. **`/src/components/ProductPage/`**
    *   **Contains:** Components specific to the product detail page layout and sections (`ProductPageDesktop.tsx`, `ProductPageMobile.tsx`, `RightBar.tsx`, `Gallery/`, `Attributes/`, `ProductTabs/`, etc.).
    *   **Purpose:** Structure and display elements for the product detail page.
    *   **Target FSD:**
        *   `ProductPageDesktop.tsx`, `ProductPageMobile.tsx`: These are **Widgets** composing the different views of the product page. Move to `src/widgets/product-page/ui/`.
        *   `RightBar.tsx`: A **Widget** for the product page sidebar containing price and purchase actions. Move to `src/widgets/product-page/ui/`.
        *   `ProductGallery.tsx`, `ProductTabs.jsx`: Likely **Widgets**. Move to `src/widgets/product-page/ui/`.
        *   Components within `Attributes/`, `Gallery/`, `ProductTabs/`, `Mobile/`: Sub-components belonging to their parent widgets. Move along with the parent widget structure.

12. **`/src/components/Profile/`**
    *   **Contains:** Components for different profile sections (`ChangePassword/`, `MyOrders/`, `Organizations/`, `PersonalData/`, `ProfileSidebar.tsx`, `UserReviews/`).
    *   **Purpose:** UI for managing user profile information and related data (orders, organizations, reviews).
    *   **Target FSD:**
        *   `ProfileSidebar.tsx`: A **Widget**. Move to `src/widgets/profile-sidebar/ui/`.
        *   Components within subdirectories (`ChangePassword.jsx`, `MyOrders.tsx`, etc.): These are often the main content for specific profile **Pages**. They might contain logic belonging to `features` or display data using `widgets`/`entities`. Refactor primarily into `src/pages/profile/` sub-pages, extracting feature logic (e.g., password change form handling) to `src/features/` and data display using components from lower layers. `Order` related components (`MyOrders/`) might form a `widgets/order-list` or similar. `Organization` components belong with `pages/profile/OrganizationsPage` and potentially `widgets/org-card`.

13. **`/src/components/ProtectedRoute/`**
    *   **Contains:** `ProtectedRoute.jsx`.
    *   **Purpose:** Guards routes based on authentication status.
    *   **Target FSD:** Primarily related to authentication flow. Move to `src/features/auth/ui/` or `src/shared/lib/routing/` if made more generic.

14. **`/src/components/SearchResults/`**
    *   **Contains:** `SRContent.tsx`, `SRMain.tsx`, `SRSidebar.tsx`.
    *   **Purpose:** Legacy implementation of search results display.
    *   **Target FSD:** Largely superseded by the components intended for `pages/SearchPage` (currently in `src/components/Catalog/Search.tsx`) and related widgets (`widgets/search/`). This directory should likely be removed after refactoring `src/components/Catalog/Search.tsx`.