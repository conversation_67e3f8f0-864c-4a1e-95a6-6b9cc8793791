import { Icon } from '@iconify-icon/react';

import type { DeliveryInfo as DeliveryInfoType } from '@/entities/order/api/types';

interface DeliveryInfoProps {
  delivery?: DeliveryInfoType;
  className?: string;
  variant?: 'default' | 'compact';
}

export const DeliveryInfo = ({
  delivery,
  className = '',
  variant = 'default'
}: DeliveryInfoProps): JSX.Element | null => {
  if (!delivery) {
    return null;
  }

  // For pickup delivery type
  if (delivery.type === 'pickup' && delivery.point) {
    const { point } = delivery;

    if (variant === 'compact') {
      return (
        <div className={`flex items-start text-xs text-colDarkGray ${className}`}>
          <Icon
            icon="solar:map-point-outline"
            className="text-colGreen flex-shrink-0 mt-0.5 mr-1"
            width="16"
            height="16"
          />
          <div className="truncate">
            <span className="font-medium">{point.name}</span>
          </div>
        </div>
      );
    }

    return (
      <div className={`flex items-start text-xs text-colDarkGray ${className}`}>
        <Icon
          icon="solar:map-point-outline"
          className="text-colGreen flex-shrink-0 mt-0.5 mr-1"
          width="18"
          height="18"
        />
        <div>
          <div className="font-medium">{point.name}</div>
          <div>{point.city}, {point.address}</div>
        </div>
      </div>
    );
  }

  // For courier delivery type
  if (delivery.type === 'courier' && delivery.address) {
    if (variant === 'compact') {
      return (
        <div className={`flex items-start text-xs text-colDarkGray ${className}`}>
          <Icon
            icon="solar:truck-outline"
            className="text-colGreen flex-shrink-0 mt-0.5 mr-1"
            width="16"
            height="16"
          />
          <div className="truncate">
            <span className="font-medium">Доставка</span>
          </div>
        </div>
      );
    }

    return (
      <div className={`flex items-start text-xs text-colDarkGray ${className}`}>
        <Icon
          icon="solar:truck-outline"
          className="text-colGreen flex-shrink-0 mt-0.5 mr-1"
          width="18"
          height="18"
        />
        <div>
          <div className="font-medium">Доставка курьером</div>
          <div>{delivery.address}</div>
        </div>
      </div>
    );
  }

  // Default case - just show delivery type
  return (
    <div className={`flex items-start text-xs text-colDarkGray ${className}`}>
      <Icon
        icon={delivery.type === 'pickup' ? "solar:map-point-outline" :
              delivery.type === 'courier' ? "solar:truck-outline" :
              "solar:info-circle-outline"}
        className="text-colGreen flex-shrink-0 mt-0.5 mr-1"
        width="18"
        height="18"
      />
      <div>
        <div className="font-medium">
          {delivery.type === 'pickup'
            ? 'Самовывоз'
            : delivery.type === 'courier'
              ? 'Доставка курьером'
              : 'Способ доставки не указан'}
        </div>
      </div>
    </div>
  );
};
