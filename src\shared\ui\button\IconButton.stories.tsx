import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { IconButton } from './IconButton';
import { Heart, Trash2, Plus } from 'lucide-react'; // Example icons

const meta = {
  title: 'Shared/UI/IconButton',
  component: IconButton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'outline', 'ghost', 'link', 'added'],
    },
    disabled: { control: 'boolean' },
    isLoading: { control: 'boolean' },
    'aria-label': { control: 'text' },
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked
  args: { 
    onClick: fn(), 
    icon: <Heart />, 
    'aria-label': 'Favorite' 
  },
} satisfies Meta<typeof IconButton>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    variant: 'primary',
    icon: <Plus />,
    'aria-label': 'Add',
  },
};

export const Outline: Story = {
  args: {
    variant: 'outline',
    icon: <Trash2 />,
    'aria-label': 'Delete',
  },
};

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    icon: <Heart />,
    'aria-label': 'Favorite Ghost',
  },
};

export const Added: Story = {
  args: {
    variant: 'added',
    icon: <Heart />,
    'aria-label': 'Favorited',
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    icon: <Heart />,
    'aria-label': 'Disabled Favorite',
  },
};
