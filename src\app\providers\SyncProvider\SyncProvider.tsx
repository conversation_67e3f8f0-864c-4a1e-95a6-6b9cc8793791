// src/app/providers/SyncProvider/SyncProvider.tsx
import { setUserData } from '@/entities/user';
import { useGetUserDataQuery } from '@/entities/user/api/userApi';
import { useAuthContext } from '@/entities/user/model/AuthContext';
import { useCart } from '@/features/cart/model/hooks/useCart';
import { useComparison } from '@/features/comparison/model/hooks/useComparison';
import { useFavorites } from '@/features/favorite/model/hooks/useFavorites';
import React, { createContext, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
export interface SyncContextType {
  isInitialized: boolean;
  syncOnLogin: () => Promise<void>;
  syncOnLogout: () => void;
}

const SyncContext = createContext<SyncContextType | null>(null);

export const useSyncContext = (): SyncContextType => {
  const context = useContext(SyncContext);
  if (!context) {
    throw new Error('useSyncContext must be used within a SyncProvider');
  }
  return context;
};

export const SyncProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const dispatch = useDispatch();
  const { isAuthenticated, isInitialized: authInitialized } = useAuthContext();
  const [isInitialized, setIsInitialized] = useState(false);
  const initOnceRef = useRef(false);

  // Entity hooks
  const {
    loadFromStorage: loadCartFromStorage,
    refreshFromServer: refreshCartFromServer,
    clearData: clearCartData,
    syncToServer: syncCartToServer,
    cart: cartData
  } = useCart();

  const {
    loadFromStorage: loadFavoritesFromStorage,
    refreshFromServer: refreshFavoritesFromServer,
    clearData: clearFavoritesData,
    syncToServer: syncFavoritesToServer,
    favorites: favoritesData
  } = useFavorites();

  const {
    loadFromStorage: loadComparisonFromStorage,
    refreshFromServer: refreshComparisonFromServer,
    clearData: clearComparisonData,
    syncToServer: syncComparisonToServer,
    comparison: comparisonData
  } = useComparison();

  // Track previous auth state for login/logout detection
  const prevAuthRef = useRef<boolean | null>(null);

  // User data fetching
  const { data: userData } = useGetUserDataQuery(undefined, {
    skip: !authInitialized
  });

  // Update user data in Redux when available
  useEffect(() => {
    if (userData?.user) {
      console.log('👤 User data received, updating Redux');
      dispatch(setUserData(userData.user));
    }
  }, [userData, dispatch]);

  // Login sync function
  const syncOnLogin = useCallback(async (): Promise<void> => {
    try {
      console.log('🔄 Starting login sync...');

      // Step 1: Upload local data to server
      const uploadPromises: Promise<any>[] = [];

      // Upload cart data
      if (cartData?.cart && cartData.cart.length > 0) {
        uploadPromises.push(
          syncCartToServer(cartData.cart).catch((err) => {
            console.warn('[SyncProvider] Error uploading cart:', err);
          })
        );
      }

      // Upload favorites data
      if (favoritesData && favoritesData.length > 0) {
        uploadPromises.push(
          syncFavoritesToServer(favoritesData).catch((err) => {
            console.warn('[SyncProvider] Error uploading favorites:', err);
          })
        );
      }

      // Upload comparison data
      if (comparisonData && comparisonData.length > 0) {
        uploadPromises.push(
          syncComparisonToServer(comparisonData).catch((err) => {
            console.warn('[SyncProvider] Error uploading comparison:', err);
          })
        );
      }

      // Wait for all uploads to complete
      if (uploadPromises.length > 0) {
        await Promise.allSettled(uploadPromises);
        console.log('✅ Local data uploaded to server');
      }

      // Step 2: Fetch fresh data from server
      console.log('📥 Fetching fresh data from server...');
      console.log('[SyncProvider] Attempting to call refreshCartFromServer...');
      const p1 = refreshCartFromServer();
      console.log('[SyncProvider] Attempting to call refreshFavoritesFromServer...');
      const p2 = refreshFavoritesFromServer();
      console.log('[SyncProvider] Attempting to call refreshComparisonFromServer...');
      const p3 = refreshComparisonFromServer();

      void Promise.allSettled([
        p1,
        p2,
        p3
      ]).finally(() => {
        console.log('✅ Login sync completed');
      });
    } catch (error) {
      console.error('❌ Error during login sync:', error);
    }
  }, [
    cartData,
    favoritesData,
    comparisonData,
    syncCartToServer,
    syncFavoritesToServer,
    syncComparisonToServer,
    refreshCartFromServer,
    refreshFavoritesFromServer,
    refreshComparisonFromServer
  ]);

  // Logout sync function
  const syncOnLogout = useCallback((): void => {
    console.log('🔄 Starting logout sync...');
    clearCartData();
    clearFavoritesData();
    clearComparisonData();
    setIsInitialized(false);
    initOnceRef.current = false;
    console.log('✅ Logout sync completed');
  }, [clearCartData, clearFavoritesData, clearComparisonData]);

  // Simplified initialization effect
  useEffect(() => {
    console.log('[SyncProvider] Initial load effect triggered. authInitialized:', authInitialized, 'initOnceRef.current:', initOnceRef.current);
    // Step 1: Initialize once when auth is ready
    if (authInitialized && !initOnceRef.current) {
      initOnceRef.current = true;
      console.log('⚙️ SyncProvider: Auth initialized. Performing initial data load from server...');

      // Always attempt to fetch all data from the server.
      // The server will determine if it's a logged-in user or a new visitor based on the cookie.
      // The `useGetUserDataQuery` is already set to skip if !isAuthenticated, 
      // so we need to ensure it's called or its skip logic is re-evaluated if we want user data on initial load unconditionally.
      // For now, let's assume `isAuthenticated` will become true if server validates cookie and returns user data.
      // If `isAuthenticated` remains false after server interaction, then cart/favs/comp will be for a 'guest' session from server if supported, or empty.

      // Fetch from server for all users (authenticated or guest)
      // The `useGetUserDataQuery` hook will fetch user data if isAuthenticated becomes true after server validation.
      // Cart, favorites, and comparison will be fetched regardless.
      console.log('[SyncProvider] Initial Load: Attempting to call refreshCartFromServer...');
      const p1 = refreshCartFromServer();
      console.log('[SyncProvider] Initial Load: Attempting to call refreshFavoritesFromServer...');
      const p2 = refreshFavoritesFromServer();
      console.log('[SyncProvider] Initial Load: Attempting to call refreshComparisonFromServer...');
      const p3 = refreshComparisonFromServer();

      void Promise.allSettled([
        p1,
        p2,
        p3
      ]).finally(() => {
        setIsInitialized(true);
        console.log('✅ Initial data fetch attempt from server completed.');
      });

      // Commenting out the direct local storage load for guests during initialization.
      // The server should provide the initial state for guests too, or these will be empty.
      // } else {
      //   // Load from storage for non-authenticated users
      //   loadCartFromStorage();
      //   loadFavoritesFromStorage();
      //   loadComparisonFromStorage();
      //   setIsInitialized(true);
      // }
    }
  }, [authInitialized, isAuthenticated, refreshCartFromServer, refreshFavoritesFromServer, refreshComparisonFromServer]);

  // Separate effect for handling auth state changes (login/logout)
  useEffect(() => {
    // Only handle changes after initial setup
    if (!initOnceRef.current || prevAuthRef.current === null) {
      prevAuthRef.current = isAuthenticated;
      return;
    }

    // User just logged in
    if (!prevAuthRef.current && isAuthenticated) {
      console.log('🔄 User logged in - triggering login sync');
      void syncOnLogin();
    }

    // User just logged out
    if (prevAuthRef.current && !isAuthenticated) {
      console.log('🔄 User logged out - triggering logout sync');
      syncOnLogout();
    }

    // Update previous auth state
    prevAuthRef.current = isAuthenticated;
  }, [isAuthenticated, syncOnLogin, syncOnLogout]);

  const contextValue: SyncContextType = {
    isInitialized,
    syncOnLogin,
    syncOnLogout
  };

  return <SyncContext.Provider value={contextValue}>{children}</SyncContext.Provider>;
};
