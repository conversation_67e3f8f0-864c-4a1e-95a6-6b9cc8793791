import React from 'react';

import stallicon from '@/shared/assets/icons/stall-icon.svg';
import truckicon from '@/shared/assets/icons/truck-icon.svg';
import boxicon from '@/shared/assets/icons/box-icon.svg';

const InfoTab: React.FC = () => {
  const deliveryOptions = [
    {
      icon: stallicon,
      title: 'Самовывоз',
      description: 'сегодня, из 1 магазина',
    },
    {
      icon: truckicon,
      title: 'Доставка курьером',
      description: 'завтра, от 500 р.',
    },
    {
      icon: boxicon,
      title: 'Транспортной компанией',
      description: 'СДЕК, Деловые Линии и др.',
    },
  ];

  const paymentOptions = [
    {
      icon: stallicon, // Re-using icons for simplicity, can be updated
      title: 'Наличными или картой в магазине',
      description: 'При получении заказа',
    },
    {
      icon: truckicon,
      title: 'Онлайн-оплата',
      description: '<PERSON>арт<PERSON><PERSON><PERSON> Visa, MasterCard, Мир',
    },
    {
      icon: boxicon,
      title: 'Оплата по счёту',
      description: 'Для юридических лиц',
    },
  ];

  const InfoCard = ({ icon, title, description }: { icon: string; title: string; description: string }) => (
    <div className='flex flex-col w-full sm:w-[300px] p-5 border border-colLightGray rounded-[10px]'>
      <div className='mb-[10px]'>
        <img className='w-10 h-10' src={icon} alt={`${title} icon`} />
      </div>
      <div className='mb-[10px] decoration-colGreen font-semibold underline underline-offset-8 cursor-pointer mr-2 text-sm'>{title}</div>
      <div className='text-sm'>{description}</div>
    </div>
  );

  return (
    <div className="py-5">
      <h3 className='text-2xl font-semibold'>Оплата и доставка</h3>

      <h4 className='text-xl mt-5 mb-4 font-semibold'>Способы получения</h4>
      <div className='flex lg:flex-row flex-col gap-5'>
        {deliveryOptions.map((option) => (
          <InfoCard key={option.title} {...option} />
        ))}
      </div>

      <h4 className='text-xl mt-8 mb-4 font-semibold'>Способы оплаты</h4>
      <div className='flex lg:flex-row flex-col gap-5'>
        {paymentOptions.map((option) => (
          <InfoCard key={option.title} {...option} />
        ))}
      </div>
    </div>
  );
};

export default InfoTab;
