import React, { useState } from 'react';
import { ProductCardOrder } from '@/widgets/product-card';
import noImg from '@/shared/assets/images/no-image.png'
import { Icon } from '@iconify-icon/react';
import type { OrderDetailsData } from '@/entities/order/api/types';

interface OrderItemProps {
  order: OrderDetailsData;
}

export const OrderItem: React.FC<OrderItemProps> = ({ order }) => {
  const [expanded, setExpanded] = useState(true);
  
  if (!order) {
    return <div className="p-4 text-center text-red-500">Ошибка: Данные заказа отсутствуют.</div>;
  }

  const toggleExpand = () => {
    setExpanded(!expanded);
  };

  const formatPrice = (price: number) => {
    return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ");
  };

  const formatProductsLabel = (count: number): string => {
    const lastDigit = count % 10;
    const lastTwoDigits = count % 100;
    if (lastTwoDigits >= 11 && lastTwoDigits <= 14) return 'товаров';
    if (lastDigit === 1) return 'товар';
    if (lastDigit >= 2 && lastDigit <= 4) return 'товара';
    return 'товаров';
  };

  const companyShortName = order.company_info?.short_name || 'Компания';
  const companyName = order.company_info?.name || 'Не указано';
  const companyLogo = order.company_info?.logo || noImg;

  return (
    <div className="flex flex-col gap-2 bg-white rounded-lg shadow-sm overflow-hidden border border-gray-100 p-4">
      {/* Order header */}
      <div 
        className="flex justify-between items-center p-4 transition-colors "
        // onClick={toggleExpand}
        style={{ fontFeatureSettings: "'pnum' on, 'lnum' on" }}
      >
        <div className="flex items-center gap-4 bg-colSuperLight p-2 rounded-lg">
          <div className='w-10 h-10'><img src={companyLogo} alt={`${companyShortName} logo`} /></div>
          <div className="">
            <p className="text-[#727272]">Компания:</p>
            <p className="font-semibold">{companyShortName}</p>
            <p className="font-medium text-sm">{companyName}</p>
          </div>
          <div className='w-10 h-10 bg-colLightGray flex justify-center items-center rounded-md'>
            <Icon icon="solar:alt-arrow-right-outline" width="24" height="24"/>
          </div>
        </div>
      </div>
      
      {/* Order items - collapsible */}
      {expanded && (
        <div>
          <div className="flex gap-8 flex-col ">
            {order.items.map((item) => (
              <ProductCardOrder key={item.id_product_item} product={item} order_number={order.order_number} showOrderControls={order.status.name === "Новый" ? true : false}/>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
