import { useState, useEffect, useMemo, useCallback } from 'react';

import type { ProductListCategoryChain } from '@/entities/category';
import type { Product } from '@/entities/product';

/**
 * Hook for managing category switching functionality
 * Can be used with both favorites and comparison features
 */
export const useCategorySwitcher = (
  categories: ProductListCategoryChain[],
  products: Product[],
) => {
  // Selected category ID (empty string means "All")
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  
  // Track loading state during category operations
  const [isUpdating, setIsUpdating] = useState(false);

  // Handle category selection logic
  useEffect(() => {
    // If a specific category IS selected, check if it's still valid.
    if (selectedCategory !== '') {
      const categoryExists = categories.some((cat) =>
        cat.chain.some((item) => String(item.id) === selectedCategory)
      );
      // If the selected category no longer exists in the provided categories list
      // (and there are products to display), reset to "All".
      if (!categoryExists && products?.length > 0) {
        setSelectedCategory('');
      }
    }
    // If selectedCategory is already '' (i.e., "All"), we don't need to do anything here.
    // This ensures "All" remains selected if it was the initial state or explicitly chosen.
  }, [categories, products, selectedCategory]);

  // Filter products by selected category
  const filteredProducts = useMemo(() => {
    return selectedCategory && products
      ? products.filter((item) => String(item.category.id) === selectedCategory)
      : products;
  }, [selectedCategory, products]);

  // Handle category change
  const handleCategoryChange = useCallback((categoryId: string | number) => {
    // Show loading state
    setIsUpdating(true);
    
    // Use setTimeout to allow the UI to update with the loading state
    setTimeout(() => {
      // When switching to the "All" category, make sure we use empty string
      if (categoryId === '' || categoryId === 0) {
        setSelectedCategory('');
      } else {
        setSelectedCategory(String(categoryId));
      }
      
      // Hide loading state after a small delay to ensure smooth transition
      setTimeout(() => {
        setIsUpdating(false);
      }, 100);
    }, 10);
  }, []);

  return {
    selectedCategory,
    filteredProducts,
    handleCategoryChange,
    isUpdating, // Expose the loading state
  };
};
