export const removePrefixes = (
  input: Record<string, any>,
  prefixes: string[]
): Record<string, any> => {
  return Object.entries(input).reduce(
    (acc, [key, value]) => {
      const prefix = prefixes.find((p) => key.startsWith(p));

      if (prefix === 'filter_') {
        const newKey = prefix ? key.slice(prefix.length) : key;
        acc[newKey] = { type: 'multiple', value: Array.isArray(value) ? value : [value] };
        return acc;
      }
      if (prefix === 'range_') {
        const newKey = prefix ? key.slice(prefix.length) : key;
        const [from, to] = value.split('-');
        acc[newKey] = {
          type: 'range',
          value: { from, to }
        };
        return acc;
      }
      if (prefix === 'sort_') {
        const newKey = prefix ? key.slice(prefix.length) : key;
        acc[newKey] = value;
        return acc;
      }
      return { ...acc, [key]: value };
    },
    {} as Record<string, any>
  );
};
