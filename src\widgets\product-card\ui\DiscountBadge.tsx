import type { Product } from '@/entities/product';

interface DiscountBadgeProps {
  product: Product;
  className?: string;
}

export const DiscountBadge = ({ product, className = '' }: DiscountBadgeProps) => {
  if (!product?.price?.discount || !product?.price?.discount?.percent) {
    return null;
  }

  return (
    <div className={`p-1 bg-red-600 text-white rounded-md font-semibold text-sm ${className}`}>
      -{product.price.discount.percent}%
    </div>
  );
};
