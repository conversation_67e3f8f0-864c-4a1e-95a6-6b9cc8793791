# Legacy Documentation: `/src/redux/` (Root Level)

This directory contains RTK Query endpoint definitions that should be co-located with their respective entity or feature slices according to FSD principles.

## Modules & Refactoring Targets

1.  **`contentEndpoints.ts`**
    *   **Purpose:** Defines RTK Query endpoints (`getContacts`, `getGuarantee`, `getFaq`, `getMainPageData`) for fetching general page content from a `/api/PageContent/get` endpoint, distinguished by a `target` parameter.
    *   **Target FSD:** These endpoints fetch data related to specific informational domains. Ideally, each `target` could correspond to its own simple entity or be grouped under a general `content` entity.
    *   **Refactoring:**
        *   Create entity slices if they don't exist (e.g., `entities/contact`, `entities/faq`, `entities/guarantee`, `entities/landing-content`).
        *   Move the corresponding query definition (`getContacts`, `getFaq`, etc.) into the `api/{entityName}Api.ts` file within each new entity slice.
        *   Alternatively, create a single `entities/content` slice and keep these endpoints grouped within its `api/contentApi.ts`.
        *   Update imports wherever these hooks (`useGetContactsQuery`, etc.) are used.
        *   Remove the original `src/redux/api/contentEndpoints.ts`.

2.  **`walletEndpoints.ts`**
    *   **Purpose:** Defines RTK Query endpoints (`getTransactionList`, `getWalletInfo`) related to a user's wallet or internal balance system.
    *   **Target FSD:** This functionality relates to a distinct "Wallet" domain.
    *   **Refactoring:**
        *   Create a new entity or feature slice named `wallet` (e.g., `src/entities/wallet/` or `src/features/wallet/` depending on complexity - likely `entity`).
        *   Move the endpoint definitions (`getTransactionList`, `getWalletInfo`) into `src/entities/wallet/api/walletApi.ts`.
        *   Define relevant types (`WalletInfo`, `Transaction`) in `src/entities/wallet/model/types.ts`.
        *   Update imports wherever `useGetTransactionListQuery` or `useGetWalletInfoQuery` are used.
        *   Remove the original `src/redux/api/walletEndpoints.ts`.