# Legacy Documentation: `/src/helpers/`

This directory contains miscellaneous helper components and utilities that should be relocated to appropriate FSD layers.

## Modules & Refactoring Targets

1.  **`ErrorEmpty.jsx`, `ErrorServer.jsx`**
    *   **Purpose:** Display components for specific states (no data, server error).
    *   **Target FSD:** These are reusable UI components. Move to `src/shared/ui/errors/` or a more general `src/shared/ui/feedback/`.

2.  **`FAQStyledAccordion.jsx`**
    *   **Purpose:** A specifically styled version of the MUI Accordion component for use in the FAQ page.
    *   **Target FSD:** If this styling is unique to the FAQ page, it could be a local component within `src/pages/FAQ/ui/`. If the styled accordion pattern is intended to be reusable, move to `src/shared/ui/accordion/` or similar.

3.  **`QuestionForm.tsx`**
    *   **Purpose:** A form for users to submit questions or feedback, including fields for name, phone, and message. It includes logic to call the `sendFeedback` API mutation.
    *   **Target FSD:** This component combines UI and feature logic.
        *   The form UI itself could be a reusable component in `shared/ui/forms/`.
        *   The logic involving `useSendFeedbackMutation` belongs in a feature, likely `features/feedback/` or potentially integrated directly into the modals/components that use it if it's not complex.