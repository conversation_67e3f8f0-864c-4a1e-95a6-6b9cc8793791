# Entity: Category

## Purpose

This entity represents the product **Category** structure within the application. It defines category data, hierarchy, and provides the API endpoint for fetching category trees.

## Key Data Points

*   Basic category info: `id`, `name`, `slug`.
*   Category image (`ImageSet` type).
*   Product count within the category (`product_count`).
*   Category hierarchy (`children`, `path`, `category_chain` in API responses).

## Structure

*   **`model/types.ts`:** Defines the core interfaces: `CategoryBase`, `CategoryWithImage`, `CategoryFull`, and `ProductListCategoryChain` (used in API responses like favorites/comparison to show category distribution). *(Currently in `src/entities/category/types.ts`)*
*   **`api/categoryApi.ts`:** Defines the RTK Query endpoint:
    *   `getCategoryTree`: Query to fetch the category tree structure, often starting from a specific parent category ID (or `null` for the root). Includes the current category's details, its parent chain, and its direct children. Uses `providesTags` (though currently seems to lack specific tagging).
*   **`api/types.ts`:** Defines the `GetCategoryTreeResponse` interface.
*   **`index.ts`:** Public API for the entity, exporting types and the API hook.
*   **`ui/`:** (Currently empty) Could potentially hold simple UI components for displaying a category chip or link if needed across multiple features/widgets.

## Usage

*   **Fetching Categories:** `useGetCategoryTreeQuery` is used by catalog-related components (`widgets/catalog-accordion`, `pages/Catalog/...`) to display category navigation and hierarchy. It's also used by `widgets/breadcrumbs` to construct the navigation trail on catalog pages.
*   **Filtering/Display:** Components like `widgets/category-switcher` use category data (often derived from other API responses like favorites or comparison) to allow users to filter lists by category.
*   **Type Usage:** `CategoryBase` and related types are used within the `Product` entity and wherever category information is displayed or processed.

## Related Entities/Widgets

*   `entities/product`: Products belong to a category.
*   `widgets/catalog-accordion`: Displays the category tree fetched by this entity's API.
*   `widgets/breadcrumbs`: Uses the category chain from the API response.
*   `widgets/category-switcher`: Displays category filters.

## Migration Notes

*   Ensure consistent use of the defined Category types throughout the application.
*   Consider adding specific RTK Query tags for category data if more granular cache invalidation becomes necessary.
