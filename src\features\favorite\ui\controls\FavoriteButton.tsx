import { Icon } from '@iconify-icon/react';

import { useFavorites } from '../../model/hooks/useFavorites';

import type { Product } from '@/entities/product';

interface FavoriteButtonProps {
  product: Product;
  className?: string;
}

export const FavoriteButton = ({
  product,
  className = '',
}: FavoriteButtonProps) => {
  const { isInFavorites, isLoading, handleFavoriteClick } = useFavorites();

  // Add null check for product
  if (!product) {
    return null;
  }

  const isInFavorite = isInFavorites(product.id);

  return (
    <button
      onClick={(e) => handleFavoriteClick(product, e)}
      disabled={isLoading}
      className={` ${isLoading ? 'cursor-wait' : 'cursor-pointer'} ${className}`}
    >
      {isInFavorite ? (
        <Icon
          icon="solar:heart-bold"
          width={24}
          height={24}
          className="icon-btn-icon"
        />
      ) : (
        <Icon
          icon="solar:heart-outline"
          width={24}
          height={24}
          className="icon-btn-icon"
        />
      )}
    </button>
  );
};
