// src/components/ProductPage/Mobile/MobileProductInfo/MobileFiles.tsx
import React from 'react';
import { File } from '@/components/ProductPage/ProductTabs/File';

interface ProductFileEntry {
  id: string | number; // Or a more specific type if available
  name: string;
  url?: string; // Or path, for actual download link
  // other properties like fileType, size, etc.
}

interface ProductData {
  files?: ProductFileEntry[];
  // other product properties
}

interface MobileFilesProps {
  product: ProductData;
}

const MobileFiles: React.FC<MobileFilesProps> = ({ product }) => {
  return (
    <>
      <h3 className='text-2xl mt-5 mb-3 font-semibold'>Файлы для скачивания</h3> {/* Added mb-3 for spacing */}
      {
        product?.files && product.files.length > 0 ? (
          <div className='flex flex-col gap-4'> {/* Changed to flex-col and gap-4 for better list presentation */}
            {product.files.map((file) => (
              <File key={file.id} file={file} />
            ))}
          </div>
        ) : (
          <div className='text-base mt-2 text-colDarkGray'>У данного товара нет файлов для скачивания</div> /* Adjusted styling */
        )
      }
    </>
  );
};

export default MobileFiles;
