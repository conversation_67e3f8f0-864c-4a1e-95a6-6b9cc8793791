# Auth & Sync Logic Rework Plan

## 1. Initial State & Problem Description

*   **Authentication:**
    *   Client-side JWT token management: Token stored in browser cookies.
    *   <PERSON><PERSON> manually read from cookies and added to `Authorization: Bearer <token>` header for authenticated API requests in `src/shared/api/api.ts`.
    *   `src/entities/user/model/userSlice.ts` handles setting/removing token in cookies (`setTokenInCookies`, `removeTokenFromCookies`) and Redux state.
    *   `isAuthenticated` status in Redux and `AuthContext` primarily derived from the presence of this client-managed token.
*   **Data Synchronization (`SyncProvider` and Entity Hooks):**
    *   `SyncProvider` (`src/app/providers/SyncProvider/SyncProvider.tsx`) orchestrates the overall synchronization logic.
    *   Core data management for cart, favorites, and comparison is primarily handled by custom hooks:
        *   `useCart` (from `src/features/cart/model/hooks/useCart.ts`)
        *   `useFavorites` (from `src/features/favorite/model/hooks/useFavorites.ts`)
        *   `useComparison` (from `src/features/comparison/model/hooks/useComparison.ts`)
    *   These hooks encapsulate logic for:
        *   Loading data from local storage (primarily for guests in the old model, or as a fallback).
        *   Refreshing data from server endpoints.
        *   Clearing local data.
        *   Syncing local data to the server (e.g., guest cart to user's cart on login).
    *   `SyncProvider` invokes methods exposed by these hooks (e.g., `loadFromStorage`, `refreshFromServer`, `syncToServer`, `clearData`).
    *   User-specific data (`userData`) is fetched via `useGetUserDataQuery` (from `src/entities/user/api/userApi.ts`).
    *   **Initial State Behavior (Old Model):**
        *   `SyncProvider` would check the `isAuthenticated` flag (derived from the client-side token in `userSlice` / `AuthContext`).
        *   If `isAuthenticated` was true: It would trigger `refreshFromServer` methods for cart, favorites, comparison, and `useGetUserDataQuery` would fetch user data.
        *   If `isAuthenticated` was false (guest): It would trigger `loadFromStorage` methods for cart, favorites, comparison.
    *   **Login/Logout Behavior (Old Model):**
        *   On login: `SyncProvider` would typically call `syncToServer` methods (from the entity hooks) to upload local guest data, followed by `refreshFromServer` methods for all data.
        *   On logout: `SyncProvider` would call `clearData` methods and then potentially `loadFromStorage` to revert to a guest state.
*   **Protected Routes (`ProtectedRoute`):
    *   Relies on `isAuthenticated` from `AuthContext` (which depends on client-side token).

## 2. Desired State & Goals

*   **Server-Managed Sessions (HTTP-only Cookies):
    *   The server will be solely responsible for setting and managing an HTTP-only session cookie upon successful login.
    *   The client will no longer read, write, or store the token in its own JavaScript-accessible cookies or local storage for authentication headers.
    *   All API requests from the client will include `credentials: 'include'` to automatically send the server-set cookie.
    *   The client will remove all logic for manually adding `Authorization` headers.
*   **Unified Data Synchronization (`SyncProvider`):
    *   On app initialization: `SyncProvider` will *always* attempt to fetch user data, cart, favorites, and comparison data from the server.
        *   The server, using the HTTP-only cookie, will determine if the request comes from a known user or a new visitor.
        *   The server's response will contain the appropriate data (user-specific or default/empty for a new visitor).
        *   Client-side loading from local storage *during initial app load* will be removed. Local storage might still be used for guest data persistence *between sessions* if the server doesn't persist guest carts/favs, but the initial source of truth is the server.
    *   On login: After the server sets the session cookie, `SyncProvider` will trigger a full refresh of all relevant data (user, cart, favorites, comparison) from the server. The server should handle any merging of pre-login (guest) data with the authenticated user's data if applicable.
    *   On logout: The client will call a server logout endpoint. The server will invalidate its session/cookie. The client will then clear local user-specific state and fetch/load the guest state (which might be an empty state from the server or potentially from local storage if that's the desired guest UX between sessions).
*   **Authentication State (`AuthContext`, `userSlice`):
    *   `isAuthenticated` status will be derived primarily from the presence of `userData` in the Redux store, which is populated from server responses (via `useGetUserDataQuery`) when a valid session cookie is present.
*   **Protected Routes (`ProtectedRoute`):
    *   Will rely on the updated `isAuthenticated` status from `AuthContext`.
    *   Access decisions will ultimately be enforced by the server based on the session cookie.

## 3. Changes Made So Far (as of 2025-06-16)

1.  **`src/shared/api/api.ts`**:
    *   Commented out the manual setting of the `Authorization: Bearer <token>` header in `prepareHeaders`. The `credentials: 'include'` option remains active in `fetchBaseQuery`.

2.  **`src/entities/user/model/userSlice.ts`**:
    *   Commented out client-side cookie manipulation functions (`setTokenInCookies`, `removeTokenFromCookies`) within the `setToken` and `logout` reducers. The token is still managed in the Redux state for now, but not written to/read from JS-accessible cookies by these functions.

3.  **`src/app/providers/SyncProvider/SyncProvider.tsx`**:
    *   **Initial Data Load Logic (Main Change):**
        *   The `useEffect` hook for initial data loading (when `authInitialized` is true) has been modified to *always* attempt fetching data from the server (`refreshCartFromServer`, `refreshFavoritesFromServer`, `refreshComparisonFromServer`).
        *   The previous `else` block that loaded data from local storage for guests *during this initial load phase* has been commented out.
        *   `useGetUserDataQuery` is still used for user data, and its `skip` condition (`!authInitialized || !isAuthenticated`) means it will only run if `isAuthenticated` becomes true (e.g., if `AuthContext` updates it based on a successful server response to an earlier request).
    *   **`syncOnLogin` Function:**
        *   Retains the logic to first upload local guest data (cart, favorites, comparison) to the server.
        *   Then, it refreshes all data (cart, favorites, comparison) from the server. User data fetching is handled by `useGetUserDataQuery` based on `isAuthenticated` state.
    *   **`syncOnLogout` Function:**
        *   Clears local cart, favorites, and comparison data using `clearData` methods from the respective hooks.
        *   Resets `isInitialized` to `false` and `initOnceRef.current` to `false`.

4.  **Removed `mainSync` approach**: Previously explored using a single `useMainSyncQuery` endpoint but reverted to using individual data fetching hooks (`useGetUserDataQuery`, `useCart`, `useFavorites`, `useComparison`) as per user preference.

## 4. Next Steps & Remaining Tasks

*   **Thorough Testing of Current State:**
    *   Verify initial app load for new visitors (server should provide guest state or empty data for cart/favs/comp).
    *   Verify initial app load for returning users (server validates cookie, returns user data and associated cart/favs/comp).
    *   Verify login flow (server sets cookie, client syncs all data).
    *   Verify logout flow (server clears cookie, client clears user state, loads guest state).
    *   Monitor console logs and network requests (no manual `Authorization` headers, correct API calls, `credentials: 'include'` is working).
*   **Refactor `AuthContext.tsx`**:
    *   Derive `isAuthenticated` primarily from the presence of `userData` in Redux (populated by server responses) rather than a client-side token.
    *   Ensure `authenticateWithPhone` (login mutation) correctly triggers server cookie setting and then the `syncOnLogin` process in `SyncProvider`.
    *   Create and integrate `useLogoutMutation` for calling a server logout endpoint. On success, dispatch `logoutAction` from `userSlice` and call `syncOnLogout` from `SyncProvider`.
*   **Refactor `ProtectedRoute.tsx`**:
    *   Update to rely on the new `isAuthenticated` logic from `AuthContext`.
    *   Consider if a proactive auth status check (`useCheckAuthStatusQuery`) is needed or if relying on API call failures (401/403) within protected components is sufficient to trigger auth modals/redirects.
*   **Server-Side Implementation:** Ensure the backend correctly handles HTTP-only cookies for session management, provides necessary endpoints (login, logout, data fetches that respect session cookies), and manages guest vs. authenticated user data appropriately.
*   **Cleanup:** Remove any remaining dead code related to old token management once the new system is stable.
