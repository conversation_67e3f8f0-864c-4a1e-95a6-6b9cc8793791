import { createSlice } from '@reduxjs/toolkit';

interface UiState {
  isCatalogVisible: boolean;
}

const initialState: UiState = {
  isCatalogVisible: false,
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleCatalog(state) {
      state.isCatalogVisible = !state.isCatalogVisible;
    },
    openCatalog(state) {
      state.isCatalogVisible = true;
    },
    closeCatalog(state) {
      state.isCatalogVisible = false;
    },
  },
});

export const { toggleCatalog, openCatalog, closeCatalog } = uiSlice.actions;
export default uiSlice.reducer;
