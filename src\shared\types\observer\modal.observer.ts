import { TreeNode } from '@/types/CategorieTree';
import { ProductFilter, ProductFilterMultiple } from '@/types/Filters/ProductFilter';

type ModalTypes = 'item' | 'all' | 'category' | 'search' | 'explore';

interface BaseModal {
  type: ModalTypes;
}

export interface FilterItemModal extends BaseModal {
  type: 'item';
  data: ProductFilterMultiple;
  onAccept: (data: any) => void;
  onClose: () => void;
}

export interface FilterAllModal extends BaseModal {
  type: 'all';
  data: ProductFilter[];
  onCancel: () => void;
  onAccept: (data: ProductFilter[], params: string[]) => void;
}
export interface FilterAllExplorModal extends BaseModal {
  type: 'explore';
  data: ProductFilter[];
  onCancel: () => void;
  onAccept: (data: ProductFilter[], params: string[]) => void;
}
export interface FilterSearchAllModal extends BaseModal {
  type: 'search';
  data: ProductFilter[];
  onCancel: () => void;
  onAccept: (data: ProductFilter[], params: string[]) => void;
}

export interface FilterCategoryModal extends BaseModal {
  type: 'category';
  data: TreeNode;
  onCancel: () => void;
}

type ModalMap = {
  all: FilterAllModal;
  explore: FilterAllExplorModal;
  item: FilterItemModal;
  category: FilterCategoryModal;
  search: FilterSearchAllModal;
};

export type ModalFilterData = ModalMap[keyof ModalMap];
export type ModalFilterItem = ModalMap['item'];
export type ModalFilterAll = ModalMap['all'];
export type ModalExploreFilterAll = ModalMap['explore'];
export type ModalFilterSearchAll = ModalMap['search'];
export type ModalFilterCategory = ModalMap['category'];
