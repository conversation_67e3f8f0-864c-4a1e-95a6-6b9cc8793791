import React from 'react';
import { CustomRadioButton } from './CustomRadioButton';
import type { VariantPay } from '@/entities/order/api/types';

interface PaymentMethodSelectorProps {
  methods: VariantPay[] | undefined;
  selectedMethod: string | undefined;
  onSelectMethod: (methodCode: string) => void;
}

export const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  methods,
  selectedMethod,
  onSelectMethod,
}) => {
  if (!methods || methods.length === 0) {
    return <p>Способы оплаты не найдены.</p>;
  }

  return (
    <div>
      <h3 className="font-semibold text-center text-2xl mb-4">Способ оплаты</h3>
      <div className="space-y-3">
        {methods.map((method) => (
          <CustomRadioButton
          method={method}
            key={method.code}
            id={`payment-${method.code}`}
            name="payment_method"
            value={method.code}
            checked={selectedMethod === method.code}
            onChange={(e) => onSelectMethod(e.target.value)}
            label={method.name}
          />
        ))}
      </div>
    </div>
  );
};
