# Widget: Header

## Purpose

This widget represents the main application header displayed across most pages. It provides primary navigation, branding, access to core features like search and catalog, and user-specific controls (cart, favorites, profile/login).

## Composition

The Header widget assembles components and data from various layers:

*   **Entities:** Displays user name (from `user` entity), counts for cart, favorites, comparison (derived from `cart`, `favorite`, `comparison` entities/features).
*   **Features:** Integrates the `SearchBar` feature (`features/search`). Includes buttons/icons triggering modals from `features/modals` (e.g., login/logout, potentially city selection). Integrates cart, favorite, and comparison buttons from their respective features.
*   **Shared UI:** Uses `NavLink` for routing, potentially shared icons and layout utilities.
*   **Widgets:** May be composed of sub-widgets like `PreHeader` and `MobileTabbar`.

## Key UI Components (`ui/`)

*   **`Header.tsx`:** The main component orchestrating the desktop header layout. Manages the state for the Catalog modal visibility.
*   **`PreHeader.tsx`:** Renders the top bar with secondary navigation links (Payment, Warranty, Contacts, etc.) and potentially city selection.
*   **`CatalogFastAccess.tsx`:** Displays a horizontally scrolling list of top categories and tags below the main header bar.
*   **`HeaderControls/`:**
    *   `HeaderControls.tsx`: Container for the right-aligned user action icons (Orders, Comparison, Favorites, Cart, Profile/Login).
    *   `CartButton.tsx`: Icon and count for the cart, potentially with a mini-cart dropdown on hover.
    *   `LoginButton.tsx`: Displays the "Login" button/icon when the user is not authenticated, triggers the auth modal.
    *   `ProfileButton.tsx`: Displays the user's name and profile icon when authenticated, links to the profile page, potentially with a dropdown menu.
*   **`MobileTabbar.tsx`:** The bottom navigation bar for mobile devices, providing quick access to Home, Catalog, Favorites, Profile, and Cart.

## Logic & State (`lib/`)

*   **`hooks/useQuantities.ts`:** A custom hook to conveniently retrieve the current counts for cart items, favorites, and comparison items from the Redux store. Used by `HeaderControls` and `MobileTabbar` to display badges.
*   The `Header` component itself manages the `showCatalog` state to toggle the main catalog modal.

## Usage

*   The `Header` widget (along with `MobileTabbar`) is typically rendered once within the main application `Layout` component (`src/app/layouts/Layout.tsx`).