import { createSlice } from '@reduxjs/toolkit';

import type { SearchState } from './types';
import type { PayloadAction } from '@reduxjs/toolkit';

const initialState: SearchState = {
  searchTerm: '',
  recentSearches: [],
  suggestions: {
    products: [],
    categories: [],
  },
  isSuggestionsOpen: false,
  isLoading: false,
  error: null,
  selectedCategoryId: null,
};

export const searchSlice = createSlice({
  name: 'search',
  initialState,
  reducers: {
    setSearchTerm: (state, action: PayloadAction<string>) => {
      state.searchTerm = action.payload;
    },

    clearSearchTerm: (state) => {
      state.searchTerm = '';
    },

    setRecentSearches: (state, action: PayloadAction<string[]>) => {
      state.recentSearches = action.payload;
    },

    addRecentSearch: (state, action: PayloadAction<string>) => {
      // Avoid duplicates
      if (!state.recentSearches.includes(action.payload)) {
        // Keep only the 10 most recent searches
        state.recentSearches = [
          action.payload,
          ...state.recentSearches.filter((s) => s !== action.payload),
        ].slice(0, 10);
      }
    },

    clearRecentSearches: (state) => {
      state.recentSearches = [];
    },

    setSuggestions: (
      state,
      action: PayloadAction<{
        products?: any[];
        categories?: any[];
      }>
    ) => {
      if (action.payload.products) {
        state.suggestions.products = action.payload.products;
      }
      if (action.payload.categories) {
        state.suggestions.categories = action.payload.categories;
      }
    },

    clearSuggestions: (state) => {
      state.suggestions = {
        products: [],
        categories: [],
      };
    },

    setIsSuggestionsOpen: (state, action: PayloadAction<boolean>) => {
      state.isSuggestionsOpen = action.payload;
    },

    setIsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    setSelectedCategoryId: (state, action: PayloadAction<string | null>) => {
      state.selectedCategoryId = action.payload;
    },

    resetSearch: (state) => {
      return {
        ...initialState,
        recentSearches: state.recentSearches, // Keep recent searches
      };
    },
  },
});

export const {
  setSearchTerm,
  clearSearchTerm,
  setRecentSearches,
  addRecentSearch,
  clearRecentSearches,
  setSuggestions,
  clearSuggestions,
  setIsSuggestionsOpen,
  setIsLoading,
  setError,
  setSelectedCategoryId,
  resetSearch,
} = searchSlice.actions;

export default searchSlice.reducer;
