import React from 'react';
import { cn } from '@/shared/lib/utils';
import type { Product } from '@/entities/product/types';

interface ProductTagsProps {
  product: Product;
  className?: string;
  limit?: number;
  variant?: 'default' | 'small' | 'large';
}

/**
 * Component for displaying product tags
 * 
 * @param product - The product object containing tags
 * @param className - Optional additional classes for styling
 * @param limit - Optional limit of tags to display (default: show all)
 * @param variant - Optional size variant (default: 'default')
 */
export const ProductTags: React.FC<ProductTagsProps> = ({
  product,
  className,
  limit,
  variant = 'default',
}) => {
  // Return null if product has no tags or tags is false
  if (!product?.tags || product.tags === false) {
    return null;
  }

  // Determine size variant classes
  const variantClasses = {
    small: 'py-0.5 px-1 text-[8px] rounded-sm',
    default: 'py-1 px-1.5 text-xs rounded-sm',
    large: 'py-1.5 px-2.5 text-sm rounded-sm'
  };

  // Limit the number of tags to display if specified
  const tagsToShow = limit ? product.tags.slice(0, limit) : product.tags;
  
  return (
    <div className={cn('flex flex-wrap gap-1', className)}>
      {tagsToShow.map((tag, index) => (

        <div
          key={`${tag.tag}-${index}`}
          style={{
            color: tag.text_color,
            backgroundColor: tag.background_color,
          }}
          className={cn(
            'flex uppercase font-semibold',
            variantClasses[variant]
          )}
        >
          {tag?.light_icon && <img src={tag?.light_icon?.url} alt={tag.text} className="w-4 h-4 mr-1" />}
          {tag.tag}
        </div>
      ))}
    </div>
  );
};
