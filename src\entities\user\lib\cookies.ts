// src/entities/user/lib/cookies.ts
/**
 * Token storage constants and utilities
 */

// Use our own token for simpler handling
export const FURNICA_TOKEN_KEY = 'furnica_token';

/**
 * Get the authentication token from cookies
 * @returns The token string or null if not found
 */
export const getTokenFromCookies = (): string | null => {
  return document.cookie.replace(
    new RegExp(`(?:(?:^|.*;\\s*)${FURNICA_TOKEN_KEY}\\s*\\=\\s*([^;]*).*$)|^.*$`),
    '$1'
  ) || null;
};

/**
 * Set token in cookies
 * @param token - The token string to store
 * @param days - Number of days until expiration (default: 7)
 */
export const setTokenInCookies = (token: string, days: number = 7): void => {
  const date = new Date();
  date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
  const expires = `expires=${date.toUTCString()}`;
  document.cookie = `${FURNICA_TOKEN_KEY}=${token};${expires};path=/`;
};

/**
 * Remove token from cookies
 */
export const removeTokenFromCookies = (): void => {
  document.cookie = `${FURNICA_TOKEN_KEY}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
};

/**
 * Check if token exists in cookies
 * @returns boolean indicating if token exists
 */
export const hasTokenInCookies = (): boolean => {
  return !!getTokenFromCookies();
};

// Keep these commented but available if needed
/*
export const GX_SESSION_KEY = 'gx_session';

export const hasGxSessionCookie = (): boolean => {
  return document.cookie.includes(`${GX_SESSION_KEY}=`);
};
*/
