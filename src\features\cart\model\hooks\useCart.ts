import { useCallback, useEffect, useRef } from 'react';

import { useDispatch, useSelector } from 'react-redux';

import { useAuthContext } from '@/entities/user/model/AuthContext';
import { setCart, useLazyGetUserCartQuery, useSendCartMutation } from '@/features/cart'; // MODIFIED import
import { getFromSessionStorage, saveToSessionStorage } from '@/features/storage/lib';

import type { RootState } from '@/app/providers/store';
import type { LocalCartState } from '@/features/cart';

// Initial empty cart state for resets
const initialCartState: LocalCartState = {
  cart: [],
  total: {
    items_count: 0,
    quantity: 0,
    price_before_discount: 0,
    discount: 0,
    price_after_discount: 0
  },
  selected: {
    items_count: 0,
    quantity: 0,
    price_before_discount: 0,
    discount: 0,
    price_after_discount: 0
  },
  currency: {
    code: 'RUB',
    title: 'Рубль',
    symbol: '₽'
  }
};

/**
 * Enhanced hook for accessing and managing cart data throughout the application.
 * This is the single source of truth for cart data.
 *
 * Features:
 * - <PERSON>les both authenticated and unauthenticated states
 * - Provides access to cart data from Redux store
 * - Manages cart data fetching with optimized caching
 * - Provides helper methods for finding items in cart
 * - Includes methods for syncing with server and storage
 */
export const useCart = () => {
  const { isAuthenticated } = useAuthContext();
  const localCart = useSelector((state: RootState) => state.cart);
  const dispatch = useDispatch();
  const hasLoadedFromStorageRef = useRef(false);

  // Only fetch cart data from server on initial load or when explicitly needed
  // The skip parameter ensures we don't fetch if not authenticated
  // MODIFIED: Use the lazy query hook
  const [
    triggerGetUserCart, // This is the function to call to execute the query
    { isLoading, error, data: userCartDataFromServer } // Result object. Note: `data` is destructured here for clarity if needed later, but `result.data` will be used in refreshFromServer.
  ] = useLazyGetUserCartQuery();

  // API mutation hook for sending cart updates to server
  const [sendCart] = useSendCartMutation();

  // Helper function to find a product in the cart by ID
  const findCartItem = useCallback(
    (productId: number) => {
      return localCart?.cart?.find((item) => item.id === productId) || null;
    },
    [localCart?.cart]
  );

  // Helper function to check if a product is in the cart
  const isInCart = useCallback(
    (productId: number) => {
      return !!findCartItem(productId);
    },
    [findCartItem]
  );

  // Force refresh cart data from server (use sparingly)
  const refreshFromServer = useCallback(async () => {
    try {
      console.log('[useCart] refreshFromServer called. Attempting triggerGetUserCart...');
      // MODIFIED: Call the trigger function. It takes an optional argument (void here).
      const result = await triggerGetUserCart(undefined, false); // `false` means don't prefer cache for this explicit fetch
      console.log('[useCart] triggerGetUserCart result:', result);

      if (result.data) {
        const transformedData = {
          cart: result.data.data,
          total: result.data.total,
          selected: result.data.selected,
          currency: result.data.current_currency
        };
        dispatch(setCart(transformedData));
        console.log('[useCart] Cart data updated in Redux from server.');
      } else if (result.isError) {
        console.error('[useCart] Error in triggerGetUserCart result:', result.error);
      }
      return result; // Return the whole result object
    } catch (error) {
      console.error('[useCart] Error fetching from server in refreshFromServer:', error);
      throw error;
    }
  }, [triggerGetUserCart, dispatch]);

  // Send current cart to server
  const syncToServer = useCallback(
    async (cartItems = localCart?.cart) => {
      if (!isAuthenticated || !cartItems?.length) {
        return Promise.resolve();
      }

      try {
        const payload = cartItems.map((item) => ({
          id: item.id,
          quantity: item.quantity,
          selected: !!item.selected // Convert to boolean
        }));

        return await sendCart({ items: payload });
      } catch (error) {
        console.error('[useCart] Error syncing cart to server:', error);
        return Promise.reject(error);
      }
    },
    [isAuthenticated, localCart?.cart, sendCart]
  );

  // Load cart data from sessionStorage
  const loadFromStorage = useCallback(() => {
    try {
      const storedCart = getFromSessionStorage('cart') as LocalCartState | null;
      if (storedCart) {
        dispatch(setCart(storedCart));
      }
      hasLoadedFromStorageRef.current = true;
    } catch (error) {
      console.error('[useCart] Error loading from storage:', error);
    }
  }, [dispatch]);

  // Save cart data to sessionStorage
  const saveToStorage = useCallback(
    (cartData = localCart) => {
      try {
        if (!isAuthenticated && cartData) {
          saveToSessionStorage('cart', cartData);
        }
      } catch (error) {
        console.error('[useCart] Error saving to storage:', error);
      }
    },
    [isAuthenticated] // Remove localCart from dependencies to prevent infinite loop
  );

  // Clear cart data in Redux and sessionStorage
  const clearData = useCallback(() => {
    dispatch(setCart(initialCartState));
    sessionStorage.removeItem('cart');
  }, [dispatch]);

  // Auto-save to storage when cart changes (for non-authenticated users)
  // Only save after initial load to prevent overwriting stored data
  useEffect(() => {
    // Only auto-save if:
    // 1. Not authenticated (local storage mode)
    // 2. Has loaded from storage at least once (prevents overwriting on initial load)
    // 3. Has cart data
    if (!isAuthenticated && hasLoadedFromStorageRef.current && localCart) {
      saveToStorage(localCart);
    }
  }, [localCart, isAuthenticated, saveToStorage]);

  // Log cart state changes for debugging
  useEffect(() => {}, [localCart, isAuthenticated]);

  // The returned `isLoading` and `isError` should now come from the lazy query's result object.
  return {
    cart: localCart, // This is still the Redux state
    isLoading, // From useLazyGetUserCartQuery result
    isError: !!error, // From useLazyGetUserCartQuery result
    findCartItem,
    isInCart,
    refreshCart: refreshFromServer,
    refreshFromServer,
    syncToServer,
    loadFromStorage,
    saveToStorage,
    clearData,
    initialCartState
  };
};
