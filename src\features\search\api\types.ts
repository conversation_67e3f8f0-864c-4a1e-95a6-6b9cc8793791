import type { GetVariantsResponse, GetVariantsRequest } from '@/entities/product/api/types';
import type { GetFiltersResponse, GetFiltersRequest } from '@/entities/filter/api/types';

// Interface for search suggestions response
export interface GetSearchSuggestionsResponse {
  variants?: any[];
  categories?: any[];
  history?: any[];
}

// Interface for search suggestions request
export interface GetSearchSuggestionsRequest {
  text: string;
  limit?: number;
}

// Interface for search corrections response
export interface SearchCorrectionItem {
  word: string;
  action: 'append' | 'replace';
}

export interface GetSearchCorrectionsResponse {
  success: boolean;
  data: SearchCorrectionItem[];
  total_request_time?: number;
  api_processing_time?: number;
  sessid?: string;
}

// Export all types from the file
export type { GetVariantsResponse, GetVariantsRequest, GetFiltersResponse, GetFiltersRequest };
