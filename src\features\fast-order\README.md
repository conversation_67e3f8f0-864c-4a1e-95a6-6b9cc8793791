# Feature: Fast Order (Buy in 1 Click)

## Purpose

This feature provides a streamlined checkout process, allowing users to quickly place an order for a single product variant by providing only essential contact information (name and phone).

## Key Functionality

*   **Simplified Order:** Creates an order with minimal user input directly from a product page or similar context.
*   **UI:** Presents a modal (`FastOrderModal`) to collect the user's name and phone number.
*   **API Interaction:** Sends the collected information along with the product variant ID to a dedicated "one-click order" backend endpoint.

## Components (`ui/`)

*   **`FastOrderModal.tsx`:** The modal dialog containing the form for name and phone input. It handles form submission and API interaction.

## State & Logic

*   The feature primarily manages local form state using `react-hook-form` within the `FastOrderModal`.
*   It relies on the `modals` feature (`useModal`) to be displayed.
*   It receives the `product` object as context via `modalContent` when shown.

## API Integration (`api/`)

*   **`fastOrderApi.ts`:** Defines the RTK Query endpoint:
    *   `oneClickOrder` (Mutation): Sends the user's name, phone number, and the target `item_id` (product variant ID) to the `/api/ProductOrders/oneClick` backend endpoint. Invalidates `Cart` and `Order` list tags upon success. Includes `transformErrorResponse` to provide user-friendly messages for specific backend error codes related to this flow.

## Usage

1.  A "Buy in 1 Click" button (typically on the product page `src/components/ProductPage/RightBar.tsx`) triggers the modal via `useModal`.
    ```tsx
    showModal({ type: 'fastOrder', product: currentProduct });
    ```
2.  The `FastOrderModal` is displayed via the `ModalManager`.
3.  The user enters their name and phone number.
4.  Upon form submission, the modal calls the `useOneClickOrderMutation` hook with the form data and the product ID.
5.  On success, a toast notification is shown, and the modal closes. On failure, an error toast is shown (potentially customized by `transformErrorResponse`).

## Related Features/Entities

*   `features/modals`: Manages the display of the `FastOrderModal`.
*   `entities/product`: The feature operates on a specific product variant.
*   `entities/order`: Successfully placing a fast order creates a new order and potentially invalidates the user's order list.
