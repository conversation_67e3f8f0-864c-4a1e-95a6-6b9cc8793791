import { ProductFilterURLInput } from '@/components/Catalog/CatalogRoot';
import { Range } from '@/features/range';
import { ProductFilterRange } from '@/types/Filters/ProductFilter';
import { useEffect, useState } from 'react';

interface FilterOptionColorProps {
  data: ProductFilterRange;
  onChange: (data: ProductFilterURLInput) => void;
  disabled?: boolean;
}

export const FilterOptionRange: React.FC<FilterOptionColorProps> = ({
  data,
  onChange,
  disabled
}) => {
  const [value, setValue] = useState<[number, number]>([data.current.from, data.current.to]);

  const handleChange = (values: [number, number]) => {
    setValue(values);
    onChange({
      alone: {
        parentId: data.id,
        type: 'range',
        value: values,
        title: data.name
      }
    });
  };

  useEffect(() => {
    setValue([data.current.from, data.current.to]);
  }, [data.current]);

  return (
    <div className='flex flex-col gap-[4px] mt-2 overflow-hidden'>
      <Range
        max={data.limits.max}
        min={data.limits.min}
        onValueCommit={handleChange}
        defaultValue={value}
        disabled={disabled}
      >
        <Range.Inputs />
        <Range.Sliders />
      </Range>
    </div>
  );
};
