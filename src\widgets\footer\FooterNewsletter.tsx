// src/widgets/footer/FooterNewsletter.tsx (Temporary location)
import React, { useState } from 'react';
import { NavLink } from 'react-router-dom';

export const FooterNewsletter: React.FC = () => {
  const [privacyPolicy, setPrivacyPolicy] = useState(true);
  // TODO: Implement newsletter subscription logic (e.g., form state, API call)
  return (
    <form className='pt-7 md:pt-10'>
      <h2 className='text-colBlack text-xl sm:text-2xl font-semibold pb-3'>
        Подпишитесь на рассылку
      </h2>
      <div className='sm:flex'>
        <input
          className='border border-[#B5B5B5] h-[38px] sm:max-w-[340px] w-full bg-white px-3 rounded outline-none'
          type='email' // Changed type to email for better semantics
          placeholder='Email'
          aria-label='Email for newsletter'
        />
        <button 
          type='submit' // Added type submit
          className='bg-colGreen text-white h-[38px] sm:max-w-[180px] w-full rounded mt-2 sm:mt-0 sm:ml-2 hover:bg-colGreenHover transition-colors'
        >
          Подписаться
        </button>
      </div>
      <div className='flex items-center pt-3'>
        <input
          checked={privacyPolicy}
          onChange={() => setPrivacyPolicy(!privacyPolicy)}
          className='w-5 h-5 mr-2 cursor-pointer'
          type='checkbox'
          name='privacyPolicyNewsletter'
          id='privacyPolicyNewsletter' // Ensure unique ID if Footer.tsx still has one
        />
        <label
          htmlFor='privacyPolicyNewsletter'
          className='text-colDarkGray text-xs cursor-pointer'
        >
          Я согласен на обработку моих персональных данных в соответствии
          с <NavLink to="/terms" className="underline hover:text-colGreen">Условиями</NavLink>
        </label>
      </div>
    </form>
  );
};
