// src/components/ProductPage/ProductTabs/CharactersticsTab.tsx
import React from 'react';

interface AttributeValue {
  id: string | number;
  text: string;
  // other properties...
}

interface Attribute {
  id: string | number;
  name: string;
  values?: AttributeValue[]; // Typically for group attributes, e.g., [{ id: 'val1', text: 'Value 1' }]
  text?: string;             // Typically for current (variant-specific) attributes, representing the direct value
  // other properties...
}

interface ProductVariant {
  id: string | number;
  sku?: string;
  description?: string;
  attributes?: Attribute[]; // Variant attributes might be { id, name, text }
  [key: string]: any;
}

interface ProductGroup {
  id: string | number;
  attributes?: Attribute[]; // Group attributes might be { id, name, values: [AttributeValue] }
  [key: string]: any;
}

interface CharactersticsTabProps {
  current?: ProductVariant | null;
  group?: ProductGroup | null;
}

interface DisplayAttribute {
  id: string | number;
  name: string;
  value: string;
}

const CharactersticsTab: React.FC<CharactersticsTabProps> = ({ current, group }) => {
  const attributesToDisplay: DisplayAttribute[] = [];

  const processedGroupAttrIds = new Set<string | number>();

  // Process group attributes, applying variant overrides
  group?.attributes?.forEach(groupAttr => {
    processedGroupAttrIds.add(groupAttr.id);
    const variantAttribute = current?.attributes?.find(attr => String(attr.id) === String(groupAttr.id));
    const valueToDisplay = variantAttribute?.text ?? groupAttr.values?.[0]?.text;

    if (valueToDisplay) {
      attributesToDisplay.push({
        id: groupAttr.id,
        name: groupAttr.name,
        value: valueToDisplay,
      });
    }
  });

  // Add attributes that are only in current (variant-specific) and not in group
  current?.attributes?.forEach(currentAttr => {
    if (!processedGroupAttrIds.has(currentAttr.id) && currentAttr.text) {
      attributesToDisplay.push({
        id: currentAttr.id,
        name: currentAttr.name, // Assuming currentAttr also has a name property
        value: currentAttr.text,
      });
    }
  });

  return (
    <>
      <h3 className='text-2xl my-5 font-semibold'>Характеристики</h3>

      <div className='flex flex-wrap flex-row gap-x-[20px] gap-y-[10px]'>
        {current?.sku && (
          <div className='flex items-end basis-[calc(50%-10px)] min-w-[250px] md:basis-[calc(33.333%-14px)] lg:basis-[calc(50%-10px)]'>
            <div className='shrink leading-none text-colDarkGray mr-1'>Код товара</div>
            <div className='grow border-b-2 border-dotted'></div>
            <div className='flex items-end leading-none shrink ml-1'>
              {current.sku}
            </div>
          </div>
        )}
        {attributesToDisplay.map((attribute) => (
          <div key={attribute.id} className='flex items-end basis-[calc(50%-10px)] min-w-[250px] md:basis-[calc(33.333%-14px)] lg:basis-[calc(50%-10px)]'>
            <div className='shrink self-start leading-none text-colDarkGray mr-1'>{attribute.name}</div>
            <div className='grow self-start h-4 border-b-2 border-dotted'></div>
            <div className='flex text-end leading-none shrink ml-1 max-w-[calc(50%-5px)] break-words'>
              {attribute.value}
            </div>
          </div>
        ))}
      </div>

      {current?.description && (
        <>
          <h3 className='text-2xl mt-10 mb-[10px] font-semibold'>Описание</h3>
          <div className='text-[14px] whitespace-pre-wrap leading-relaxed'>
            {current.description}
          </div>
        </>
      )}
    </>
  );
};

export default CharactersticsTab;
