import { Search } from '@/shared/ui';
import { useProductFilterOptionAlone } from './hook/useProductFilterOptionAlone';
import { FilterOptionColor } from './ui/Options/FilterOptionColor';
import { FilterOptionText } from './ui/Options/FilterOptionText';

interface ProductFIlterOptionsAloneProps {}

export const ProductFilterOptionsAlone: React.FC<ProductFIlterOptionsAloneProps> = () => {
  const { onChange, filtered, handleFilter } = useProductFilterOptionAlone();

  const multiple = () => {
    switch (filtered.type) {
      case 'color': {
        return <FilterOptionColor data={filtered} onChange={onChange} />;
      }
      case 'text': {
        return <FilterOptionText data={filtered} onChange={onChange} />;
      }
    }
  };

  return (
    <div className='flex flex-col gap-2 h-full'>
      <div>
        <span className='flex items-center border-[2px] mt-2 focus-within:border-colGreen rounded-[12px] px-2 py-2 gap-[4px] bg-[#E6E9E8]'>
          <Search size={16} color='#5F6664' />
          <input
            type='text'
            placeholder='Поиск'
            className='focus:outline-none text-[14px] bg-transparent placeholder:text-[#5F6664] flex-1'
            onChange={handleFilter}
          />
        </span>
      </div>
      <div className='h-full min-h-0 max-h-[calc(90vh_-_172px)] overflow-y-auto scrollable2'>
        {multiple()}
      </div>
    </div>
  );
};
