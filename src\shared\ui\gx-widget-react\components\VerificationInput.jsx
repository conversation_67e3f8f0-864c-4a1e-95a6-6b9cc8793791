import React, { useRef, useEffect, useState, forwardRef, useImperativeHandle } from 'react';

/**
 * Verification Code Input Component
 * 
 * @param {Object} props - Component properties
 * @param {string} props.value - Current verification code value
 * @param {Function} props.onChange - Callback when code changes
 * @param {Function} props.onComplete - Callback when complete code is entered
 * @param {boolean} props.isVerified - Whether the code has been verified
 * @param {Function} props.onEdit - Callback when edit button is clicked
 * @param {string} props.className - Additional CSS classes
 * @returns {JSX.Element} - Verification input component
 */
const VerificationInput = forwardRef(({ 
  value = '', 
  onChange,
  onComplete,
  isVerified = false,
  onEdit,
  className = ''
}, ref) => {
  const inputRefs = useRef([]);
  const [isInvalid, setIsInvalid] = useState(false);
  
  // Split the value into individual digits
  const digits = value.split('').concat(Array(4 - value.length).fill(''));
  
  // Focus first input on mount if not verified
  useEffect(() => {
    if (!isVerified && inputRefs.current[0] && !isInvalid) {
      inputRefs.current[0].focus();
    }
  }, [isVerified, isInvalid]);
  
  // Handle digit input
  const handleDigitChange = (index, e) => {
    const newValue = e.target.value;
    
    // Allow only numbers
    if (!/^\d*$/.test(newValue)) {
      return;
    }
    
    // If pasting multiple digits
    if (newValue.length > 1) {
      const pastedValue = newValue.slice(0, 4);
      onChange(pastedValue);
      
      // Focus last input or first empty one
      const focusIndex = Math.min(pastedValue.length, 3);
      setTimeout(() => {
        inputRefs.current[focusIndex]?.focus();
      }, 0);
      
      return;
    }
    
    // Update the digit
    const newDigits = [...digits];
    newDigits[index] = newValue;
    const newCode = newDigits.join('').slice(0, 4);
    onChange(newCode);
    
    // Auto-focus next input if value entered
    if (newValue && index < 3) {
      setTimeout(() => {
        inputRefs.current[index + 1]?.focus();
      }, 0);
    }
    
    // Trigger completion callback if all digits entered
    if (newCode.length === 4) {
      if (onComplete) {
        setTimeout(() => {
          onComplete(newCode);
        }, 100);
      }
    }
  };
  
  // Handle keydown for backspace and navigation
  const handleKeyDown = (index, e) => {
    switch (e.key) {
      case 'Backspace':
        // If current input is empty and not the first one, focus previous
        if (!digits[index] && index > 0) {
          e.preventDefault();
          inputRefs.current[index - 1]?.focus();
        } else if (digits[index] && e.target.selectionStart === 0) {
          // If at beginning of input with content, clear it but don't navigate
          e.preventDefault();
          const newDigits = [...digits];
          newDigits[index] = '';
          onChange(newDigits.join('').slice(0, 4));
        }
        break;
        
      case 'ArrowLeft':
        // Navigate left
        if (index > 0) {
          e.preventDefault();
          inputRefs.current[index - 1]?.focus();
        }
        break;
        
      case 'ArrowRight':
        // Navigate right
        if (index < 3) {
          e.preventDefault();
          inputRefs.current[index + 1]?.focus();
        }
        break;
        
      default:
        break;
    }
  };
  
  // Show invalid animation and clear inputs
  const showInvalidAnimation = () => {
    setIsInvalid(true);
    setTimeout(() => {
      setIsInvalid(false);
      // Clear inputs and focus first one
      onChange('');
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 0);
    }, 800);
  };
  
  // Expose showInvalidAnimation to parent component via ref
  useImperativeHandle(ref, () => ({
    showInvalidAnimation,
    clear: () => {
      onChange('');
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 0);
    },
    focus: () => {
      inputRefs.current[0]?.focus();
    }
  }), [onChange]);

  // Combine CSS classes
  const rootClassName = [
    'gx-verification-input',
    isInvalid && 'invalid',
    className
  ].filter(Boolean).join(' ');
  
  return (
    <div className={rootClassName}>
      {isVerified ? (
        <div className="gx-verification-success">
          <div className="gx-verification-success-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" 
                stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <span className="gx-verification-success-text">Авторизация успешна!</span>
        </div>
      ) : (
        <>
          <div className="gx-verification-digits">
            {Array.from({ length: 4 }).map((_, index) => (
              <input
                key={index}
                ref={el => inputRefs.current[index] = el}
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                maxLength={1}
                value={digits[index] || ''}
                onChange={(e) => handleDigitChange(index, e)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                className="gx-verification-digit-input"
                disabled={isInvalid}
                autoComplete="one-time-code"
              />
            ))}
          </div>
          <p className="gx-verification-hint">Введите 4-значный код, отправленный на ваш телефон</p>
        </>
      )}
    </div>
  );
});

// Add display name for better debugging
VerificationInput.displayName = 'VerificationInput';

export default VerificationInput;
