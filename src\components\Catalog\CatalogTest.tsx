import { Filter } from '@/shared/ui';
import { Breadcrumbs } from '@/widgets/breadcrumbs';
import {
  ProductCard,
  ProductCardMobile, // Added import for mobile version
  ProductCardLine,
  ProductCardLineSkeleton,
  ProductCardSkeleton
} from '@/widgets/product-card';
import StickyBox from 'react-sticky-box';
import { useDevice } from '@/shared/lib/hooks/useDevice'; // Added useDevice import
import { CatalogChipses } from './CatalogChipses';
import CardTypeControls from './CatalogContent/CardTypeControls';
import { CatalogSidebarFilters } from './CatalogSidebarFilters';
import { CatalogSort } from './CatalogSort';
import { useCatalogTest } from './hooks/useCatalogTest';

export const CatalogTest = () => {
  const { isMobile } = useDevice(); // Added useDevice hook call
  const {
    filters,
    onChangeFilter,
    filtersLoading,
    handleOpenModalAllFilters,
    onResetFilters,
    chips,
    currentSort,
    handleSortChange,
    sortBy,
    cardType,
    setTypeCard,
    isLoadingProducts,
    products,
    hasMore,
    loadMoreRef,
    categoryTree,
    prevCategories
  } = useCatalogTest();

  return (
    <div className='content lining-nums proportional-nums p-2'>
      <div className='my-5'>
        <Breadcrumbs />
      </div>

      <div className='flex gap-[20px] items-start'>
        <StickyBox offsetTop={90} offsetBottom={20} className='hidden sm:block'>
          <CatalogSidebarFilters
            prevCategories={prevCategories}
            categories={categoryTree}
            filters={filters}
            onChangeFilter={onChangeFilter}
            filtersLoading={filtersLoading}
            handleOpenModalAllFilters={handleOpenModalAllFilters}
            onResetFilters={onResetFilters}
          />
        </StickyBox>
        <div className='flex flex-col gap-[24px] w-full'>
          <div className='flex flex-col gap-[20px]'>
            <div className='flex justify-between items-center'>
              <CatalogSort
                currentSort={currentSort}
                handleSortChange={handleSortChange}
                sortBy={sortBy}
              />
              <div className='hidden sm:block'>
                <CardTypeControls cardType={cardType} setTypeCard={setTypeCard} />
              </div>
              <button
                className='block sm:hidden p-[8px] rounded-[4px] bg-[#C4E5DB] flex items-center gap-1 text-[#414947]'
                onClick={handleOpenModalAllFilters}
              >
                <Filter size={24} />
                {/* <span className='leading-[14px] text-[14px]'>+2</span> */}
              </button>
            </div>
            <CatalogChipses
              chips={chips}
              filtersLoading={filtersLoading}
              onChangeFilter={onChangeFilter}
              onResetFilters={onResetFilters}
            />
          </div>

          {cardType === 'tile' ? (
            <div className='grid grid-cols-2 mm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 ll:grid-cols-3 gap-5 xl:grid-cols-4'>
              {!!products.length
                ? products.map((el) =>
                    isMobile ? (
                      <ProductCardMobile key={el?.id} product={el} className='' />
                    ) : (
                      <ProductCard key={el?.id} product={el} className='' />
                    )
                  )
                : null}

              {isLoadingProducts
                ? Array.from({ length: 20 }).map((_, index) => <ProductCardSkeleton key={index} />)
                : null}
            </div>
          ) : null}
          {cardType === 'line' ? (
            <div className='space-y-4'>
              {isLoadingProducts
                ? Array.from({ length: 20 }).map((_, index) => (
                    <ProductCardLineSkeleton key={index} />
                  ))
                : null}
              {!!products.length &&
                products.map((el) => <ProductCardLine key={el?.id} product={el} />)}
            </div>
          ) : null}
          {isLoadingProducts && <div className='text-center'>Идет загрузка...</div>}
          {hasMore && <div ref={loadMoreRef} style={{ height: '20px' }} />}
          {!hasMore && <div>Больше нет товаров</div>}
        </div>
      </div>
    </div>
  );
};
