// vite.config.js
import { defineConfig } from "file:///D:/code/furnica/node_modules/vite/dist/node/index.js";
import react from "file:///D:/code/furnica/node_modules/@vitejs/plugin-react/dist/index.mjs";
import { fileURLToPath, URL } from "url";
var __vite_injected_original_import_meta_url = "file:///D:/code/furnica/vite.config.js";
var vite_config_default = defineConfig({
  plugins: [
    react()
    // tsconfigPaths()
  ],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url)),
      "@pages": fileURLToPath(new URL("./src/pages", __vite_injected_original_import_meta_url)),
      "@components": fileURLToPath(new URL("./src/components", __vite_injected_original_import_meta_url)),
      "@context": fileURLToPath(new URL("./src/context", __vite_injected_original_import_meta_url)),
      "@hooks": fileURLToPath(new URL("./src/hooks", __vite_injected_original_import_meta_url)),
      "@store": fileURLToPath(new URL("./src/redux", __vite_injected_original_import_meta_url)),
      "@api": fileURLToPath(new URL("./src/redux/api", __vite_injected_original_import_meta_url)),
      "@helpers": fileURLToPath(new URL("./src/helpers", __vite_injected_original_import_meta_url))
    }
  },
  server: {
    port: 5175
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
