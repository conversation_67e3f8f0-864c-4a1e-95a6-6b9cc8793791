import { useCart } from '@/features/cart';
import { useComparison } from '@/features/comparison';
import { useFavorites } from '@/features/favorite';

export const useQuantities = () => {
  const { favorites } = useFavorites();
  const { comparison } = useComparison();
  const { cart } = useCart();

  const getFavoritesCount = () => {
    return favorites?.length || 0;
  };

  const getComparisonCount = () => {
    return comparison?.length || 0;
  };

  const getCartQuantity = () => {
    // Always calculate the total quantity from cart items to ensure accuracy
    // This prevents issues when items are removed from the cart
    return cart.cart?.reduce((total, item) => total + (item.quantity || 0), 0) || 0;
  };

  return {
    getFavoritesCount,
    getComparisonCount,
    getCartQuantity,
  };
};
