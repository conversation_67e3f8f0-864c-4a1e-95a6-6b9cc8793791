// src/components/ProductPage/Mobile/MobileTopBar.tsx
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ComparisonButton } from '@/features/comparison/';
import { FavoriteButton } from '@/features/favorite/';
import { useModal } from '@/features/modals/model/context';
import { Icon } from '@iconify-icon/react';
// TODO: Define a more specific Product type if available
interface Product {
  id: number | string;
  // Add other necessary product properties
  [key: string]: any; // Allow other properties for now
}

interface MobileTopBarProps {
  product: Product;
}

const MobileTopBar: React.FC<MobileTopBarProps> = ({ product }) => {
  const { showModal } = useModal();
  const navigate = useNavigate();

  return (
    <div className="flex justify-between my-2">
   <div>
   <button
          onClick={() => navigate(-1)}
          className='flex items-center gap-1'
        >
          <div className="icon-btn icon-btn-gray-bg">
            <Icon icon="lucide:chevron-left" width={24} height={24} className="icon-btn-icon"/>
          </div>
          {/* <span className="">
            Поделиться
          </span> */}
        </button>
    
   </div>
   <div className='flex gap-3'>
   <button
          
          onClick={() => showModal({ type: 'share' })}
          className='flex items-center gap-1'
        >
          <div className="icon-btn icon-btn-gray-bg">
          <Icon icon="solar:share-bold" width={24} height={24} className="icon-btn-icon"/>
          </div>
          {/* <span className="">
            Поделиться
          </span> */}
        </button>
        <ComparisonButton product={product} className="icon-btn icon-btn-gray-bg" />
        <FavoriteButton product={product} className="icon-btn icon-btn-gray-bg" />
   </div>

        
    </div>
  );
};

export default MobileTopBar;
