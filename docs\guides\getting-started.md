# Getting Started Guide

This guide provides instructions on how to set up and run the Rostok Frontend project locally for development.

## Prerequisites

Before you begin, ensure you have the following installed on your system:

1.  **Node.js:** Version 16.x or higher is recommended. You can download it from [nodejs.org](https://nodejs.org/). Verify your installation by running `node -v` in your terminal.
2.  **npm (Node Package Manager):** Version 7.x or higher is recommended. npm usually comes bundled with Node.js. Verify your installation by running `npm -v`.

## Setup and Installation

1.  **Clone the Repository:**
    Open your terminal or command prompt and clone the project repository using Git:
    ```bash
    git clone <repository-url>
    # Replace <repository-url> with the actual URL of the Git repository
    ```

2.  **Navigate to Project Directory:**
    Change your current directory to the newly cloned project folder:
    ```bash
    cd <repository-directory>
    # Replace <repository-directory> with the name of the folder created by git clone
    ```

3.  **Install Dependencies:**
    Install all the necessary project dependencies defined in `package.json`:
    ```bash
    npm install
    ```
    This command might take a few minutes to complete as it downloads and installs all required libraries and tools.

## Running the Development Server

1.  **Start the Server:**
    Once dependencies are installed, start the Vite development server:
    ```bash
    npm run dev
    ```

2.  **Access the Application:**
    Vite will typically start the server and print the local URL where the application is running (usually `http://localhost:5175` unless that port is occupied). Open this URL in your web browser.

    The development server includes features like:
    *   **Hot Module Replacement (HMR):** Changes you make to the code will often reflect in the browser instantly without needing a full page reload.
    *   **Development-specific configurations:** May include enhanced logging or debugging tools.

## Available Scripts

Refer to the main [README.md](../../README.md#available-scripts) for a full list of available `npm` scripts and their descriptions (e.g., `build`, `lint`, `format`, `validate`).

## Next Steps

*   Familiarize yourself with the [Project Structure](../../README.md#project-structure-target-state).
*   Review the [Architecture Documentation](../architecture/fsd-overview.md) to understand the FSD principles applied.
*   Check the [Contributing Guidelines](./contributing.md) if you plan to contribute code.