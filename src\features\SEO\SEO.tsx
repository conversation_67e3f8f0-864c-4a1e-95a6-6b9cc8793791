import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useLocation } from 'react-router-dom';

const DEFAULT_TITLE = 'Furnica - Ваш интернет-магазин мебели';
const DEFAULT_DESCRIPTION = 'Широкий ассортимент мебели по выгодным ценам';

// API base URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

interface RouteInfoResponse {
  meta: {
    title?: string;
    description?: string;
    h1?: string;
    'og:title'?: string;
    'og:description'?: string;
    'og:image'?: string;
    [key: string]: string | undefined;
  };
}

export function SEO() {
  const [routeInfo, setRouteInfo] = useState<RouteInfoResponse | null>(null);
  const location = useLocation();

  useEffect(() => {
    const path = location.pathname + location.search;

    // Skip API call for home page or specific routes if needed
    if (path === '/' || path.startsWith('/_next') || path.includes('.')) {
      setRouteInfo({
        meta: {
          title: 'Furnica - ' + (path === '/' ? 'Главная' : path),
          description: 'Широкий ассортимент мебели по выгодным ценам'
        }
      });
      return;
    }

    fetch(`${API_BASE_URL}/route/info?url=${encodeURIComponent(path)}`, {
      credentials: 'include'
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then((data) => {
        setRouteInfo(data);
      })
      .catch((error) => {
        console.error('[SEO] Error fetching route info:', error);
        setRouteInfo({
          meta: {
            title: 'Furnica - ' + path,
            description: 'Широкий ассортимент мебели по выгодным ценам'
          }
        });
      });
  }, [location.pathname, location.search]);

  const meta = routeInfo?.meta || {};
  const title = meta.title || DEFAULT_TITLE;
  const description = meta.description || DEFAULT_DESCRIPTION;
  const ogTitle = meta['og:title'] || title;
  const ogDescription = meta['og:description'] || description;
  const ogImage = meta['og:image'];
  const h1 = meta.h1;

  return (
    <Helmet>
      <title>{title}</title>
      <meta name='description' content={description} />
      <meta property='og:title' content={ogTitle} />
      <meta property='og:description' content={ogDescription} />
      <meta property='og:type' content='website' />
      {ogImage && <meta property='og:image' content={ogImage} />}
      <meta name='robots' content='index, follow' />
      {h1 && <meta name='h1' content={h1} />}
    </Helmet>
  );
}
