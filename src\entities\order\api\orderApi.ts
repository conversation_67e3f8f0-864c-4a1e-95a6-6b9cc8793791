import { useCreateOrderMutation } from '@/entities/order';
import { api } from '@/shared/api/api';

import type {
  GetUserOrdersResponse,
  OrderRequest,
  FeedbackRequest,
  FeedbackResponse,
  OrderPaymentRequest,
  OrderPaymentResponse,
  CheckoutItemDelRequest,
  CheckoutItemReturnCardRequest,
  GetOrderResponse,
  OrderDetailsData,
} from './types';
import type {
  GetPickupPointsRequest,
  GetPickupPointsResponse,
  AssignPickupPointRequest,
  AssignPickupPointResponse
} from './pickupPointsTypes';

export interface ErrorResponseProps {
  data?: {
    err_code?: string;
  };
}

export interface OrderResponseErrorProps {
  text: string;
  error: string;
}

export interface OrderResponseBillProps {
  success: string;
  link: string;
  bill: string;
  link_opening_location: string;
  internal_bill: string;
}

export interface OrderResponseProps {
  success: boolean | string;
  numbers?: string[];
  errors?: OrderResponseErrorProps[];
  total_request_time: number;
  api_processing_time: number;
  sessid: string;
}

export interface OrderRequest {
  // Add properties for OrderRequest
}

export interface OrderPaymentRequest {
  order_number: string[];
  payment_id: number;
}

// --- Invoice Payment Types ---
/**
 * @description Функция инициирует запрос на создание счета для оплаты заказа.
 * @url /api/ProductOrders/billing/invoices/create
 * @apiArray par
 *   order_number (text): Номер заказа
 */
export interface InvoicePaymentRequest {
  order_number: string[];
}

export interface InvoicePaymentResponse {
  success: boolean;
  data?: {
    file?: {
      name: string;
      mime_type: string;
      url: string;
      size: number;
    };
    [key: string]: any;
  };
  total_request_time?: number;
  api_processing_time?: number;
  sessid?: string;
}


export interface FeedbackRequest {
  // Add properties for FeedbackRequest
}

export interface FeedbackResponse {
  // Add properties for FeedbackResponse
}

export const orderApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getPickupPoints: builder.query<GetPickupPointsResponse, GetPickupPointsRequest>({
      query: (data) => ({
        url: '/api/ProductOrders/pickup/points',
        method: 'POST',
        body: data,
      }),
    }),
    getCitiesAndRegions: builder.query({
      query: () => '/api/Location/full',
      staleTime: 60000,
    }),
    createOrder: builder.mutation<OrderResponseProps, OrderRequest>({
      query: () => ({
        url: '/api/ProductOrders/checkout',
        method: 'POST',
      }),
      invalidatesTags: (result) => {
        if (result?.success === 'ok') {
          return [{ type: 'Order', d: 'LIST' }];
        }
        return [];
      },
    }),
    getOrder: builder.query<GetOrderResponse, { number: string | undefined }>({
      query: ({ number }) => ({
        url: '/api/ProductOrders/getOrder',
        method: 'POST',
        body: { number },
      }),
      providesTags: (result: GetOrderResponse | undefined, error, { number }) =>
        result ? [{ type: 'Order', id: result.data.order_number }] : [],
    }),
    getOrderPDF: builder.mutation({
      query: (data) => ({
        url: '/api/ProductOrders/getOrderPDF',
        method: 'POST',
        body: data,
      }),
    }),
    orderPayment: builder.mutation<OrderPaymentResponse, OrderPaymentRequest>({
      query: (payload) => ({
        url: '/api/ProductOrders/checkoutPayment',
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: (result, error, arg) =>
        arg.order_number.map((num) => ({ type: 'Order', id: num as string })),
    }),
    /**
     * @description Функция инициирует запрос на создание счета для оплаты заказа.
     * @url /api/ProductOrders/billing/invoices/create
     * @apiArray par
     *   order_number (text): Номер заказа
     */
    createInvoicePayment: builder.mutation<InvoicePaymentResponse, InvoicePaymentRequest>({
      query: (payload) => ({
        url: '/api/ProductOrders/billing/invoices/create',
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: (result, error, { order_number }) => [{ type: 'Order', id: order_number }],


    }),
    cancelOrder: builder.mutation<
      { success: string },
      { order_number: string; reason: string }
    >({
      query: (data) => ({
        url: '/api/ProductOrders/cancel/order',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        { type: 'Order', id: 'LIST' },
        { type: 'Order', id: 'FILTERS' },
      ],
    }),
    getUserOrders: builder.query<GetUserOrdersResponse, { page?: number; limit?: number }>({
      query: ({ page = 1, limit = 10 } = {}) => ({
        url: '/api/ProductOrders/get/orders',
        params: { page, limit },
      }),
      serializeQueryArgs: ({ endpointName }) => endpointName,
      merge: (currentCache, newItems) => {
        if (newItems.page === 1) {
          return newItems;
        }
        return {
          ...newItems,
          data: [...(currentCache?.data || []), ...(newItems.data || [])],
        };
      },
      forceRefetch({ currentArg, previousArg }) {
        return currentArg?.page !== previousArg?.page;
      },
      providesTags: (result) =>
        result?.data
          ? [
              ...result.data.map(({ order_number }) => ({ type: 'Order' as const, id: order_number })),
              { type: 'Order', id: 'LIST' },
            ]
          : [{ type: 'Order', id: 'LIST' }],
    }),
    getOrdersFilters: builder.query({
      query: () => '/api/Products/orders/filters',
      providesTags: (result) =>
        result
          ? [{ type: 'Order', id: 'FILTERS' }]
          : [{ type: 'Order', id: 'FILTERS' }],
      refetchOnMountOrArgChange: true,
    }),
    sendFeedback: builder.mutation<FeedbackResponse, FeedbackRequest>({
      query: (feedback: FeedbackRequest) => ({
        url: '/api/Products/feedback',
        method: 'POST',
        body: feedback,
      }),
    }),
    repeatOrder: builder.mutation<
      { success: string; new_order_number: string },
      { order_number: string }
    >({
      query: (data) => ({
        url: '/api/ProductOrders/repeat/order',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [
        { type: 'Order', id: 'LIST' },
        { type: 'Order', id: 'FILTERS' },
        { type: 'Cart', id: 'LIST' },
      ],
    }),
    checkoutItemDel: builder.mutation<void, CheckoutItemDelRequest>({
      query: (payload) => ({
        url: '/api/ProductOrders/checkoutItemDel',
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: (result, error, { order_number }) => [{ type: 'Order', id: order_number }],
    }),
    checkoutItemReturnCard: builder.mutation<void, CheckoutItemReturnCardRequest>({
      query: (payload) => ({
        url: '/api/ProductOrders/checkoutItemReturnCard',
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: (result, error, { order_number }) => [
        { type: 'Order', id: order_number },
        { type: 'Cart', id: 'LIST' },
      ],
    }),
    addFeedback: builder.mutation<any, FeedbackRequest>({
      query: (data) => ({
        url: '/api/feedback',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'Feedback', id: 'LIST' }],
    }),
    assignPickupPoint: builder.mutation<AssignPickupPointResponse, AssignPickupPointRequest>({
      query: (payload) => ({
        url: '/api/ProductOrders/pickup/assign',
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: (result, error, { order_number }) => [{ type: 'Order', id: order_number }],
    }),
  }),
});

export const {
  useGetPickupPointsQuery,
  useGetCitiesAndRegionsQuery,
  useCreateOrderMutation,
  useGetOrderQuery,
  useGetOrderPDFMutation,
  useOrderPaymentMutation,
  useCreateInvoicePaymentMutation,
  useCancelOrderMutation,
  useGetUserOrdersQuery, // Ensure this is exported
  useGetOrdersFiltersQuery,
  useSendFeedbackMutation,
  useRepeatOrderMutation,
  useCheckoutItemDelMutation,
  useCheckoutItemReturnCardMutation,
  useAssignPickupPointMutation,
} = orderApi;
