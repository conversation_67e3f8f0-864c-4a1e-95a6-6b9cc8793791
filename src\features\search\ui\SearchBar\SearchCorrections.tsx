import { memo } from 'react';
import type { SearchCorrectionItem } from '../../api/types';

export interface SearchCorrectionsProps {
  corrections: SearchCorrectionItem[];
  onSelect: (item: SearchCorrectionItem) => void;
  className?: string;
  maxItems?: number;
}

/**
 * Displays search corrections/suggestions as clickable badges
 */
export const SearchCorrections = memo(({ 
  corrections, 
  onSelect,
  className = '',
  maxItems = 10,
}: SearchCorrectionsProps) => {
  // Early return if there are no corrections
  if (!corrections || corrections.length === 0) {
    return null;
  }

  // Limit the number of displayed corrections
  const displayCorrections = corrections.slice(0, maxItems);

  return (
    <div className={`suggestions-wrapper фывфывфы flex flex-wrap gap-1`}>
      {displayCorrections.map((item, index) => (
        <span
          key={`correction-${index}-${item.word}`}
          className={`suggestion-badge inline-flex px-2 py-1 rounded-md text-sm cursor-pointer lining-nums proportional-nums
            ${item.action === 'replace' 
              ? 'bg-colGreen bg-opacity-80 text-white ' 
              : 'bg-colGreen bg-opacity-90 text-white'
            }`}
          onClick={() => onSelect(item)}
          data-action={item.action}
        >
          {item.word}
        </span>
      ))}
    </div>
  );
});

SearchCorrections.displayName = 'SearchCorrections';
