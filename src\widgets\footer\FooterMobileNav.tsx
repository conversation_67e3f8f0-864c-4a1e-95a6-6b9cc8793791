// src/widgets/footer/FooterMobileNav.tsx (Temporary location)
import React, { useState } from 'react';
import { NavLink } from 'react-router-dom';
import arrowDown from '@/shared/assets/icons/arrow-black.svg';

interface MenusState {
  about: boolean;
  buyer: boolean;
  info: boolean;
}

export const FooterMobileNav: React.FC = () => {
  const [menus, setMenus] = useState<MenusState>({
    about: false,
    buyer: false,
    info: false,
  });

  const toggleMenu = (menuName: keyof MenusState) => {
    setMenus((prevMenus) => ({
      ...prevMenus,
      [menuName]: !prevMenus[menuName],
    }));
  };

  return (
    <div className="md:hidden pt-5">
      <div
        onClick={() => toggleMenu('about')}
        className="flex justify-between items-center pb-1 border-b border-colGray cursor-pointer"
      >
        <p
          className={`${menus.about ? 'text-colBlack' : 'text-colDarkGray'} font-medium`}
        >
          О компании
        </p>
        <img
          className={`${menus.about ? 'rotate-[180deg]' : 'rotate-[0deg]'} transition-transform duration-300`}
          src={arrowDown}
          alt="toggle menu"
        />
      </div>
      {menus?.about ? (
        <ul className="pl-5 py-1 border-b border-colGray">
          <li>
            <NavLink
              className="block py-1 text-colBlack font-semibold text-sm hover:text-colGreen"
              to="/about"
            >
              О компании
            </NavLink>
          </li>
          <li>
            <NavLink
              className="block py-1 text-colBlack font-semibold text-sm hover:text-colGreen"
              to="/contacts"
            >
              Контакты
            </NavLink>
          </li>
          <li>
            <NavLink
              className="block py-1 text-colBlack font-semibold text-sm hover:text-colGreen"
              to="/contacts" // Assuming rekvizity is part of contacts
            >
              Реквизиты
            </NavLink>
          </li>
          <li>
            <NavLink
              className="block py-1 text-colBlack font-semibold text-sm hover:text-colGreen"
              to="/contacts" // Assuming for partners is part of contacts or a dedicated page
            >
              Для партнёров
            </NavLink>
          </li>
        </ul>
      ) : null}

      <div
        onClick={() => toggleMenu('buyer')}
        className="flex justify-between items-center pb-1 border-b border-colGray cursor-pointer pt-2" // Added pt-2 for spacing
      >
        <p
          className={`${menus.buyer ? 'text-colBlack' : 'text-colDarkGray'} font-medium`}
        >
          Покупателю
        </p>
        <img
          className={`${menus.buyer ? 'rotate-[180deg]' : 'rotate-[0deg]'} transition-transform duration-300`}
          src={arrowDown}
          alt="toggle menu"
        />
      </div>
      {menus?.buyer ? (
        <ul className="pl-5 py-1 border-b border-colGray">
          <li>
            <NavLink
              className="block py-1 text-colBlack font-semibold text-sm hover:text-colGreen"
              to="/catalog" // Changed from @ to /catalog
            >
              Каталог
            </NavLink>
          </li>
          <li>
            <NavLink
              className="block py-1 text-colBlack font-semibold text-sm hover:text-colGreen"
              to="/promotions" // Changed from # to /promotions or similar
            >
              Акции
            </NavLink>
          </li>
          <li>
            <NavLink
              className="block py-1 text-colBlack font-semibold text-sm hover:text-colGreen"
              to="/sales" // Changed from # to /sales or similar
            >
              Товары со скидкой
            </NavLink>
          </li>
          <li>
            <NavLink
              className="block py-1 text-colBlack font-semibold text-sm hover:text-colGreen"
              to="/new-arrivals" // Changed from # to /new-arrivals or similar
            >
              Новинки
            </NavLink>
          </li>
        </ul>
      ) : null}

      <div
        onClick={() => toggleMenu('info')}
        className="flex justify-between items-center pb-1 border-b border-colGray cursor-pointer pt-2" // Added pt-2 for spacing
      >
        <p
          className={`${menus.info ? 'text-colBlack' : 'text-colDarkGray'} font-medium`}
        >
          Информация
        </p>
        <img
          className={`${menus.info ? 'rotate-[180deg]' : 'rotate-[0deg]'} transition-transform duration-300`}
          src={arrowDown}
          alt="toggle menu"
        />
      </div>
      {menus?.info ? (
        <ul className="pl-5 py-1 border-b border-colGray">
          <li>
            <NavLink
              className="block py-1 text-colBlack font-semibold text-sm hover:text-colGreen"
              to="/faq" // Changed from # to /faq
            >
              Вопрос-ответ
            </NavLink>
          </li>
          <li>
            <NavLink
              className="block py-1 text-colBlack font-semibold text-sm hover:text-colGreen"
              to="/payment-delivery" // Changed from # to /payment-delivery or similar
            >
              Оплата и доставка
            </NavLink>
          </li>
          <li>
            <NavLink
              className="block py-1 text-colBlack font-semibold text-sm hover:text-colGreen"
              to="/warranty-exchange" // Changed from # to /warranty-exchange or similar
            >
              Гарантия и обмен
            </NavLink>
          </li>
        </ul>
      ) : null}
    </div>
  );
};
