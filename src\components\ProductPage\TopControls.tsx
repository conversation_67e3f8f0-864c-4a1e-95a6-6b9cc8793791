import { ComparisonButton } from '@/features/comparison/';
import { FavoriteButton } from '@/features/favorite/';
import { useModal } from '@/features/modals/model/context';
import star from '@/shared/assets/icons/adv1fill.svg';
import share from '@/shared/assets/icons/share.svg';
import { Icon } from '@iconify-icon/react';

const TopControls = ({ product, reviews }) => {
  const { showModal } = useModal();

  return (
    <div className="flex justify-between my-5">
      <div className="flex gap-[10px]">
        <button className="flex items-center gap-1 proportional-nums  lining-nums">
        <div className="icon-btn icon-btn-gray-bg">
          <Icon icon="solar:star-bold" width={24} height={24} className="icon-btn-icon"/>
          </div>
          <span className="">
            {reviews.raiting}
          </span>
          <span className="">
            {reviews.total_count_text}
          </span>
        </button>

        <ComparisonButton product={product} className="icon-btn icon-btn-gray-bg" />
        <FavoriteButton product={product} className="icon-btn icon-btn-gray-bg" />

        <button
          
          onClick={() => showModal({ type: 'share' })}
          className='flex items-center gap-1'
        >
          <div className="icon-btn icon-btn-gray-bg">
          <Icon icon="solar:share-bold" width={24} height={24} className="icon-btn-icon"/>
          </div>
          {/* <span className="">
            Поделиться
          </span> */}
        </button>
      </div>

      {/* <div className="flex gap-[10px]">
        <button className="text-center flex flex-row justify-between items-center">
          <img className="mx-auto mr-1" src={downloadpdf} alt="*" />
          <span className="text-xs pt-1 font-medium text-colBlack">
            Скачать PDF
          </span>
        </button>
        <button className="text-center flex flex-row justify-between items-center">
          <img className="mx-auto mr-1" src={print} alt="*" />
          <span className="text-xs pt-1 font-medium text-colBlack">
            Распечатать
          </span>
        </button>
      </div> */}
    </div>
  );
};

export default TopControls;
