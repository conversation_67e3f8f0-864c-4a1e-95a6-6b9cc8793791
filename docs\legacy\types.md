# Legacy Documentation: `/src/types/` (Root Level)

This directory contains various TypeScript type definitions that are currently global but should be co-located with the FSD slice (layer/feature/entity/widget) they primarily relate to.

## Overview

Having types defined globally in `src/types/` can lead to poor discoverability and potential coupling issues. The FSD approach encourages placing types close to the code that uses or defines them.

## Files & Refactoring Targets

*(Based on the file list, specific analysis is needed, but here's the general approach)*

1.  **`Filters/FiltersState.ts`**
    *   **Purpose:** Defines the shape of the filter state object.
    *   **Target FSD:** Belongs to the `filter` entity. Move to `src/entities/filter/model/types.ts`.

2.  **`ServerData/` Subdirectory**
    *   Contains various files defining the shape of data received directly from specific API endpoints (e.g., `GetCartShareLink.ts`, `GetTransactionList.ts`, `GetWalletInfo.ts`, `PageContent/...`).
    *   **Target FSD:** These types should generally reside within the `api/types.ts` file of the corresponding entity or feature slice that defines the API endpoint.
        *   `GetCartShareLink.ts` -> `src/features/cart-share/api/types.ts`
        *   `GetTransactionList.ts`, `GetWalletInfo.ts` -> `src/entities/wallet/api/types.ts` (after creating the entity)
        *   `PageContent/...` (e.g., `GetContactsResponse.ts`, `GetFaqResponse.ts`) -> `src/entities/{contentEntityName}/api/types.ts` (e.g., `src/entities/contact/api/types.ts`)

3.  **Other Potential Files (If any)**
    *   Analyze any other type files in `src/types/`.
    *   If a type defines a core business model -> `src/entities/{entityName}/model/types.ts`.
    *   If a type is specific to a feature's internal state or logic -> `src/features/{featureName}/model/types.ts`.
    *   If a type is used across multiple, unrelated parts of the application and is not tied to a specific entity -> `src/shared/types/`.

## Refactoring Process

*   Identify the primary consumer or definition point for each type.
*   Move the type definition to the appropriate `types.ts` file within the target FSD slice and segment (`model/` or `api/`).
*   Update all import statements across the project that referenced the old location in `src/types/`.
*   Remove the original file from `src/types/` once all its contents have been moved and imports are updated.
*   Ensure the `index.ts` file for the target slice exports the moved type if it's part of the slice's public API.