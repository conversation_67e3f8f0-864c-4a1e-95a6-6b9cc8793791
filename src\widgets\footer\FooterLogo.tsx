// src/widgets/footer/FooterLogo.tsx (Temporary location)
import React from 'react';
import { NavLink } from 'react-router-dom';
// import logo from '@/shared/assets/images/logo.svg'; // Assuming logo might be used later

export const FooterLogo: React.FC = () => {
  return (
    <div className="mm:max-w-[220px] w-full">
      <NavLink to="/">
        {/* <img className="w-[220px] mm:w-full" src={logo} alt="Furnica Logo" /> */}
        <span className="text-3xl font-bold text-colGreen">FURNICA</span>
      </NavLink>
      <p className="text-colDarkGray text-xs pt-2">
        Интернет-магазин мебельной фурнитуры, столешниц и плитных
        материалов
      </p>
    </div>
  );
};
