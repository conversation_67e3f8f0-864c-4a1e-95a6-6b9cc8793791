import type React from 'react';
import { NavLink } from 'react-router-dom';
import { FloatingPortal } from '@floating-ui/react';
import type { FloatingProps } from '@floating-ui/react';

import { useCart } from '@/features/cart';
import { ProductCardPopover } from '@/widgets/product-card';
import { useEffect } from 'react';

type CartPopoverProps = {
  isOpen: boolean;
  refs: {
    setFloating: (node: HTMLElement | null) => void;
  };
  getFloatingProps: () => Record<string, unknown>;
  floatingStyles: React.CSSProperties;
};

export const CartPopover: React.FC<CartPopoverProps> = ({
  isOpen,
  refs,
  getFloatingProps,
  floatingStyles,
}) => {
  // Use the cart hook directly
  const { cart, isLoading, refreshFromServer } = useCart();

  // Refresh cart data when the popover opens
  useEffect(() => {
    if (isOpen) {
      refreshFromServer();
    }
  }, [isOpen, refreshFromServer]);

  // Don't render anything if the popover is closed or cart is empty
  if (!isOpen || !cart?.cart?.length) {
    return null;
  }

  return (
    <FloatingPortal>
      <div
        ref={refs.setFloating}
        {...getFloatingProps()}
        style={{ ...floatingStyles }}
        className="max-w-[100vw] flex flex-col gap-2 max-h-[500px] border bg-white p-5 border-colLightGray rounded-[10px] z-50 proportional-nums lining-nums"
      >
        <div className="font-semibold">Товары в корзине</div>
        <div className="flex flex-col gap-2 overflow-y-auto scrollable">
          {cart.cart.map((product) => (
            <NavLink
              key={product.id}
              to={`/catalog/${product.category.slug}/${product.slug}`}
            >
              <ProductCardPopover product={product} />
            </NavLink>
          ))}
        </div>
        <div></div>
        <div className="flex justify-between">
          <span className="font-semibold">Итого:</span>
          <span className="font-semibold">
            {cart?.total?.price_after_discount} {cart?.currency?.symbol}
          </span>
        </div>
        <NavLink
          to="/shopping-cart"
          className="flex items-center justify-center bg-colGreen p-3 rounded-sm"
        >
          <span className="font-semibold text-white">В корзину</span>
        </NavLink>
      </div>
    </FloatingPortal>
  );
};

// Also export as default for backward compatibility
export default CartPopover;
