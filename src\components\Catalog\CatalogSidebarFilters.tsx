import { ProductFilter } from '@/features/product-filter';
import { TreeView } from '@/shared/ui';
import { PrevTreeNode, TreeNode } from '@/types/CategorieTree';

interface CatalogSidebarFilterProps {
  filters: any;
  onChangeFilter: (data: any) => void;
  filtersLoading: boolean;
  handleOpenModalAllFilters: (data: any) => void;
  onResetFilters: () => void;
  categories?: TreeNode;
  prevCategories?: PrevTreeNode[];
}

export const CatalogSidebarFilters: React.FC<CatalogSidebarFilterProps> = ({
  filters,
  filtersLoading,
  handleOpenModalAllFilters,
  onChangeFilter,
  onResetFilters,
  categories,
  prevCategories
}) => {
  return (
    <div
      className={`w-[250px] shrink-[0] p-[13px] border border-[#f5f5f5] rounded-[16px] shadow-sm `}
    >
      {!!categories && (
        <div className='border-b-2 border-color-[#f1f1f1] py-[12px]'>
          <TreeView prevTree={prevCategories} tree={categories} />
        </div>
      )}
      {filters.map((filter) => (
        <div
          key={filter.id}
          className={`border-b-2 border-color-[#f1f1f1] py-[12px] px-[6px] will-change-[height]`}
        >
          <ProductFilter data={filter} onChange={onChangeFilter} disabled={filtersLoading} />
        </div>
      ))}
      <button
        className='w-full py-2 px-3 border border-colGreen mt-3 rounded-[4px] text-[#222] font-semibold'
        onClick={handleOpenModalAllFilters}
      >
        Все фильтры
      </button>
      <button
        className='w-full py-2 px-3 border border-transparent mt-3 rounded-[4px] text-[#727272] hover:text-[#222] font-semibold'
        onClick={onResetFilters}
      >
        Сбросить фильтр
      </button>
    </div>
  );
};
