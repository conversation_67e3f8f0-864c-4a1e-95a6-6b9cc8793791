# Cross-Domain Authentication Handling

*Last updated: 2025-04-11 11:48 UTC*

## Overview

This document explains how the application handles authentication across different domains, particularly focusing on the differences between development and production environments.

## The Cross-Domain Cookie Challenge

### Understanding the Problem

A fundamental security feature of web browsers is the [Same-Origin Policy](https://developer.mozilla.org/en-US/docs/Web/Security/Same-origin_policy), which prevents JavaScript code from accessing cookies that belong to different domains. This creates a specific challenge in our application:

1. **Production Environment**: The application and API are on the same domain (`rosstok.ru`), so JavaScript can directly read authentication cookies
2. **Development Environment**: The application runs on `localhost`, but the API and authentication tokens are on `rosstok.ru`

When a user logs in via the GX Phone widget, the widget sets a `gx_session` cookie on the `rosstok.ru` domain. In development (localhost), JavaScript **cannot directly read** this cookie due to cross-domain restrictions.

### Our Solution

We've implemented a two-stage authentication detection process:

1. **Direct Cookie Detection**:
   - First attempt to read cookies directly using `getTokenFromCookies()`
   - This works in production but fails in development due to cross-domain restrictions

2. **API-Based Authentication Detection** (Development Only):
   - If direct cookie detection fails in development, make a request to the API (`/api/UserData/get`)
   - Include credentials with the request (`credentials: 'include'`)
   - The browser will automatically include the `gx_session` cookie with the request
   - If the API returns a successful user response, we know the user is authenticated

## Implementation Details

### Authentication Initialization Flow

The implementation is in `InitializationProvider.tsx`:

```typescript
// Step 1: Try direct cookie-based auth
useEffect(() => {
  // Initialize auth from cookies if possible
  if (!isInitialized) {
    dispatch(initializeAuthAsync());
  }
}, [dispatch, isInitialized]);

// Step 2: If that fails in development, try API-based detection
useEffect(() => {
  if (isDevelopment && isInitialized && !isAuthenticated && !apiAuthCheckDone) {
    // Make authenticated request to API with credentials
    fetch('https://rosstok.ru/api/UserData/get', { credentials: 'include' })
      .then(response => response.json())
      .then(data => {
        if (data.success && data.user) {
          // User is authenticated via API cookies
          dispatch(setToken({ token: 'valid-session-exists', source: 'gx' }));
          dispatch(setUserData(data.user));
        }
      });
  }
}, [dispatch, isInitialized, isAuthenticated, apiAuthCheckDone, isDevelopment]);
```

### Key Technical Aspects

1. **Detecting Development Environment**:
   ```typescript
   const isDevelopment = window.location.hostname === 'localhost' || 
                         window.location.hostname.includes('127.0.0.1');
   ```

2. **Including Credentials in API Requests**:
   ```typescript
   fetch('https://rosstok.ru/api/UserData/get', {
     credentials: 'include'  // This sends cookies for rosstok.ru
   })
   ```

3. **Token Representation**:
   - Since we can't directly access the GX session token in development, we use a placeholder value ('valid-session-exists') to represent the authenticated state in Redux
   - Actual API requests will still include the real token cookie automatically

## Best Practices

1. **API Configuration**:
   - Always use `credentials: 'include'` in API configurations (as done in `src/shared/api/api.ts`)
   - This ensures cookies are sent with cross-domain requests

2. **Authentication Detection**:
   - Always try direct cookie access first (better performance)
   - Fall back to API-based detection only in development

3. **User Experience**:
   - Keep the loading state active until both authentication methods have been tried
   - Provide smooth transitions between unauthenticated and authenticated states

## Security Considerations

1. The cross-domain authentication method is only used in development environments
2. In production, all traffic is on the same domain, using standard cookie security
3. Use the standard security headers in production (Secure, HttpOnly, SameSite)
4. Consider implementing CSRF protection if not already present

## Troubleshooting

If authentication doesn't work in development:

1. Check that the GX session cookie is present on the rosstok.ru domain
2. Verify that `credentials: 'include'` is properly set in API requests
3. Ensure network requests to the API are completing successfully
4. Check console for any CORS-related errors (might need server configuration)
