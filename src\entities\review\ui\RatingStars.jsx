import React, { useEffect, useState } from 'react';

import starfill from '@/shared/assets/icons/adv1fill.svg';
import star from '@/shared/assets/icons/adv1grey.svg';
import starhalf from '@/shared/assets/icons/adv1half.svg';

export const RatingStars = ({
  totalStars,
  initialRating,
  isActive = false,
  handleSetRating,
}) => {
  const [rating, setRating] = useState(initialRating || 0);

  useEffect(() => {
    setRating(initialRating || 0);
  }, [initialRating]);

  const handleStarClick = (selectedRating) => {
    if (isActive) {
      setRating(selectedRating);
      handleSetRating?.(selectedRating);
    }
  };

  return (
    <div className="flex gap-0.5">
      {[...Array(parseFloat(totalStars))].map((_, index) => {
        const starValue = index + 1;
        const currentRating = Number(rating);
        const hasHalf = currentRating % 1 !== 0;
        const fullStars = Math.floor(currentRating);

        let starImage = star; // default empty star
        if (starValue <= fullStars) {
          starImage = starfill; // full star
        } else if (hasHalf && starValue === fullStars + 1) {
          starImage = starhalf; // half star
        }

        return (
          <img
            className="h-5 w-5"
            key={index}
            src={starImage}
            alt={`Star ${index + 1}`}
            onClick={() => handleStarClick(starValue)}
            style={{ cursor: isActive ? 'pointer' : 'default' }}
          />
        );
      })}
    </div>
  );
};

export default RatingStars;
