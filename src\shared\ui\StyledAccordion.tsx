import { styled } from '@mui/material/styles';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';

export const StyledAccordion = styled(Accordion)(({ theme }) => ({
    padding: 1,
    dropShadow: 'none',
    boxShadow: 'none',
  '&:before': {
    display: 'none',
  },
  '&.Mui-expanded': {
    'margin': 0,
  },
}));

export const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  borderRadius: '8px',
  backgroundColor: 'lightgrey',
  '&.Mui-expanded': {
    borderRadius: '8px 8px 0 0',
  },
}));

export const StyledAccordionDetails = styled(AccordionDetails)(({ theme }) => ({
  borderRadius: '0 0 8px 8px',
}));
