# Widget: Breadcrumbs

## Purpose

This widget displays the navigational breadcrumb trail, helping users understand their current location within the application hierarchy and providing quick links back to parent pages or sections.

## Composition

*   **Entities:** Primarily uses category data (`CategoryBase`, `Category<PERSON>hain`) from the `category` entity when displaying catalog paths.
*   **Routing:** Relies heavily on `react-router-dom` hooks (`useLocation`, `useParams`) to determine the current path and relevant parameters.
*   **Shared UI:** Uses `NavLink` for links and basic layout/styling utilities.

## Key UI Components (`ui/`)

*   **`Breadcrumbs.tsx`:** The main widget component. It determines the correct breadcrumb path based on the current URL and potentially fetched data (like category chains).
*   **`BreadcrumbsItem.tsx`:** Renders a single breadcrumb link or text item, including the separator (if not the last item). Handles active state styling.
*   **`BreadcrumbsSkeleton.tsx`:** A skeleton loader component displayed while category data (if needed) is being fetched.

## Logic & State (`lib/`)

*   **`breadcrumb.ts`:** Defines the `Breadcrumb` interface (shape: `{ name: string, slug: string }`).
*   **`constants.ts`:** Contains mappings from route segments to display names (e.g., `ROUTE_NAMES`).
*   **`mappers/category.ts`:** Contains helper functions (`mapCategoryChainToBreadcrumbs`) to transform category data fetched from the API into the `Breadcrumb` interface format required by the UI.
*   **Path Generation Logic (within `Breadcrumbs.tsx`):** The main component contains the logic (`generateBreadcrumbs`) to:
    *   Always start with "Главная".
    *   Parse the current `pathname`.
    *   If on a catalog page (`/catalog/...`), fetch the category tree (`useGetCategoryTreeQuery`) and use the `category_chain` data to build the breadcrumbs.
    *   If on other known pages (profile, cart, etc.), use the `ROUTE_NAMES` constant to build the trail.
    *   Handle fallback cases.

## Usage

*   The `Breadcrumbs` widget is typically included near the top of the main content area in the `Layout` component or directly within individual page components.

```tsx
// Example in a Page component
import { Breadcrumbs } from '@/widgets/breadcrumbs';

const MyPage = () => {
  return (
    <div className="content">
      <Breadcrumbs />
      {/* ... rest of the page content */}
    </div>
  );
};
```

## Key Considerations

*   **Data Dependency:** For catalog pages, the breadcrumbs depend on data fetched via `useGetCategoryTreeQuery`. The `BreadcrumbsSkeleton` handles the loading state.
*   **Route Mapping:** Relies on the `ROUTE_NAMES` constant being kept up-to-date with major application sections.
*   **Dynamic Segments:** Correctly handles dynamic segments like `:categoryId` by fetching corresponding data.
